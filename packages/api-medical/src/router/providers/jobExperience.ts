import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { <PERSON>risma } from "@axa/database-medical";
import { calculateSkip } from "@axa/lib/utils";

import type { ActionData } from "../../constants/actions";
import type { ProcedureResult } from "../../types/select";

import { ActionType, ResourceType } from "../../constants/actions";
import { performAction } from "../../lib/actions";
import { createTRPCRouter, protectedProcedure } from "../../trpc";

// Core schema (direct fields)
const zCoreJobExperienceSchema = z.object({
  role: z.string(),
  description: z.string().optional(),
  company: z.string().optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  specialties: z.array(z.string()).optional(),
});

// Extended schema with relations
const zJobExperienceSchema = zCoreJobExperienceSchema.extend({
  providerId: z.string(),
  facilityId: z.string().optional(),
  organizationId: z.string().optional(),
  departmentId: z.string().optional(),
});

// Schema for controlling nested selections
const zIncludeSchema = z.object({
  provider: z.boolean().default(false),
  person: z.boolean().default(false),
  organization: z.boolean().default(false),
  facility: z.boolean().default(false),
  department: z.boolean().default(false),
  specialties: z.boolean().default(false),
  deleted: z.boolean().default(false),
});

// Standard selections for all related data
const selection = {
  jobExperience: {
    id: true,
    role: true,
    description: true,
    company: true,
    startDate: true,
    endDate: true,
    providerId: true,
    facilityId: true,
    organizationId: true,
    departmentId: true,
  },
  provider: {
    id: true,
    status: true,
    title: true,
  },
  person: {
    id: true,
    firstName: true,
    lastName: true,
    avatar: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  facility: {
    id: true,
    name: true,
    type: true,
  },
  department: {
    id: true,
    name: true,
    type: true,
  },
  specialty: {
    id: true,
    name: true,
  },
} satisfies {
  jobExperience: Prisma.JobExperienceSelect;
  provider: Prisma.ProviderSelect;
  person: Prisma.PersonSelect;
  organization: Prisma.OrganizationSelect;
  facility: Prisma.LocationSelect;
  department: Prisma.DepartmentSelect;
  specialty: Prisma.SpecialtySelect;
};

export const jobExperiencesRouter = createTRPCRouter({
  get: protectedProcedure
    .input(z.object({ id: z.string(), include: zIncludeSchema.optional() }))
    .query(async ({ ctx, input }) => {
      const select = {
        ...selection.jobExperience,
        provider: input.include?.provider
          ? {
              select: {
                ...selection.provider,
                person: input.include.person
                  ? { select: selection.person }
                  : undefined,
              },
            }
          : undefined,
        organization: input.include?.organization
          ? { select: selection.organization }
          : undefined,
        facility: input.include?.facility
          ? { select: selection.facility }
          : undefined,
        department: input.include?.department
          ? { select: selection.department }
          : undefined,
        specialties: input.include?.specialties
          ? { select: selection.specialty }
          : undefined,
      } satisfies Prisma.JobExperienceSelect;

      const jobExperience = (await ctx.prisma.jobExperience.findUnique({
        where: { id: input.id },
        select,
      })) as unknown as ProcedureResult<
        typeof select,
        Prisma.$JobExperiencePayload
      > | null;

      if (!jobExperience) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job experience not found",
        });
      }

      return jobExperience;
    }),

  getMany: protectedProcedure
    .input(
      z.object({
        providerId: z.string(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        include: zIncludeSchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const where: Prisma.JobExperienceWhereInput = {
        providerId: input.providerId,
        deletedAt: input.include?.deleted ? undefined : null,
      };

      const select = {
        ...selection.jobExperience,
        provider: input.include?.provider
          ? {
              select: {
                ...selection.provider,
                person: input.include.person
                  ? { select: selection.person }
                  : undefined,
              },
            }
          : undefined,
        organization: input.include?.organization
          ? { select: selection.organization }
          : undefined,
        facility: input.include?.facility
          ? { select: selection.facility }
          : undefined,
        department: input.include?.department
          ? { select: selection.department }
          : undefined,
        specialties: input.include?.specialties
          ? { select: selection.specialty }
          : undefined,
      } satisfies Prisma.JobExperienceSelect;

      const [total, rawItems] = await Promise.all([
        ctx.prisma.jobExperience.count({ where }),
        ctx.prisma.jobExperience.findMany({
          where,
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize,
          orderBy: { startDate: "desc" },
          select,
        }),
      ]);

      const items = rawItems as unknown as ProcedureResult<
        typeof select,
        Prisma.$JobExperiencePayload
      >[];

      return {
        items,
        total,
      };
    }),

  create: protectedProcedure
    .input(zJobExperienceSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.prisma.jobExperience.create({
        data: {
          ...input,
          specialties: input.specialties
            ? {
                connect: input.specialties.map((id) => ({ id })),
              }
            : undefined,
        },
        select: {
          id: true,
        },
      });

      await performAction({
        actorId: ctx.user.id,
        type: ActionType.CREATE,
        resourceType: ResourceType.JOB_EXPERIENCE,
        resourceId: result.id,
        providerId: input.providerId,
        metadata: {
          role: input.role,
          company: input.company ?? null,
          specialtiesCount: input.specialties?.length || 0,
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
        },
      } satisfies ActionData);

      return result;
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        data: zJobExperienceSchema.partial().omit({ providerId: true }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const experience = await ctx.prisma.jobExperience.findUnique({
        where: { id: input.id },
        include: { specialties: true },
      });

      if (!experience) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Experience not found",
        });
      }

      const result = await ctx.prisma.jobExperience.update({
        where: { id: input.id },
        data: {
          ...input.data,
          specialties: input.data.specialties
            ? {
                set: input.data.specialties.map((id) => ({ id })),
              }
            : undefined,
        },
        select: {
          id: true,
          providerId: true,
        },
      });

      await performAction({
        actorId: ctx.user.id,
        type: ActionType.UPDATE,
        resourceType: ResourceType.JOB_EXPERIENCE,
        resourceId: result.id,
        providerId: result.providerId,
        metadata: {
          updatedFields: Object.keys(input.data).join(","),
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
        },
      } satisfies ActionData);

      return result;
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const currentExperience = await ctx.prisma.jobExperience.findUnique({
        where: { id: input.id },
      });

      if (!currentExperience) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Experience not found",
        });
      }

      let experience;

      if (ctx.options.permanentDelete) {
        experience = await ctx.prisma.jobExperience.delete({
          where: { id: input.id },
          select: {
            id: true,
            providerId: true,
          },
        });
      } else {
        experience = await ctx.prisma.jobExperience.update({
          where: { id: input.id },
          data: { deletedAt: new Date() },
          select: {
            id: true,
            providerId: true,
          },
        });
      }

      await performAction({
        actorId: ctx.user.id,
        type: ActionType.DELETE,
        resourceType: ResourceType.JOB_EXPERIENCE,
        resourceId: experience.id,
        providerId: experience.providerId,
        metadata: {
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
        },
      } satisfies ActionData);

      return experience;
    }),
});
