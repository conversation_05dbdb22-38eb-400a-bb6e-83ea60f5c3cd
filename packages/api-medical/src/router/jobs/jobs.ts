import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type {
  ApplicationStatus,
  JobPositionStatus,
  OfferStatus,
  Prisma,
} from "@axa/database-medical";
import {
  JobPostMode,
  JobPostPriority,
  JobPostStatus,
  JobPostType,
  PayType,
} from "@axa/database-medical";
import { calculateSkip } from "@axa/lib/utils";

import type { ProcedureResult } from "../../types/select";

import { ActionType, ResourceType } from "../../constants/actions";
import { performAction } from "../../lib/actions";
import {
  authorizedProcedure,
  createTRPCRouter,
  publicProcedure,
} from "../../trpc";

// Base enums
const zJobPostStatus = z.nativeEnum(JobPostStatus);
const zPayType = z.nativeEnum(PayType);
const zJobPostMode = z.nativeEnum(JobPostMode);
const zJobPostType = z.nativeEnum(JobPostType);
const zJobPostPriority = z.nativeEnum(JobPostPriority);

// Core schema (direct fields)
const zCoreJobPost = z.object({
  status: zJobPostStatus.default(JobPostStatus.DRAFT),
  mode: zJobPostMode.default(JobPostMode.INDEPENDENT),
  priority: zJobPostPriority.default(JobPostPriority.MEDIUM),
  type: zJobPostType.optional(),
  summary: z.string(),
  scope: z.string(),
  role: z.string(),
  paymentType: zPayType.default(PayType.HOURLY),
  paymentAmount: z.number().optional().default(0.0),
  paymentRate: z.number().optional().default(1.0),
  nightRate: z.number().optional().default(1.25),
  overtimeRate: z.number().optional().default(1.5),
  holidayRate: z.number().optional().default(2.0),
  bonusRate: z.number().optional().default(0.0),
});

// Extended schema with relations
const zJobPostSchema = zCoreJobPost.extend({
  organizationId: z.string(),
  locationId: z.string().optional(),
  departmentId: z.string().optional(),
  specialtyIds: z.array(z.string()).optional(),
});

// Schema for controlling nested selections
const zIncludeSchema = z
  .object({
    organization: z.boolean().default(false),
    location: z.boolean().default(false),
    specialties: z.boolean().default(false),
    department: z.boolean().default(false),
    schedule: z.boolean().default(false),
    thread: z.boolean().default(false),
    offers: z.boolean().default(false),
    applications: z.boolean().default(false),
    actions: z.boolean().default(false),
    person: z.boolean().default(true),
    contracts: z.boolean().default(false),
    provider: z.boolean().default(false),
    position: z.boolean().default(false),
    contacts: z.boolean().default(false),
    analytics: z.boolean().default(false),
    deleted: z.boolean().default(false),
    context: z.boolean().default(false),
  })
  .optional();

// Standard selections for all related data
const selection = {
  jobPost: {
    id: true,
    status: true,
    mode: true,
    role: true,
    type: true,
    summary: true,
    scope: true,
    priority: true,
    paymentType: true,
    paymentAmount: true,
    paymentRate: true,
    nightRate: true,
    overtimeRate: true,
    holidayRate: true,
    bonusRate: true,
    billingType: true,
    billingRate: true,
    isBillable: true,
    publishedAt: true,
    filledAt: true,
    completedAt: true,
    cancelledAt: true,
    expiredAt: true,
    expiresAt: true,
    createdAt: true,
    updatedAt: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  location: {
    id: true,
    name: true,
    type: true,
    address: {
      select: {
        formatted: true,
        timeZone: true,
        latitude: true,
        longitude: true,
      },
    },
  },
  schedule: {
    id: true,
    type: true,
    startsAt: true,
    endsAt: true,
    blocks: {
      select: {
        id: true,
        type: true,
        startsAt: true,
        endsAt: true,
        startDate: true,
        endDate: true,
        startTime: true,
        endTime: true,
        dayOfWeek: true,
        hours: true,
        recurrence: true,
        timeZone: true,
      },
    },
  },
  specialty: {
    id: true,
    name: true,
  },
  department: {
    id: true,
    name: true,
    description: true,
    type: true,
    contacts: {
      select: {
        id: true,
        role: true,
        person: {
          select: {
            id: true,
            role: true,
            title: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
            avatar: true,
          },
        },
      },
    },
  },
  thread: {
    id: true,
  },
  offer: {
    id: true,
    status: true,
    provider: {
      select: {
        id: true,
        title: true,
        person: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    },
  },
  application: {
    id: true,
    status: true,
    provider: {
      select: {
        id: true,
        title: true,
        person: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    },
  },
  contract: {
    id: true,
    status: true,
    createdAt: true,
    expiresAt: true,
    type: true,
    signatures: {
      select: {
        id: true,
        status: true,
        role: true,
        createdAt: true,
        person: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    },
  },
  contact: {
    id: true,
    role: true,
    person: {
      select: {
        id: true,
        role: true,
        title: true,
        firstName: true,
        lastName: true,
        phone: true,
        email: true,
        avatar: true,
      },
    },
  },
  action: {
    id: true,
    type: true,
    createdAt: true,
    metadata: true,
    actor: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        avatar: true,
      },
    },
  },
  provider: {
    id: true,
    title: true,
    status: true,
    person: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        avatar: true,
      },
    },
  },
  position: {
    id: true,
    status: true,
    createdAt: true,
    completedAt: true,
  },
} satisfies {
  jobPost: Prisma.JobPostSelect;
  organization: Prisma.OrganizationSelect;
  location: Prisma.LocationSelect;
  schedule: Prisma.ScheduleSelect;
  specialty: Prisma.SpecialtySelect;
  department: Prisma.DepartmentSelect;
  thread: Prisma.ThreadSelect;
  offer: Prisma.OfferSelect;
  application: Prisma.ApplicationSelect;
  action: Prisma.ActionSelect;
  contract: Prisma.ContractSelect;
  provider: Prisma.ProviderSelect;
  contact: Prisma.ContactSelect;
  position: Prisma.JobPositionSelect;
};

export const jobPostsRouter = createTRPCRouter({
  metadata: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const jobPost = await ctx.prisma.jobPost.findUnique({
        where: { id: input.id },
        select: {
          summary: true,
          scope: true,
        },
      });

      return {
        title: jobPost?.summary,
        description: jobPost?.scope,
      };
    }),

  get: authorizedProcedure
    .input(z.object({ id: z.string(), include: zIncludeSchema.optional() }))
    .query(async ({ ctx, input }) => {
      const select = {
        ...selection.jobPost,
        organization: input.include?.organization
          ? { select: selection.organization }
          : undefined,
        location: input.include?.location
          ? { select: selection.location }
          : undefined,
        specialties: input.include?.specialties
          ? { select: selection.specialty }
          : undefined,
        department: input.include?.department
          ? { select: selection.department }
          : undefined,
        schedule: input.include?.schedule
          ? { select: selection.schedule }
          : undefined,
        thread: input.include?.thread
          ? { select: selection.thread }
          : undefined,
        offers: input.include?.offers ? { select: selection.offer } : undefined,
        applications: input.include?.applications
          ? { select: selection.application }
          : undefined,
        actions: input.include?.actions
          ? { select: selection.action }
          : undefined,
        contacts: input.include?.contacts
          ? { select: selection.contact }
          : undefined,
        position: input.include?.position
          ? {
              select: {
                ...selection.position,
                provider: {
                  select: {
                    ...selection.provider,
                  },
                },
                contracts: input.include.contracts
                  ? { select: selection.contract }
                  : undefined,
              },
            }
          : undefined,
      } satisfies Prisma.JobPostSelect;

      const jobPost = (await ctx.prisma.jobPost.findUnique({
        where: {
          id: input.id,
          deletedAt: input.include?.deleted ? undefined : null,
        },
        select,
      })) as unknown as ProcedureResult<
        typeof select,
        Prisma.$JobPostPayload
      > | null;

      if (!jobPost) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job post not found",
        });
      }

      let analytics = null;

      if (input.include?.analytics) {
        const applicants = await ctx.prisma.application.count({
          where: { jobId: jobPost.id },
        });
        const offers = await ctx.prisma.offer.count({
          where: { jobId: jobPost.id },
        });
        const avgPayment = await ctx.prisma.jobPost.aggregate({
          where: { role: jobPost.role },
          _min: {
            paymentAmount: true,
          },
          _max: {
            paymentAmount: true,
          },
          _avg: {
            paymentAmount: true,
          },
        });

        analytics = {
          applicants,
          offers,
          payment: {
            min: avgPayment._min.paymentAmount,
            max: avgPayment._max.paymentAmount,
            avg: avgPayment._avg.paymentAmount,
          },
        };
      }

      let context = null;

      if (input.include?.context) {
        const user = await ctx.prisma.person.findUnique({
          where: { id: ctx.user.id },
          select: {
            id: true,
            role: true,
            provider: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        });

        if (user?.provider) {
          context = {
            offer: await ctx.prisma.offer.findFirst({
              where: {
                jobId: jobPost.id,
                providerId: user.provider.id,
              },
              select: {
                id: true,
                status: true,
                createdAt: true,
              },
            }),
            application: await ctx.prisma.application.findFirst({
              where: {
                jobId: jobPost.id,
                providerId: user.provider.id,
              },
              select: {
                id: true,
                status: true,
                createdAt: true,
              },
            }),
            position: await ctx.prisma.jobPosition.findFirst({
              where: {
                job: {
                  id: jobPost.id,
                },
                providerId: user.provider.id,
              },
              select: {
                id: true,
                status: true,
                createdAt: true,
              },
            }),
          };
        }
      }

      return {
        ...jobPost,
        analytics,
        context,
      };
    }),

  getMany: authorizedProcedure
    .input(
      z.object({
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        status: zJobPostStatus.optional(),
        type: zJobPostType.optional(),
        priority: zJobPostPriority.optional(),
        role: z.string().optional(),
        organizationId: z.string().optional(),
        include: zIncludeSchema.optional(),
        filter: z
          .object({
            draft: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const where: Prisma.JobPostWhereInput = {
        deletedAt: input.include?.deleted ? undefined : null,
        organizationId: input.organizationId,
        status: input.status,
        type: input.type,
        role: input.role,
        ...(input.filter?.draft && {
          status: {
            not: JobPostStatus.DRAFT,
          },
        }),
        OR: input.query
          ? [
              { summary: { contains: input.query, mode: "insensitive" } },
              { scope: { contains: input.query, mode: "insensitive" } },
              {
                organization: {
                  name: { contains: input.query, mode: "insensitive" },
                },
              },
              {
                location: {
                  name: { contains: input.query, mode: "insensitive" },
                },
              },
            ]
          : undefined,
      };

      const select = {
        ...selection.jobPost,
        organization: input.include?.organization
          ? { select: selection.organization }
          : undefined,
        location: input.include?.location
          ? { select: selection.location }
          : undefined,
        specialties: input.include?.specialties
          ? { select: selection.specialty }
          : undefined,
        department: input.include?.department
          ? { select: selection.department }
          : undefined,
        schedule: input.include?.schedule
          ? { select: selection.schedule }
          : undefined,
        thread: input.include?.thread
          ? { select: selection.thread }
          : undefined,
        offers: input.include?.offers ? { select: selection.offer } : undefined,
        applications: input.include?.applications
          ? { select: selection.application }
          : undefined,
        contacts: input.include?.contacts
          ? { select: selection.contact }
          : undefined,
        actions: input.include?.actions
          ? { select: selection.action }
          : undefined,
        position: input.include?.position
          ? {
              select: {
                ...selection.position,
                provider: {
                  select: {
                    ...selection.provider,
                  },
                },
              },
            }
          : undefined,
      } satisfies Prisma.JobPostSelect;

      const [total, rawItems] = await Promise.all([
        ctx.prisma.jobPost.count({ where }),
        ctx.prisma.jobPost.findMany({
          where,
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize,
          orderBy: { createdAt: "desc" },
          select,
        }),
      ]);

      const items = rawItems as unknown as (ProcedureResult<
        typeof select,
        Prisma.$JobPostPayload
      > & {
        context: {
          offer: {
            id: string;
            status: OfferStatus;
            createdAt: Date;
          } | null;
          application: {
            id: string;
            status: ApplicationStatus;
            createdAt: Date;
          } | null;
          position: {
            id: string;
            status: JobPositionStatus;
            createdAt: Date;
          } | null;
        } | null;
        analytics: {
          applicants: number;
          offers: number;
          payment: {
            min: number | null;
            max: number | null;
            avg: number | null;
          };
        } | null;
      })[];

      if (input.include?.context || input.include?.analytics) {
        for (const item of items) {
          let context = null;
          let analytics = null;

          if (input.include.context) {
            const user = await ctx.prisma.person.findUnique({
              where: { id: ctx.user.id },
              select: {
                id: true,
                role: true,
                provider: {
                  select: {
                    id: true,
                    title: true,
                  },
                },
              },
            });

            if (user?.provider) {
              context = {
                offer: await ctx.prisma.offer.findFirst({
                  where: {
                    jobId: item.id,
                    providerId: user.provider.id,
                  },
                  select: {
                    id: true,
                    status: true,
                    createdAt: true,
                  },
                }),
                application: await ctx.prisma.application.findFirst({
                  where: {
                    jobId: item.id,
                    providerId: user.provider.id,
                  },
                  select: {
                    id: true,
                    status: true,
                    createdAt: true,
                  },
                }),
                position: await ctx.prisma.jobPosition.findFirst({
                  where: {
                    job: {
                      id: item.id,
                    },
                    providerId: user.provider.id,
                  },
                  select: {
                    id: true,
                    status: true,
                    createdAt: true,
                  },
                }),
              };
            }
          }

          if (input.include.analytics) {
            const applicants = await ctx.prisma.application.count({
              where: { jobId: item.id },
            });
            const offers = await ctx.prisma.offer.count({
              where: { jobId: item.id },
            });
            const avgPayment = await ctx.prisma.jobPost.aggregate({
              where: { role: item.role },
              _min: {
                paymentAmount: true,
              },
              _max: {
                paymentAmount: true,
              },
              _avg: {
                paymentAmount: true,
              },
            });

            analytics = {
              applicants,
              offers,
              payment: {
                min: avgPayment._min.paymentAmount,
                max: avgPayment._max.paymentAmount,
                avg: avgPayment._avg.paymentAmount,
              },
            };
          }

          items.splice(items.indexOf(item), 1, {
            ...item,
            context,
            analytics,
          });
        }
      }

      return {
        items,
        total,
      };
    }),

  create: authorizedProcedure
    .input(zJobPostSchema)
    .mutation(async ({ ctx, input }) => {
      const organization = await ctx.prisma.organization.findUnique({
        where: { id: input.organizationId },
        select: { id: true, assistPercentage: true, basePercentage: true },
      });

      if (!organization) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Organization is required to create a job post",
        });
      }

      const mode =
        input.mode === JobPostMode.INDEPENDENT
          ? JobPostMode.INDEPENDENT
          : JobPostMode.ASSISTED;

      // TODO: add payment rates based on job role from previous job posts similar to how we do for providers
      const rates = await ctx.prisma.jobPost.aggregate({
        where: { role: input.role },
        _avg: {
          paymentRate: true,
          nightRate: true,
          overtimeRate: true,
          holidayRate: true,
          bonusRate: true,
        },
      });

      const bonusRate = input.bonusRate ?? rates._avg.bonusRate;
      const paymentRate = input.paymentRate ?? rates._avg.paymentRate;
      const nightRate = input.nightRate ?? rates._avg.nightRate;
      const overtimeRate = input.overtimeRate ?? rates._avg.overtimeRate;
      const holidayRate = input.holidayRate ?? rates._avg.holidayRate;
      const billingRate =
        mode === JobPostMode.INDEPENDENT
          ? organization.basePercentage
          : organization.assistPercentage;

      const result = await ctx.prisma.jobPost.create({
        data: {
          status: input.status,
          mode: input.mode,
          type: input.type,
          role: input.role,
          summary: input.summary,
          scope: input.scope,
          paymentType: input.paymentType,
          paymentAmount: input.paymentAmount,
          paymentRate,
          nightRate,
          overtimeRate,
          holidayRate,
          bonusRate,
          billingRate,
          organization: {
            connect: { id: organization.id },
          },
          location: input.locationId
            ? { connect: { id: input.locationId } }
            : undefined,
          department: input.departmentId
            ? { connect: { id: input.departmentId } }
            : undefined,
          specialties:
            input.specialtyIds && input.specialtyIds.length > 0
              ? { connect: input.specialtyIds.map((id) => ({ id })) }
              : undefined,
        },
        select: {
          id: true,
          organization: { select: { id: true, name: true } },
          summary: true,
        },
      });

      await performAction({
        actorId: ctx.user.id,
        type: ActionType.CREATE,
        resourceType: ResourceType.JOB_POST,
        resourceId: result.id,
        organizationId: input.organizationId,
        metadata: {
          title: result.summary,
          organizationName: result.organization.name,
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
        },
      });

      return result;
    }),

  update: authorizedProcedure
    .input(
      z.object({
        id: z.string(),
        data: zCoreJobPost.partial(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const jobPost = await ctx.prisma.jobPost.findUnique({
        where: { id: input.id },
        select: { id: true, organizationId: true },
      });

      if (!jobPost) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job post not found",
        });
      }

      const result = await ctx.prisma.jobPost.update({
        where: { id: input.id },
        data: input.data,
        select: {
          id: true,
          organization: { select: { id: true, name: true } },
          summary: true,
        },
      });

      await performAction({
        actorId: ctx.user.id,
        type: ActionType.UPDATE,
        resourceType: ResourceType.JOB_POST,
        resourceId: result.id,
        organizationId: result.organization.id,
        metadata: {
          title: result.summary,
          organizationName: result.organization.name,
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
        },
      });

      return {
        id: result.id,
      };
    }),

  delete: authorizedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const jobPost = await ctx.prisma.jobPost.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          organizationId: true,
          summary: true,
          organization: { select: { name: true } },
        },
      });

      if (!jobPost) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job post not found",
        });
      }

      if (ctx.options.permanentDelete) {
        await ctx.prisma.jobPost.delete({
          where: { id: input.id },
          select: { id: true },
        });
      } else {
        await ctx.prisma.jobPost.update({
          where: { id: input.id },
          data: { deletedAt: new Date() },
          select: { id: true },
        });
      }

      await performAction({
        actorId: ctx.user.id,
        type: ActionType.DELETE,
        resourceType: ResourceType.JOB_POST,
        resourceId: input.id,
        organizationId: jobPost.organizationId,
        metadata: {
          title: jobPost.summary,
          organizationName: jobPost.organization.name,
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
        },
      });

      return { id: input.id };
    }),
});
