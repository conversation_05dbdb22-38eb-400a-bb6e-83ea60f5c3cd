import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type {
  RateRangeFieldProps,
  RateRangeValue,
} from "@/ui/fields/numeric/rate-range";

import {
  RangeInput,
  RateInput,
  RateRangeControl,
  RateRangeField,
  SliderRangeInput,
  SliderRateInput,
} from "@/ui/fields/numeric/rate-range";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Custom schema for rate range validation
const rateRangeSchema = z
  .object({
    min: z.number().optional(),
    max: z.number().optional(),
    rate: z.number().optional(),
  })
  .refine(
    (data) => {
      if (data.min !== undefined && data.max !== undefined) {
        return data.max > data.min;
      }
      return true;
    },
    {
      message: "Maximum rate must be greater than minimum rate",
    },
  );

// Define args type for this specific story
interface CurrentRateRangeStoryArgs
  extends RateRangeFieldProps,
    BaseFormProviderArgs {
  fieldName: string;
  fieldSchema: typeof rateRangeSchema;
  defaultValues?: Record<string, RateRangeValue>;
}

const meta: Meta<CurrentRateRangeStoryArgs> = {
  title: "Fields/Numeric/RateRangeField",
  component: RateRangeField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for RateRangeField component itself
    label: "Hourly Rate",
    name: "rateRange",
    minPlaceholder: "Min rate",
    maxPlaceholder: "Max rate",
    ratePlaceholder: "Enter hourly rate",
    description:
      "Set your hourly rate range with Currency formatting and slider controls",
    currency: "$",
    allowDecimals: true,
    step: 0.01,

    // Args for the WithFormProvider decorator
    fieldName: "rateRange",
    fieldSchema: rateRangeSchema,
    onFormSubmit: fn(),
    defaultValues: { rateRange: {} },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentRateRangeStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const RangeMode: Story = {
  args: {
    mode: "range",
    allowModeToggle: false,
    description: "Set the minimum and maximum hourly rates for this position",
  },
};

export const RateModeWithPredefinedRange: Story = {
  args: {
    mode: "rate",
    allowModeToggle: false,
    predefinedRange: { min: 50, max: 150 },
    description: "Select your hourly rate within the specified range",
  },
};

export const WithInitialRangeValues: Story = {
  args: {
    name: "initialRange",
    fieldName: "initialRange",
    mode: "range",
    allowModeToggle: false,
    defaultValues: {
      initialRange: { min: 75, max: 125 },
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // Currency inputs format values with $ and decimals
    const minInput = canvas.getByDisplayValue("$75.00");
    const maxInput = canvas.getByDisplayValue("$125.00");
    await expect(minInput).toBeInTheDocument();
    await expect(maxInput).toBeInTheDocument();
  },
};

export const WithInitialRateValue: Story = {
  args: {
    name: "initialRate",
    fieldName: "initialRate",
    mode: "rate",
    allowModeToggle: false,
    predefinedRange: { min: 40, max: 200 },
    defaultValues: {
      initialRate: { rate: 85 },
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // Currency input formats the value
    const rateInput = canvas.getByDisplayValue("$85.00");
    await expect(rateInput).toBeInTheDocument();
  },
};

export const InteractiveRangeInput: Story = {
  args: {
    name: "interactiveRange",
    fieldName: "interactiveRange",
    mode: "range",
    allowModeToggle: false,
    defaultValues: { interactiveRange: {} },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const minInput = canvas.getByPlaceholderText("Min rate");
    const maxInput = canvas.getByPlaceholderText("Max rate");

    // Clear and enter valid range (Currency inputs format automatically)
    await userEvent.clear(minInput);
    await userEvent.type(minInput, "50");
    await userEvent.clear(maxInput);
    await userEvent.type(maxInput, "100");

    // Currency inputs will format the values with $ and decimals
    await expect(canvas.getByDisplayValue("$50.00")).toBeInTheDocument();
    await expect(canvas.getByDisplayValue("$100.00")).toBeInTheDocument();

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: { min: 50, max: 100 } },
      expect.anything(),
    );
  },
};

export const InteractiveRateSelection: Story = {
  args: {
    name: "interactiveRate",
    fieldName: "interactiveRate",
    mode: "rate",
    allowModeToggle: false,
    predefinedRange: { min: 30, max: 120 },
    defaultValues: { interactiveRate: {} },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const rateInput = canvas.getByPlaceholderText("Enter hourly rate");

    // Clear and enter rate within range
    await userEvent.clear(rateInput);
    await userEvent.type(rateInput, "85");

    // Currency input will format the value
    await expect(canvas.getByDisplayValue("$85.00")).toBeInTheDocument();

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: { rate: 85 } },
      expect.anything(),
    );
  },
};

export const ToggleBetweenModes: Story = {
  args: {
    name: "toggleMode",
    fieldName: "toggleMode",
    allowModeToggle: true,
    defaultValues: { toggleMode: {} },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Should start in range mode
    await expect(canvas.getByText("Set Range")).toHaveAttribute(
      "aria-pressed",
      "true",
    );
    await expect(canvas.getByPlaceholderText("Min rate")).toBeInTheDocument();
    await expect(canvas.getByPlaceholderText("Max rate")).toBeInTheDocument();

    // Switch to rate mode
    await userEvent.click(canvas.getByText("Set Rate"));

    await expect(canvas.getByText("Set Rate")).toHaveAttribute(
      "aria-pressed",
      "true",
    );
    await expect(
      canvas.getByPlaceholderText("Enter hourly rate"),
    ).toBeInTheDocument();
  },
};

export const WithValidationError: Story = {
  args: {
    name: "invalidRange",
    fieldName: "invalidRange",
    mode: "range",
    allowModeToggle: false,
    defaultValues: {
      invalidRange: { min: 100, max: 50 }, // Invalid: max < min
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Maximum rate must be greater than minimum rate"),
    ).toBeInTheDocument();
  },
};

export const RateOutOfRange: Story = {
  args: {
    name: "outOfRange",
    fieldName: "outOfRange",
    mode: "rate",
    allowModeToggle: false,
    predefinedRange: { min: 50, max: 100 },
    defaultValues: {
      outOfRange: { rate: 150 }, // Out of range
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Rate must be between $50.00 and $100.00"),
    ).toBeInTheDocument();
  },
};

export const CustomCurrency: Story = {
  args: {
    currency: "€",
    mode: "range",
    allowModeToggle: false,
    minPlaceholder: "Min rate (€)",
    maxPlaceholder: "Max rate (€)",
    description: "Set your hourly rate range in Euros",
  },
};

export const ControlVsField: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">
          RateRangeControl (Standalone with Slider)
        </h3>
        <p className="mb-4 text-sm text-muted-foreground">
          Direct use of the control component with slider and input controls
        </p>
        <RateRangeControl
          mode="range"
          allowModeToggle={true}
          useSlider={true}
          showInputs={true}
          showTooltips={true}
          currency="$"
          min={20}
          max={300}
          step={5}
          allowDecimals={true}
          minPlaceholder="Minimum rate"
          maxPlaceholder="Maximum rate"
          onChange={(value) => console.log("Control value:", value)}
        />
      </div>
      <div>
        <h3 className="mb-2 text-lg font-semibold">
          RateRangeField (Form Integrated)
        </h3>
        <p className="mb-4 text-sm text-muted-foreground">
          Form-integrated version with validation, labels and slider
        </p>
        <RateRangeField
          name="fieldExample"
          label="Position Rate Range"
          description="Set the hourly rate range for this position"
          mode="range"
          allowModeToggle={true}
          useSlider={true}
          showInputs={true}
          currency="$"
          min={25}
          max={250}
          step={2.5}
        />
      </div>
    </div>
  ),
  decorators: [WithFormProvider],
  args: {
    fieldName: "fieldExample",
    fieldSchema: rateRangeSchema,
    onFormSubmit: fn(),
    defaultValues: { fieldExample: {} },
  },
};

export const SliderMode: Story = {
  args: {
    name: "sliderMode",
    fieldName: "sliderMode",
    useSlider: true,
    showInputs: true,
    showTooltips: true,
    mode: "range",
    min: 20,
    max: 200,
    step: 5,
    currency: "$",
    description:
      "Use the slider to set your rate range, with optional precise inputs",
    defaultValues: { sliderMode: { min: 50, max: 120 } },
  },
};

export const SliderOnly: Story = {
  args: {
    name: "sliderOnly",
    fieldName: "sliderOnly",
    useSlider: true,
    showInputs: false,
    showTooltips: true,
    mode: "range",
    min: 25,
    max: 150,
    step: 2.5,
    currency: "€",
    description: "Pure slider experience without input fields",
    defaultValues: { sliderOnly: { min: 40, max: 100 } },
  },
};

export const SliderRateSelection: Story = {
  args: {
    name: "sliderRate",
    fieldName: "sliderRate",
    mode: "rate",
    predefinedRange: { min: 30, max: 180 },
    useSlider: true,
    showInputs: true,
    showTooltips: true,
    step: 1,
    currency: "$",
    description: "Select a specific rate within the range using the slider",
    defaultValues: { sliderRate: { rate: 85 } },
  },
};

export const CurrencyFormatting: Story = {
  args: {
    name: "currencyDemo",
    fieldName: "currencyDemo",
    mode: "range",
    allowModeToggle: true,
    useSlider: true,
    showInputs: true,
    label: "Professional Rate Range",
    description:
      "Currency inputs automatically format with $, commas, and decimals",
    defaultValues: { currencyDemo: { min: 1250.5, max: 3750.75 } },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check that currency formatting is applied
    const minInput = canvas.getByDisplayValue("$1,250.50");
    const maxInput = canvas.getByDisplayValue("$3,750.75");

    await expect(minInput).toBeInTheDocument();
    await expect(maxInput).toBeInTheDocument();
  },
};

export const ConvenienceComponents: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">
          Traditional Input Components
        </h3>
        <div className="space-y-4">
          <RangeInput
            name="convenienceRange"
            label="Salary Range"
            description="Set the salary range using inputs"
            currency="$"
            step={1000}
            allowDecimals={false}
          />
          <RateInput
            name="convenienceRate"
            label="Your Rate"
            description="Select your preferred hourly rate"
            range={{ min: 25, max: 75 }}
            currency="$"
          />
        </div>
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Slider-Only Components</h3>
        <div className="space-y-4">
          <SliderRangeInput
            name="sliderRange"
            label="Budget Range"
            description="Set your budget range using the slider"
            currency="$"
            min={1000}
            max={10000}
            step={250}
          />
          <SliderRateInput
            name="sliderRateOnly"
            label="Freelance Rate"
            description="Choose your hourly rate"
            range={{ min: 50, max: 200 }}
            currency="$"
            step={5}
          />
        </div>
      </div>
    </div>
  ),
  decorators: [WithFormProvider],
  args: {
    fieldName: "convenience",
    fieldSchema: rateRangeSchema,
    onFormSubmit: fn(),
    defaultValues: {
      convenienceRange: {},
      convenienceRate: {},
      sliderRange: { min: 2500, max: 7500 },
      sliderRateOnly: { rate: 125 },
    },
  },
};
