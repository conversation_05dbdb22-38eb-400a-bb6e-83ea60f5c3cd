import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import SelectOrganization from "../../src/selectors/SelectOrganization";

const meta = {
  title: "Selectors/SelectOrganization",
  component: SelectOrganization,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    useDialog: {
      control: { type: "boolean" },
    },
    loading: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg", "xl"],
    },
    variant: {
      control: { type: "select" },
      options: ["outline", "ghost", "secondary", "destructive"],
    },
    align: {
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
  },
} satisfies Meta<typeof SelectOrganization>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample organization data
const organizations = [
  {
    id: "1",
    name: "Acme Corporation",
    avatar: null,
  },
  {
    id: "2",
    name: "Globex Industries",
    avatar: null,
  },
  {
    id: "3",
    name: "Stark Enterprises",
    avatar: null,
  },
  {
    id: "4",
    name: "Wayne Enterprises",
    avatar: null,
  },
];

/**
 * Default SelectOrganization component with example data
 */
export const Default: Story = {
  args: {
    data: organizations,
  },
};

export const Dialog: Story = {
  args: {
    data: organizations,
    useDialog: true,
  },
};

export const DialogOpen: Story = {
  args: {
    data: organizations,
    useDialog: true,
    open: true,
  },
};

/**
 * SelectOrganization component in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
    data: [],
  },
};

export const DialogLoading: Story = {
  args: {
    useDialog: true,
    loading: true,
    data: [],
  },
};

export const DialogOpenLoading: Story = {
  args: {
    useDialog: true,
    loading: true,
    open: true,
    data: [],
  },
};

/**
 * SelectOrganization component with medium size
 */
export const MediumSize: Story = {
  args: {
    data: organizations,
    size: "md",
  },
};
