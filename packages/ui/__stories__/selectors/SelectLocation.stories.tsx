import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import SelectLocation from "../../src/selectors/SelectLocation";

const meta = {
  title: "Selectors/SelectLocation",
  component: SelectLocation,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    useDialog: {
      control: { type: "boolean" },
    },
    loading: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg", "xl"],
    },
    variant: {
      control: { type: "select" },
      options: ["outline", "ghost", "secondary", "destructive"],
    },
    align: {
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
  },
} satisfies Meta<typeof SelectLocation>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample location data
const locations = [
  {
    id: "1",
    name: "Headquarters",
    address: {
      formatted: "123 Main St, New York, NY 10001, USA",
      street: "123 Main St",
      city: "New York",
      state: "NY",
      postal: "10001",
      country: "USA",
    },
  },
  {
    id: "2",
    name: "Branch Office",
    address: {
      formatted: "456 Market St, San Francisco, CA 94105, USA",
      street: "456 Market St",
      city: "San Francisco",
      state: "CA",
      postal: "94105",
      country: "USA",
    },
  },
  {
    id: "3",
    name: "European Office",
    address: {
      formatted: "10 Baker Street, London, W1U 3BU, UK",
      street: "10 Baker Street",
      city: "London",
      postal: "W1U 3BU",
      country: "UK",
    },
  },
];

/**
 * Default SelectLocation component with example data
 */
export const Default: Story = {
  args: {
    data: locations,
  },
};

export const Dialog: Story = {
  args: {
    data: locations,
    useDialog: true,
  },
};

export const DialogOpen: Story = {
  args: {
    data: locations,
    useDialog: true,
    open: true,
  },
};

/**
 * SelectLocation component in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
    data: [],
  },
};

export const DialogLoading: Story = {
  args: {
    useDialog: true,
    loading: true,
    data: [],
  },
};

export const DialogOpenLoading: Story = {
  args: {
    useDialog: true,
    loading: true,
    open: true,
    data: [],
  },
};
