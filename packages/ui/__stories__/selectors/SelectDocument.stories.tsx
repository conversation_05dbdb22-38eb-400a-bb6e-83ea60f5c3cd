import type { <PERSON>a, StoryObj } from "@storybook/react";

import { SelectDocument } from "../../src/selectors/SelectDocument";

const meta = {
  title: "Selectors/SelectDocument",
  component: SelectDocument,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    useDialog: {
      control: { type: "boolean" },
    },
    loading: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg", "xl"],
    },
    variant: {
      control: { type: "select" },
      options: ["outline", "ghost", "secondary", "destructive"],
    },
    align: {
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
  },
} satisfies Meta<typeof SelectDocument>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample document data
const documents = [
  {
    id: "1",
    type: "pdf",
    name: "Contract Agreement.pdf",
    url: "/documents/contract.pdf",
    size: 1024 * 1024 * 2.5, // 2.5MB
  },
  {
    id: "2",
    type: "docx",
    name: "Report Q2 2023.docx",
    url: "/documents/report.docx",
    size: 1024 * 512, // 512KB
  },
  {
    id: "3",
    type: "xlsx",
    name: "Financial Data.xlsx",
    url: "/documents/financials.xlsx",
    size: 1024 * 768, // 768KB
  },
];

export const Default: Story = {
  args: {
    data: documents,
  },
};

export const Dialog: Story = {
  args: {
    data: documents,
    useDialog: true,
  },
};

export const DialogOpen: Story = {
  args: {
    data: documents,
    useDialog: true,
    open: true,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    data: [],
  },
};

export const DialogLoading: Story = {
  args: {
    useDialog: true,
    loading: true,
    data: [],
  },
};

export const DialogOpenLoading: Story = {
  args: {
    useDialog: true,
    loading: true,
    open: true,
    data: [],
  },
};
