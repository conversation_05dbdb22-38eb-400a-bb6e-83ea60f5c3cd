import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { SecurityImage } from "@/ui/blocks/security-id/types";

import SecurityIdContent from "@/ui/blocks/security-id/security-id-content";
import { DocumentType } from "@/ui/blocks/security-id/types";

// Mock images for stories
const mockFrontImage: SecurityImage = {
  url: "https://via.placeholder.com/400x250/3b82f6/ffffff?text=Front+ID",
  alt: "Front side of government ID",
  type: "front",
};

const mockBackImage: SecurityImage = {
  url: "https://via.placeholder.com/400x250/ef4444/ffffff?text=Back+ID",
  alt: "Back side of government ID",
  type: "back",
};

// Mock preview data
const mockPreviewData = {
  type: DocumentType.DRIVER_LICENSE,
  name: "<PERSON>",
  documentId: "DL-2024-789456",
  expiration: "12/15/2028",
};

const meta: Meta<typeof SecurityIdContent> = {
  title: "Blocks/Security ID/SecurityIdContent",
  component: SecurityIdContent,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A quad grid layout component for displaying security ID content with responsive t-shirt sizing. Features a preview section and front/back image tiles with proportional scaling.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="h-[400px] w-[500px] rounded-lg border p-4">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    size: {
      description: "T-shirt size for the component (excludes xs)",
      control: { type: "select" },
      options: ["sm", "md", "lg", "xl"],
    },
    type: {
      control: "select",
      options: [
        DocumentType.PASSPORT,
        DocumentType.GOVERNMENT_ID,
        DocumentType.DRIVER_LICENSE,
      ],
      description: "Document type",
    },
    name: {
      control: "text",
      description: "Name on the document",
    },
    documentId: {
      control: "text",
      description: "Document ID number",
    },
    expiration: {
      control: "text",
      description: "Document expiration date",
    },
    frontImage: {
      description: "Image data for the front side of the ID",
      control: false,
    },
    backImage: {
      description: "Image data for the back side of the ID",
      control: false,
    },
    onImageExpand: {
      description: "Callback when an image is expanded",
      action: "image-expanded",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof SecurityIdContent>;

// Size variations
export const Small: Story = {
  args: {
    size: "sm",
    ...mockPreviewData,
    frontImage: mockFrontImage,
    backImage: mockBackImage,
  },
};

export const Medium: Story = {
  args: {
    size: "md",
    ...mockPreviewData,
    frontImage: mockFrontImage,
    backImage: mockBackImage,
  },
};

export const Large: Story = {
  args: {
    size: "lg",
    ...mockPreviewData,
    frontImage: mockFrontImage,
    backImage: mockBackImage,
  },
};

export const ExtraLarge: Story = {
  args: {
    size: "xl",
    ...mockPreviewData,
    frontImage: mockFrontImage,
    backImage: mockBackImage,
  },
};

// Original stories updated with default size
export const Default: Story = {
  args: {
    size: "md",
    type: DocumentType.GOVERNMENT_ID,
    name: "John Doe",
    documentId: "ID*********",
  },
};

export const WithFullData: Story = {
  args: {
    size: "md",
    ...mockPreviewData,
    frontImage: mockFrontImage,
    backImage: mockBackImage,
  },
};

export const DriversLicense: Story = {
  args: {
    size: "md",
    type: DocumentType.DRIVER_LICENSE,
    name: "Michael Chen",
    documentId: "DL-2023-456789",
    expiration: "08/12/2027",
    frontImage: mockFrontImage,
    backImage: mockBackImage,
  },
};

export const Passport: Story = {
  args: {
    size: "md",
    type: DocumentType.PASSPORT,
    name: "Emma Rodriguez",
    documentId: "*********",
    expiration: "03/20/2032",
    frontImage: {
      ...mockFrontImage,
      url: "https://via.placeholder.com/400x250/16a34a/ffffff?text=Photo+Page",
      alt: "Passport photo page",
    },
    backImage: {
      ...mockBackImage,
      url: "https://via.placeholder.com/400x250/dc2626/ffffff?text=Signature+Page",
      alt: "Passport signature page",
    },
  },
};

export const WithFrontImageOnly: Story = {
  args: {
    size: "md",
    ...mockPreviewData,
    frontImage: mockFrontImage,
  },
};

// Size comparison
export const AllSizes: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 p-8 md:grid-cols-2">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Small</h3>
        <div className="h-[300px] w-[400px] rounded-lg border p-3">
          <SecurityIdContent
            size="sm"
            {...mockPreviewData}
            frontImage={mockFrontImage}
            backImage={mockBackImage}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Medium (Default)</h3>
        <div className="h-[400px] w-[500px] rounded-lg border p-4">
          <SecurityIdContent
            size="md"
            {...mockPreviewData}
            frontImage={mockFrontImage}
            backImage={mockBackImage}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Large</h3>
        <div className="h-[500px] w-[600px] rounded-lg border p-5">
          <SecurityIdContent
            size="lg"
            {...mockPreviewData}
            frontImage={mockFrontImage}
            backImage={mockBackImage}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Extra Large</h3>
        <div className="h-[600px] w-[700px] rounded-lg border p-6">
          <SecurityIdContent
            size="xl"
            {...mockPreviewData}
            frontImage={mockFrontImage}
            backImage={mockBackImage}
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    layout: "fullscreen",
  },
};

// Content variations across sizes
export const ContentVariations: Story = {
  render: () => (
    <div className="space-y-8 p-8">
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">
            Government ID - Different Sizes
          </h3>
          <div className="space-y-4">
            <div className="h-[300px] w-[400px] rounded-lg border p-3">
              <SecurityIdContent
                size="sm"
                type={DocumentType.GOVERNMENT_ID}
                name="Dr. Sarah Johnson"
                documentId="GOV-ID-*********"
                expiration="2025-12-31"
                frontImage={mockFrontImage}
                backImage={mockBackImage}
              />
            </div>
            <div className="h-[500px] w-[600px] rounded-lg border p-5">
              <SecurityIdContent
                size="lg"
                type={DocumentType.GOVERNMENT_ID}
                name="Dr. Sarah Johnson"
                documentId="GOV-ID-*********"
                expiration="2025-12-31"
                frontImage={mockFrontImage}
                backImage={mockBackImage}
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Passport - Different Sizes</h3>
          <div className="space-y-4">
            <div className="h-[300px] w-[400px] rounded-lg border p-3">
              <SecurityIdContent
                size="sm"
                type={DocumentType.PASSPORT}
                name="Jennifer Thompson"
                documentId="P987654321"
                expiration="2030-06-15"
                frontImage={mockFrontImage}
                backImage={mockBackImage}
              />
            </div>
            <div className="h-[500px] w-[600px] rounded-lg border p-5">
              <SecurityIdContent
                size="lg"
                type={DocumentType.PASSPORT}
                name="Jennifer Thompson"
                documentId="P987654321"
                expiration="2030-06-15"
                frontImage={mockFrontImage}
                backImage={mockBackImage}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    layout: "fullscreen",
  },
};

// Without images at different sizes
export const WithoutImages: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-6 p-8 md:grid-cols-2">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Small - No Images</h3>
        <div className="h-[300px] w-[400px] rounded-lg border p-3">
          <SecurityIdContent
            size="sm"
            type={DocumentType.DRIVER_LICENSE}
            name="Dr. Michael Chen"
            documentId="DL-2024-555666"
            expiration="2027-03-20"
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Large - No Images</h3>
        <div className="h-[500px] w-[600px] rounded-lg border p-5">
          <SecurityIdContent
            size="lg"
            type={DocumentType.DRIVER_LICENSE}
            name="Dr. Michael Chen"
            documentId="DL-2024-555666"
            expiration="2027-03-20"
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    layout: "fullscreen",
  },
};

// Interactive playground
export const Playground: Story = {
  args: {
    size: "md",
    ...mockPreviewData,
    frontImage: mockFrontImage,
    backImage: mockBackImage,
  },
};
