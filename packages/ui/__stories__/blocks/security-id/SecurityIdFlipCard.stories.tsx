import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type {
  DriverLicenseSecurityData,
  GovernmentIdSecurityData,
  PassportSecurityData,
  SecurityImage,
} from "@/ui/blocks/security-id";

import { DocumentType, SecurityIdFlipCard } from "@/ui/blocks/security-id";

const meta: Meta<typeof SecurityIdFlipCard> = {
  title: "Blocks/Security ID/SecurityIdFlipCard",
  component: SecurityIdFlipCard,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A flip card component for security ID verification with front verification display and back quad-grid detail view. Supports multiple document types with conditional access control.",
      },
    },
  },
  argTypes: {
    type: {
      control: "select",
      options: Object.values(DocumentType),
      description: "Type of security document",
    },
    provider: {
      control: "text",
      description: "Provider name to display",
    },
    authorized: {
      control: "boolean",
      description: "Whether user is authorized to view back content",
    },
    frontContent: {
      description: "Custom front content (overrides default)",
      control: false,
    },
    securityData: {
      description: "Security document data",
      control: false,
    },
    size: {
      control: "select",
      options: ["sm", "md", "lg", "xl"],
      description: "Card size",
    },
    aspectRatio: {
      control: "select",
      options: ["square", "portrait", "landscape"],
      description: "Card aspect ratio",
    },
    onImageExpand: {
      description: "Callback when an image is expanded",
      action: "image-expanded",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample security data with high-quality mock images
const samplePassportData: PassportSecurityData = {
  type: "passport",
  documentId: "P123456789",
  fullName: "Dr. Sarah J. Anderson",
  expirationDate: "2030-03-14",
  verificationStatus: "verified",
  nationality: "United States",
  images: {
    photo: {
      url: "https://via.placeholder.com/400x250/1e40af/ffffff?text=Passport+Photo",
      alt: "Passport photo of Dr. Sarah Anderson",
      type: "photo",
    },
    signature: {
      url: "https://via.placeholder.com/400x250/7c3aed/ffffff?text=Signature+Page",
      alt: "Signature page of Dr. Sarah Anderson",
      type: "photo",
    },
  },
};

const sampleGovernmentIdData: GovernmentIdSecurityData = {
  type: "government-id",
  documentId: "ID987654321",
  fullName: "Michael Chen",
  expirationDate: "2028-07-21",
  verificationStatus: "verified",
  issuingState: "California",
  images: {
    front: {
      url: "https://via.placeholder.com/400x250/059669/ffffff?text=CA+ID+Front",
      alt: "Front of California ID",
      type: "front",
    },
    back: {
      url: "https://via.placeholder.com/400x250/dc2626/ffffff?text=CA+ID+Back",
      alt: "Back of California ID",
      type: "back",
    },
  },
};

const sampleDriverLicenseData: DriverLicenseSecurityData = {
  type: "driver-license",
  documentId: "DL123456789",
  fullName: "Jennifer Rodriguez",
  expirationDate: "2026-11-02",
  verificationStatus: "verified",
  licenseClass: "Class C",
  issuingState: "Texas",
  restrictions: ["Corrective Lenses"],
  images: {
    front: {
      url: "https://via.placeholder.com/400x250/ea580c/ffffff?text=TX+DL+Front",
      alt: "Front of Texas Driver's License",
      type: "front",
    },
    back: {
      url: "https://via.placeholder.com/400x250/7c2d12/ffffff?text=TX+DL+Back",
      alt: "Back of Texas Driver's License",
      type: "back",
    },
  },
};

// Mock image expand handler
const handleImageExpand = (image: SecurityImage) => {
  alert(`Expanding image: ${image.alt}`);
};

export const Default: Story = {
  args: {
    type: DocumentType.DRIVER_LICENSE,
    provider: "John Doe",
    authorized: true,
    onImageExpand: handleImageExpand,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Default flip card with basic provider information and driver license type.",
      },
    },
  },
};

export const DriverLicense: Story = {
  args: {
    type: DocumentType.DRIVER_LICENSE,
    authorized: true,
    securityData: sampleDriverLicenseData,
    provider: "Jennifer Rodriguez",
    size: "md",
    onImageExpand: handleImageExpand,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Driver's license example with complete security data including state, class, and restrictions information.",
      },
    },
  },
};

export const Passport: Story = {
  args: {
    type: DocumentType.PASSPORT,
    authorized: true,
    securityData: samplePassportData,
    provider: "Michael Chen",
    size: "md",
    onImageExpand: handleImageExpand,
  },
  parameters: {
    docs: {
      description: {
        story: "Passport example with nationality and personal information.",
      },
    },
  },
};

export const GovernmentId: Story = {
  args: {
    type: DocumentType.GOVERNMENT_ID,
    authorized: true,
    securityData: sampleGovernmentIdData,
    provider: "Sarah Johnson",
    size: "md",
    onImageExpand: handleImageExpand,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Government ID example with state information and front/back images.",
      },
    },
  },
};

export const Unauthorized: Story = {
  args: {
    type: DocumentType.DRIVER_LICENSE,
    authorized: false,
    securityData: sampleDriverLicenseData,
    provider: "Restricted User",
    size: "md",
    onImageExpand: handleImageExpand,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example showing access denied state when user is not authorized to view security information.",
      },
    },
  },
};

export const DifferentSizes: Story = {
  args: {
    type: DocumentType.DRIVER_LICENSE,
    authorized: true,
    securityData: sampleDriverLicenseData,
    provider: "Jennifer Rodriguez",
    size: "lg",
    onImageExpand: handleImageExpand,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Comparison of different card sizes - all maintain aspect-portrait ratio and quad-grid layout.",
      },
    },
  },
};
