import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { SecurityImage } from "@/ui/blocks/security-id/types";

import SecurityIdImage from "@/ui/blocks/security-id/security-id-image";

// Mock images for stories
const mockFrontImage: SecurityImage = {
  url: "https://via.placeholder.com/400x250/3b82f6/ffffff?text=Government+ID+Front",
  alt: "Front side of government ID",
  type: "front",
};

const mockBackImage: SecurityImage = {
  url: "https://via.placeholder.com/400x250/ef4444/ffffff?text=Government+ID+Back",
  alt: "Back side of government ID",
  type: "back",
};

const mockPassportPhoto: SecurityImage = {
  url: "https://via.placeholder.com/400x250/1e40af/ffffff?text=Passport+Photo",
  alt: "Passport photo page",
  type: "photo",
};

const meta: Meta<typeof SecurityIdImage> = {
  title: "Blocks/Security ID/SecurityIdImage",
  component: SecurityIdImage,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A simplified security ID image tile component that displays a title (Front/Back) and 'Click to expand' text. When clicked, opens a clean dialog showing just the image with screen reader accessible title and description.",
      },
    },
  },
  argTypes: {
    title: {
      control: "text",
      description: "Title text displayed at the top (e.g., 'Front', 'Back')",
    },
    image: {
      description: "Image data to display in the dialog",
      control: false,
    },
    onImageExpand: {
      description: "Callback when image is clicked for expansion",
      action: "image-expanded",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Front: Story = {
  args: {
    title: "Front",
    image: mockFrontImage,
  },
  parameters: {
    docs: {
      description: {
        story: "Front image tile that opens a dialog when clicked.",
      },
    },
  },
};

export const Back: Story = {
  args: {
    title: "Back",
    image: mockBackImage,
  },
  parameters: {
    docs: {
      description: {
        story: "Back image tile that opens a dialog when clicked.",
      },
    },
  },
};

export const PassportPhoto: Story = {
  args: {
    title: "Photo",
    image: mockPassportPhoto,
  },
  parameters: {
    docs: {
      description: {
        story: "Passport photo tile with different image type.",
      },
    },
  },
};

export const WithoutImage: Story = {
  args: {
    title: "Front",
    image: undefined,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Tile without an image - shows the same layout but is not clickable.",
      },
    },
  },
};

export const CustomTitle: Story = {
  args: {
    title: "Signature",
    image: {
      url: "https://via.placeholder.com/400x250/7c3aed/ffffff?text=Signature+Page",
      alt: "Passport signature page",
      type: "photo",
    },
  },
  parameters: {
    docs: {
      description: {
        story: "Example with custom title for passport signature page.",
      },
    },
  },
};
