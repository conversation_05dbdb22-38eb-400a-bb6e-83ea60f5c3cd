import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { SecurityIdPreviewSize } from "@/ui/blocks/security-id/security-id-preview";

import SecurityIdPreview from "@/ui/blocks/security-id/security-id-preview";

const meta: Meta<typeof SecurityIdPreview> = {
  title: "Blocks/Security ID/SecurityIdPreview",
  component: SecurityIdPreview,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A preview component for security ID information with responsive t-shirt sizing. Displays name, document ID, and optional expiration date with consistent scaling across all sizes.",
      },
    },
  },
  argTypes: {
    size: {
      description: "T-shirt size for the component",
      control: { type: "select" },
      options: ["xs", "sm", "md", "lg", "xl"],
    },
    name: {
      description: "The name to display",
      control: { type: "text" },
    },
    documentId: {
      description: "The document ID to display",
      control: { type: "text" },
    },
    expiration: {
      description: "Optional expiration date",
      control: { type: "text" },
    },
    className: {
      description: "Additional CSS classes",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof SecurityIdPreview>;

// Basic size variations
export const ExtraSmall: Story = {
  args: {
    size: "xs",
    name: "Dr. Sarah Johnson",
    documentId: "MD-NY-123456789",
    expiration: "2025-12-31",
  },
};

export const Small: Story = {
  args: {
    size: "sm",
    name: "Dr. Sarah Johnson",
    documentId: "MD-NY-123456789",
    expiration: "2025-12-31",
  },
};

export const Medium: Story = {
  args: {
    size: "md",
    name: "Dr. Sarah Johnson",
    documentId: "MD-NY-123456789",
    expiration: "2025-12-31",
  },
};

export const Large: Story = {
  args: {
    size: "lg",
    name: "Dr. Sarah Johnson",
    documentId: "MD-NY-123456789",
    expiration: "2025-12-31",
  },
};

export const ExtraLarge: Story = {
  args: {
    size: "xl",
    name: "Dr. Sarah Johnson",
    documentId: "MD-NY-123456789",
    expiration: "2025-12-31",
  },
};

// Without expiration
export const WithoutExpiration: Story = {
  args: {
    size: "md",
    name: "Dr. Sarah Johnson",
    documentId: "MD-NY-123456789",
  },
};

// Long content variations
export const LongName: Story = {
  args: {
    size: "md",
    name: "Dr. Alexander Montgomery Fitzgerald III",
    documentId: "MD-NY-123456789",
    expiration: "2025-12-31",
  },
};

export const LongDocumentId: Story = {
  args: {
    size: "md",
    name: "Dr. Sarah Johnson",
    documentId: "RN-CALIFORNIA-EXTENDED-123456789",
    expiration: "2025-12-31",
  },
};

// Different content types
export const NurseLicense: Story = {
  args: {
    size: "md",
    name: "Jennifer Thompson, RN",
    documentId: "RN-TX-456789123",
    expiration: "2025-06-30",
  },
};

export const ShortContent: Story = {
  args: {
    size: "md",
    name: "Dr. Li Wei",
    documentId: "MD-123",
    expiration: "2024-12-31",
  },
};

// Custom styling
export const CustomStyling: Story = {
  args: {
    size: "md",
    name: "Dr. Sarah Johnson",
    documentId: "MD-NY-123456789",
    expiration: "2025-12-31",
    className: "border-blue-300 bg-blue-50/50",
  },
};

// Size comparison
export const AllSizes: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 p-8">
      <div className="grid grid-cols-2 items-start gap-4 md:grid-cols-3 lg:grid-cols-5">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">XS</h3>
          <SecurityIdPreview
            size="xs"
            name="Dr. Sarah Johnson"
            documentId="MD-NY-123456789"
            expiration="2025-12-31"
          />
        </div>
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">SM</h3>
          <SecurityIdPreview
            size="sm"
            name="Dr. Sarah Johnson"
            documentId="MD-NY-123456789"
            expiration="2025-12-31"
          />
        </div>
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">MD</h3>
          <SecurityIdPreview
            size="md"
            name="Dr. Sarah Johnson"
            documentId="MD-NY-123456789"
            expiration="2025-12-31"
          />
        </div>
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">LG</h3>
          <SecurityIdPreview
            size="lg"
            name="Dr. Sarah Johnson"
            documentId="MD-NY-123456789"
            expiration="2025-12-31"
          />
        </div>
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">XL</h3>
          <SecurityIdPreview
            size="xl"
            name="Dr. Sarah Johnson"
            documentId="MD-NY-123456789"
            expiration="2025-12-31"
          />
        </div>
      </div>
    </div>
  ),
};

// Content variations across sizes
export const ContentVariations: Story = {
  render: () => (
    <div className="space-y-8 p-8">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Standard Content</h3>
          <div className="space-y-3">
            <SecurityIdPreview
              size="sm"
              name="Dr. Sarah Johnson"
              documentId="MD-NY-123456789"
              expiration="2025-12-31"
            />
            <SecurityIdPreview
              size="md"
              name="Dr. Sarah Johnson"
              documentId="MD-NY-123456789"
              expiration="2025-12-31"
            />
            <SecurityIdPreview
              size="lg"
              name="Dr. Sarah Johnson"
              documentId="MD-NY-123456789"
              expiration="2025-12-31"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Long Names</h3>
          <div className="space-y-3">
            <SecurityIdPreview
              size="sm"
              name="Dr. Alexander Montgomery Fitzgerald III"
              documentId="MD-NY-123456789"
              expiration="2025-12-31"
            />
            <SecurityIdPreview
              size="md"
              name="Dr. Alexander Montgomery Fitzgerald III"
              documentId="MD-NY-123456789"
              expiration="2025-12-31"
            />
            <SecurityIdPreview
              size="lg"
              name="Dr. Alexander Montgomery Fitzgerald III"
              documentId="MD-NY-123456789"
              expiration="2025-12-31"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">No Expiration</h3>
          <div className="space-y-3">
            <SecurityIdPreview
              size="sm"
              name="Dr. Sarah Johnson"
              documentId="MD-NY-123456789"
            />
            <SecurityIdPreview
              size="md"
              name="Dr. Sarah Johnson"
              documentId="MD-NY-123456789"
            />
            <SecurityIdPreview
              size="lg"
              name="Dr. Sarah Johnson"
              documentId="MD-NY-123456789"
            />
          </div>
        </div>
      </div>
    </div>
  ),
};

// Responsive layout examples
export const ResponsiveGrid: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-4 p-8 sm:grid-cols-2 lg:grid-cols-3">
      <SecurityIdPreview
        size="md"
        name="Dr. Sarah Johnson"
        documentId="MD-NY-123456789"
        expiration="2025-12-31"
      />
      <SecurityIdPreview
        size="md"
        name="Jennifer Thompson, RN"
        documentId="RN-TX-456789123"
        expiration="2025-06-30"
      />
      <SecurityIdPreview
        size="md"
        name="Dr. Michael Chen"
        documentId="MD-CA-987654321"
        expiration="2024-08-15"
      />
      <SecurityIdPreview
        size="md"
        name="Dr. Emily Rodriguez"
        documentId="MD-FL-555666777"
        expiration="2026-03-20"
      />
      <SecurityIdPreview
        size="md"
        name="Dr. Kumar Patel"
        documentId="MD-TX-111222333"
        expiration="2025-09-15"
      />
      <SecurityIdPreview
        size="md"
        name="Dr. Lisa Wang"
        documentId="MD-WA-777888999"
        expiration="2025-11-30"
      />
    </div>
  ),
};

// Interactive playground
export const Playground: Story = {
  args: {
    size: "md",
    name: "Dr. Sarah Johnson",
    documentId: "MD-NY-123456789",
    expiration: "2025-12-31",
  },
};
