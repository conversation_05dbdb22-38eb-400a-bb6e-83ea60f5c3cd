import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { useState } from "react";

import type { SecurityAuditLog } from "@/ui/blocks/security-id/security-audit-logs";

import SecurityAuditLogs from "@/ui/blocks/security-id/security-audit-logs";

const meta: Meta<typeof SecurityAuditLogs> = {
  title: "Blocks/Security ID/SecurityAuditLogs",
  component: SecurityAuditLogs,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
A comprehensive security audit logging component for tracking access events and violations.

Features:
- **Event Tracking** - Logs access attempts, flip attempts, security violations, and auto-hide events
- **Authorization Status** - Tracks both authorized and unauthorized actions
- **Time-based Grouping** - Groups events by date for better organization
- **Summary Statistics** - Shows total, authorized, and unauthorized event counts
- **Metadata Support** - Expandable metadata for detailed forensic analysis
- **Auto-opening Support** - Can be controlled for automatic display in critical scenarios

⚠️ **Security Notice**: This component is designed for security auditing and compliance monitoring.
        `,
      },
    },
  },
  argTypes: {
    logs: {
      description: "Array of security audit log entries",
    },
    maxLogs: {
      control: { type: "number", min: 10, max: 100, step: 10 },
      description: "Maximum number of logs to display",
    },
    title: {
      control: "text",
      description: "Dialog title",
    },
    description: {
      control: "text",
      description: "Dialog description",
    },
    showClearButton: {
      control: "boolean",
      description: "Show button to clear all logs",
    },
    open: {
      control: "boolean",
      description: "Controls dialog open state",
    },
  },
};

export default meta;
type Story = StoryObj<typeof SecurityAuditLogs>;

// Mock data generators
const generateMockLog = (
  id: string,
  event: SecurityAuditLog["event"],
  authorized: boolean,
  timestamp: Date,
  userId?: string,
  violation?: string,
  metadata?: Record<string, unknown>,
): SecurityAuditLog => ({
  id,
  event,
  authorized,
  timestamp,
  userId,
  violation,
  metadata,
});

// Sample logs with various scenarios
const sampleLogs: SecurityAuditLog[] = [
  generateMockLog(
    "1",
    "access_attempt",
    true,
    new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    "user-123",
    undefined,
    { ip: "*************", userAgent: "Mozilla/5.0..." },
  ),
  generateMockLog(
    "2",
    "flip_attempt",
    true,
    new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
    "user-123",
    undefined,
    { previousState: "front", newState: "back" },
  ),
  generateMockLog(
    "3",
    "access_attempt",
    false,
    new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
    "user-456",
    undefined,
    { ip: "*********", reason: "insufficient_permissions" },
  ),
  generateMockLog(
    "4",
    "security_violation",
    false,
    new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    "user-789",
    "repeated_unauthorized_access",
    { attempts: 5, timeWindow: "5 minutes", blocked: true },
  ),
  generateMockLog(
    "5",
    "auto_hide",
    true,
    new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
    "user-123",
    undefined,
    { timeout: 15000, reason: "inactivity" },
  ),
  generateMockLog(
    "6",
    "flip_attempt",
    false,
    new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    "user-999",
    undefined,
    { reason: "unauthorized", attemptedState: "back" },
  ),
];

// Logs from multiple days
const multiDayLogs: SecurityAuditLog[] = [
  ...sampleLogs,
  generateMockLog(
    "7",
    "access_attempt",
    true,
    new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    "user-111",
  ),
  generateMockLog(
    "8",
    "security_violation",
    false,
    new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    "user-222",
    "brute_force_attempt",
  ),
  generateMockLog(
    "9",
    "access_attempt",
    true,
    new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 days ago
    "user-333",
  ),
];

// Large dataset for performance testing
const largeLogs: SecurityAuditLog[] = Array.from({ length: 75 }, (_, i) => {
  const events: SecurityAuditLog["event"][] = [
    "access_attempt",
    "flip_attempt",
    "security_violation",
    "auto_hide",
  ];
  const event = events[i % events.length]!;
  const authorized = Math.random() > 0.3; // 70% authorized
  const timestamp = new Date(Date.now() - 1000 * 60 * i); // Spread over time

  return generateMockLog(
    `perf-${i}`,
    event,
    authorized,
    timestamp,
    `user-${Math.floor(Math.random() * 100)}`,
    !authorized && event === "security_violation"
      ? "automated_violation_detected"
      : undefined,
    { automated: true, index: i },
  );
});

// Controlled state wrapper for stories that need state management
const ControlledAuditLogs = ({ defaultOpen = false, ...props }: any) => {
  const [open, setOpen] = useState(defaultOpen);
  return <SecurityAuditLogs {...props} open={open} onOpenChange={setOpen} />;
};

export const Default: Story = {
  args: {
    logs: sampleLogs,
    showClearButton: false,
  },
};

export const AutoOpened: Story = {
  render: (args) => <ControlledAuditLogs {...args} defaultOpen={true} />,
  args: {
    logs: sampleLogs,
    showClearButton: true,
    title: "Security Incident Review",
    description: "Critical security events requiring immediate attention",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Dialog automatically opens to show critical security events. Use this pattern for high-priority alerts.",
      },
    },
  },
};

export const EmptyLogs: Story = {
  render: (args) => <ControlledAuditLogs {...args} defaultOpen={true} />,
  args: {
    logs: [],
    title: "Clean Security Record",
    description: "No security events have been logged",
  },
  parameters: {
    docs: {
      description: {
        story: "Empty state when no security events have been recorded.",
      },
    },
  },
};

export const SecurityViolations: Story = {
  render: (args) => <ControlledAuditLogs {...args} defaultOpen={true} />,
  args: {
    logs: [
      generateMockLog(
        "v1",
        "security_violation",
        false,
        new Date(Date.now() - 1000 * 60 * 5),
        "user-malicious",
        "sql_injection_attempt",
        {
          query: "'; DROP TABLE users; --",
          blocked: true,
          severity: "critical",
        },
      ),
      generateMockLog(
        "v2",
        "security_violation",
        false,
        new Date(Date.now() - 1000 * 60 * 10),
        "user-malicious",
        "brute_force_login",
        { attempts: 50, timeframe: "2 minutes", ip: "*************" },
      ),
      generateMockLog(
        "v3",
        "access_attempt",
        false,
        new Date(Date.now() - 1000 * 60 * 15),
        "user-suspicious",
        undefined,
        { reason: "account_locked", previousAttempts: 10 },
      ),
    ],
    title: "⚠️ Critical Security Violations",
    description:
      "Immediate action required - multiple security breaches detected",
    showClearButton: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "High-priority security violations requiring immediate attention. Auto-opens for critical incidents.",
      },
    },
  },
};

export const MultiDay: Story = {
  render: (args) => <ControlledAuditLogs {...args} defaultOpen={true} />,
  args: {
    logs: multiDayLogs,
    title: "Multi-Day Security Report",
    description: "Security events grouped by date over multiple days",
    showClearButton: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Logs spanning multiple days, demonstrating date-based grouping functionality.",
      },
    },
  },
};

export const HighVolume: Story = {
  render: (args) => <ControlledAuditLogs {...args} defaultOpen={true} />,
  args: {
    logs: largeLogs,
    maxLogs: 50,
    title: "High-Volume Security Monitoring",
    description: "Large dataset with performance optimizations",
    showClearButton: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Performance test with large dataset. Shows pagination and efficient rendering.",
      },
    },
  },
};

export const AuthorizedOnly: Story = {
  render: (args) => <ControlledAuditLogs {...args} defaultOpen={true} />,
  args: {
    logs: sampleLogs.filter((log) => log.authorized),
    title: "✅ Authorized Access Log",
    description: "Successfully authorized security events",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Filtered view showing only authorized events for compliance reporting.",
      },
    },
  },
};

export const UnauthorizedOnly: Story = {
  render: (args) => <ControlledAuditLogs {...args} defaultOpen={true} />,
  args: {
    logs: sampleLogs.filter((log) => !log.authorized),
    title: "🚨 Unauthorized Access Attempts",
    description: "Security violations and blocked access attempts",
    showClearButton: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Filtered view showing only unauthorized events for security analysis.",
      },
    },
  },
};

export const WithMetadata: Story = {
  render: (args) => <ControlledAuditLogs {...args} defaultOpen={true} />,
  args: {
    logs: [
      generateMockLog(
        "meta1",
        "access_attempt",
        true,
        new Date(Date.now() - 1000 * 60 * 5),
        "user-analyst",
        undefined,
        {
          sessionId: "sess_abc123",
          ip: "*************",
          userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
          geolocation: {
            country: "US",
            city: "San Francisco",
            lat: 37.7749,
            lng: -122.4194,
          },
          mfaUsed: true,
          deviceFingerprint: "fp_xyz789",
        },
      ),
      generateMockLog(
        "meta2",
        "flip_attempt",
        true,
        new Date(Date.now() - 1000 * 60 * 10),
        "user-analyst",
        undefined,
        {
          cardType: "passport",
          flipDuration: 850,
          previousView: "front",
          newView: "back",
          scrollPosition: 0.35,
          timestamp: Date.now(),
        },
      ),
    ],
    title: "Detailed Forensic Analysis",
    description: "Comprehensive metadata for security investigation",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Rich metadata examples for forensic analysis and detailed security auditing.",
      },
    },
  },
};
