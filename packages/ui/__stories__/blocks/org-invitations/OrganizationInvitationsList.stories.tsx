import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { Trash2 } from "lucide-react";

import type { GenericInvitation } from "@/ui/blocks/org-invitations/types";

import {
  createInvitationAdapter,
  createInvitationMenu,
  InvitationsList,
} from "@/ui/blocks/org-invitations";

const meta: Meta<typeof InvitationsList> = {
  title: "Blocks/Organization Invitations/OrganizationInvitationsList",
  component: InvitationsList,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A flexible, reusable component for displaying organization invitations with support for pagination, custom rendering, and actions.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    gridCols: {
      control: { type: "select" },
      options: [1, 2, 3, 4],
      description: "Number of grid columns for invitation cards",
    },
    loading: {
      control: "boolean",
      description: "Whether the component is in a loading state",
    },
    total: {
      control: "number",
      description: "Total number of invitations",
    },
  },
};

export default meta;

type Story = StoryObj<typeof InvitationsList>;

// Helper function to create mock invitations
function createMockInvitation(
  overrides?: Partial<GenericInvitation>,
): GenericInvitation {
  const statuses = ["pending", "accepted", "revoked"] as const;
  const baseInvitation: GenericInvitation = {
    id: Math.random().toString(36).substring(2, 11),
    emailAddress: `user${Math.floor(Math.random() * 1000)}@example.com`,
    role: Math.random() > 0.5 ? "org:admin" : "org:member",
    status: statuses[Math.floor(Math.random() * statuses.length)] || "pending",
    createdAt: new Date(
      Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
    ).toISOString(),
  };

  return { ...baseInvitation, ...overrides };
}

function createMockInvitations(count: number): GenericInvitation[] {
  return Array.from({ length: count }, () => createMockInvitation());
}

// Example domain-specific invitation type
interface ExampleOrgInvitation {
  id: string;
  emailAddress: string;
  role: "org:admin" | "org:member";
  status: "pending" | "accepted" | "revoked";
  createdAt: string;
  invitedBy: string;
}

function createMockOrgInvitation(): ExampleOrgInvitation {
  return {
    id: Math.random().toString(36).substring(2, 11),
    emailAddress: `user${Math.floor(Math.random() * 1000)}@company.com`,
    role: Math.random() > 0.3 ? "org:member" : "org:admin",
    status: "pending",
    createdAt: new Date().toISOString(),
    invitedBy: "<EMAIL>",
  };
}

// Example invitation menu using the utility
const exampleInvitationMenu = createInvitationMenu<GenericInvitation>([
  {
    label: "Resend Invitation",
    onClick: (invitation) => {
      console.log("Resending invitation to:", invitation.emailAddress);
    },
  },
  {
    label: "Revoke Invitation",
    icon: Trash2,
    onClick: (invitation) => {
      console.log("Revoking invitation for:", invitation.emailAddress);
    },
    variant: "destructive",
    confirmMessage: (invitation) =>
      `Are you sure you want to revoke the invitation for ${invitation.emailAddress}?`,
  },
]);

// Default story
export const Default: Story = {
  args: {
    invitations: createMockInvitations(6),
    total: 6,
    title: "Pending Invitations",
    description: "Manage organization invites and access requests",
    gridCols: 2,
  },
};

// Loading state
export const Loading: Story = {
  args: {
    invitations: [],
    total: 0,
    loading: true,
    title: "Pending Invitations",
    description: "Manage organization invites and access requests",
  },
};

// Empty state
export const Empty: Story = {
  args: {
    invitations: [],
    total: 0,
    title: "Pending Invitations",
    description: "Manage organization invites and access requests",
    emptyMessage:
      "No pending invitations. Send your first invitation to get started!",
  },
};

// Error state
export const ErrorState: Story = {
  args: {
    invitations: [],
    total: 0,
    error: new Error("Failed to load invitations. Please try again."),
    title: "Pending Invitations",
    description: "Manage organization invites and access requests",
  },
};

// With custom actions
export const WithActions: Story = {
  args: {
    invitations: createMockInvitations(4),
    total: 4,
    title: "Pending Invitations",
    description: "Manage organization invites and access requests",
    onInviteClick: () => {
      console.log("Invite button clicked");
    },
    inviteButtonLabel: "Invite Member",
  },
};

// With invitation menu
export const WithInvitationMenu: Story = {
  args: {
    invitations: createMockInvitations(4),
    total: 4,
    title: "Pending Invitations",
    description: "Manage organization invites and access requests",
    renderInvitationMenu: exampleInvitationMenu,
  },
};

// With filters
export const WithFilters: Story = {
  args: {
    invitations: createMockInvitations(8),
    total: 8,
    title: "User Invitations",
    description: "Manage pending user invitations and access requests",
    renderInvitationMenu: exampleInvitationMenu,
    onInviteClick: () => {
      console.log("Invite button clicked");
    },
    inviteButtonLabel: "Invite User",
    filters: [
      {
        id: "status",
        label: "Status",
        options: [
          { label: "All Statuses", value: null },
          { label: "Pending", value: "pending" },
          { label: "Accepted", value: "accepted" },
          { label: "Revoked", value: "revoked" },
          { label: "Expired", value: "expired" },
        ],
      },
      {
        id: "role",
        label: "Role",
        options: [
          { label: "All Roles", value: null },
          { label: "Admin", value: "admin" },
          { label: "Member", value: "member" },
          { label: "Billing", value: "billing" },
        ],
      },
    ],
    searchPlaceholder: "Search invitations by email...",
    searchNamespace: "invitations-demo",
  },
};

// Different grid layouts
export const SingleColumn: Story = {
  args: {
    invitations: createMockInvitations(3),
    total: 3,
    title: "Pending Invitations",
    description: "Single column layout",
    gridCols: 1,
  },
};

export const ThreeColumns: Story = {
  args: {
    invitations: createMockInvitations(9),
    total: 9,
    title: "Pending Invitations",
    description: "Three column layout",
    gridCols: 3,
  },
};

// With pagination
export const WithPagination: Story = {
  args: {
    invitations: createMockInvitations(4),
    total: 23,
    title: "Pending Invitations",
    description: "Showing paginated results",
    pagination: {
      pageIndex: 0,
      pageSize: 4,
    },
    onPaginationChange: (pagination) => {
      console.log("Pagination changed:", pagination);
    },
    itemNoun: { singular: "invitation", plural: "invitations" },
  },
};

// Using the adapter pattern with domain-specific data
export const WithAdapter: Story = {
  render: () => {
    // Create mock domain-specific data
    const orgInvitations = Array.from({ length: 5 }, () =>
      createMockOrgInvitation(),
    );

    // Adapt to generic format
    const adaptedInvitations = orgInvitations.map((invitation) =>
      createInvitationAdapter(invitation, {
        getId: (i) => i.id,
        getEmailAddress: (i) => i.emailAddress,
        getRole: (i) => i.role,
        getStatus: (i) => i.status,
        getCreatedAt: (i) => i.createdAt,
        getMetadata: (i) => ({ invitedBy: i.invitedBy }),
      }),
    );

    return (
      <InvitationsList
        invitations={adaptedInvitations}
        total={adaptedInvitations.length}
        title="Organization Invitations"
        description="Using adapter pattern with domain-specific data"
        renderInvitationMenu={exampleInvitationMenu}
        onInviteClick={() => {
          console.log("Invite button clicked");
        }}
        inviteButtonLabel="Send Invitation"
        onInvitationClick={(invitation) => {
          console.log("Clicked invitation:", invitation);
          // Access original data: invitation.originalData
        }}
      />
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example showing how to use the adapter pattern to transform domain-specific data into the generic format.",
      },
    },
  },
};

// Mixed invitation statuses
export const MixedStatuses: Story = {
  args: {
    invitations: [
      createMockInvitation({
        emailAddress: "<EMAIL>",
        role: "org:admin",
        status: "pending",
      }),
      createMockInvitation({
        emailAddress: "<EMAIL>",
        role: "org:member",
        status: "accepted",
      }),
      createMockInvitation({
        emailAddress: "<EMAIL>",
        role: "org:member",
        status: "revoked",
      }),
      createMockInvitation({
        emailAddress: "<EMAIL>",
        role: "org:member",
        status: "expired",
      }),
    ],
    total: 4,
    title: "Mixed Invitation Statuses",
    description: "Example showing different invitation statuses",
  },
};
