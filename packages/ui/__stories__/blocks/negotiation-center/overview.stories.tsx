import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import {
  JobCard,
  ProviderCard,
  RateInsightsPanel,
} from "@/ui/blocks/negotiation-center";

interface OverviewProps {
  providerName: string;
  providerAvatar: string;
  providerSpecialty: string;
  providerExperience: string;
  jobSpecialty: string;
  jobLocation: string;
  jobSchedule: string;
  currentRate: number;
  rateComparison: string;
  demandLevel: "low" | "medium" | "high";
  loading?: boolean;
}

function NegotiationCenterOverview({
  providerName,
  providerAvatar,
  providerSpecialty,
  providerExperience,
  jobSpecialty,
  jobLocation,
  jobSchedule,
  currentRate,
  rateComparison,
  demandLevel,
  loading = false,
}: OverviewProps) {
  return (
    <div className="space-y-8 bg-background p-6">
      <div className="space-y-4">
        <h1 className="text-2xl font-bold text-foreground">
          Negotiation Center Components
        </h1>
        <p className="text-muted-foreground">
          All the individual components that make up the negotiation center
          cockpit layout. Now featuring proper theme color integration with
          <code className="mx-1 rounded bg-muted px-1 text-sm">
            border-border
          </code>
          ,<code className="mx-1 rounded bg-muted px-1 text-sm">bg-card</code>,
          <code className="mx-1 rounded bg-muted px-1 text-sm">
            text-foreground
          </code>
          , and
          <code className="mx-1 rounded bg-muted px-1 text-sm">
            text-muted-foreground
          </code>
          while preserving teal specialty colors for branding. Each component
          includes tailored skeleton loading states for optimal user experience.
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Provider Card */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-foreground">
            Provider Card
          </h2>
          <ProviderCard
            name={providerName}
            avatar={providerAvatar}
            specialty={providerSpecialty}
            experience={providerExperience}
            onViewProfile={() => alert("Viewing provider profile")}
            loading={loading}
          />
        </div>

        {/* Job Card */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-foreground">Job Card</h2>
          <JobCard
            specialty={jobSpecialty}
            location={{
              id: "loc-1",
              name: jobLocation,
              address: {
                formatted: jobLocation,
              },
            }}
            schedule={jobSchedule}
            onViewDetails={() => alert("Viewing job details")}
            loading={loading}
          />
        </div>

        {/* Rate Insights Panel - Takes full width */}
        <div className="space-y-2 lg:col-span-2">
          <h2 className="text-lg font-semibold text-foreground">
            Rate Insights Panel
          </h2>
          <RateInsightsPanel
            currentRate={currentRate}
            marketComparison={{
              percentile: 50,
              difference: 10,
              trend: "above",
            }}
            demandMetrics={{
              level: demandLevel,
              score: 50,
              trend: "up",
            }}
            rateHistory={[]}
            actionItems={[]}
            loading={loading}
          />
        </div>
      </div>

      <div className="rounded-lg border border-border bg-card p-4">
        <h3 className="mb-2 font-semibold text-foreground">
          Theme Integration Features
        </h3>
        <ul className="space-y-1 text-sm text-muted-foreground">
          <li>✅ Uses semantic color tokens for better theme support</li>
          <li>✅ Preserves teal specialty colors for visual hierarchy</li>
          <li>✅ Consistent with design system patterns</li>
          <li>✅ Works in both light and dark themes</li>
          <li>✅ No hardcoded gray values - all themeable</li>
          <li>✅ Built-in skeleton loading states for each component</li>
        </ul>
      </div>
    </div>
  );
}

const meta: Meta<typeof NegotiationCenterOverview> = {
  title: "Blocks/Negotiation Center/Overview",
  component: NegotiationCenterOverview,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
## Theme-Integrated Negotiation Center Components

An overview of all negotiation center components showcasing:

### 🎨 **Design System Integration**
- **Semantic Colors**: Uses \`border-border\`, \`bg-card\`, \`text-foreground\`, \`text-muted-foreground\`
- **Specialty Colors**: Preserves teal for branding and visual hierarchy  
- **Theme Support**: Works seamlessly with light and dark themes
- **Consistency**: Follows design system patterns across all components

### ⏳ **Loading States**
- **Tailored Skeletons**: Each component has custom skeleton shapes
- **Realistic Placeholders**: Skeletons match the actual content structure
- **Performance**: Lightweight loading states while data loads
- **UX Enhancement**: Smooth loading experience with visual feedback

### 🏗️ **Component Architecture**
- **Modular Design**: Each component can be used independently
- **Event Handling**: Optional callbacks for interactive elements
- **Responsive**: All components adapt to different screen sizes
- **Accessible**: Proper semantic markup and ARIA support

### 🔧 **Developer Experience**
- **Type Safety**: Full TypeScript support with proper interfaces
- **Customizable**: Flexible props for different use cases
- **Documented**: Comprehensive Storybook documentation
- **Tested**: Interactive examples with real event handling
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    providerName: {
      control: "text",
      description: "Provider's name",
    },
    providerAvatar: {
      control: "text",
      description: "Provider's avatar URL",
    },
    providerSpecialty: {
      control: "text",
      description: "Provider's medical specialty",
    },
    providerExperience: {
      control: "text",
      description: "Provider's experience level",
    },
    jobSpecialty: {
      control: "text",
      description: "Job specialty",
    },
    jobLocation: {
      control: "text",
      description: "Job location",
    },
    jobSchedule: {
      control: "text",
      description: "Job schedule",
    },
    currentRate: {
      control: "number",
      description: "Current hourly rate",
    },
    rateComparison: {
      control: "text",
      description: "Rate comparison text",
    },
    demandLevel: {
      control: "select",
      options: ["low", "medium", "high"],
      description: "Current demand level",
    },
    loading: {
      control: "boolean",
      description: "Show skeleton loading states for all components",
    },
  },
};

export default meta;

type Story = StoryObj<typeof NegotiationCenterOverview>;

export const AllComponents: Story = {
  args: {
    providerName: "Dr. Rose Chen",
    providerAvatar: "/placeholder.svg?height=48&width=48",
    providerSpecialty: "Emergency Medicine",
    providerExperience: "10 years",
    jobSpecialty: "Emergency Medicine",
    jobLocation: "Atlanta, GA",
    jobSchedule: "7:00 AM — 7:00 PM",
    currentRate: 120,
    rateComparison: "Above average",
    demandLevel: "medium",
    loading: false,
  },
};

export const LoadingStates: Story = {
  args: {
    ...AllComponents.args,
    loading: true,
  },
  parameters: {
    docs: {
      description: {
        story: `
**Inline Skeleton Loading States**

Demonstrates the improved loading approach using inline conditional rendering:

\`\`\`tsx
// Instead of separate loading trees:
if (loading) return <div>...skeleton tree...</div>
return <div>...content tree...</div>

// We use inline skeletons:
<div>
  {loading ? <Skeleton /> : <Content />}
  {loading ? <Skeleton /> : <Text />}
</div>
\`\`\`

**Benefits:**
- **Single Structure**: Same DOM whether loading or loaded
- **Clear Mapping**: Easy to see which elements get skeletons  
- **Performance**: No duplicate render trees
- **Maintainability**: One component structure to maintain
- **Debugging**: Consistent component hierarchy

Each component uses this pattern for optimal loading experience while maintaining code clarity.
        `,
      },
    },
  },
};

export const HighDemandScenario: Story = {
  args: {
    providerName: "Dr. Sarah Martinez",
    providerAvatar: "/avatars/02.png",
    providerSpecialty: "Cardiology",
    providerExperience: "15 years",
    jobSpecialty: "Interventional Cardiology",
    jobLocation: "Miami, FL",
    jobSchedule: "8:00 AM — 6:00 PM",
    currentRate: 180,
    rateComparison: "Well above average",
    demandLevel: "high",
  },
};

export const LowDemandScenario: Story = {
  args: {
    providerName: "Dr. Michael Johnson",
    providerAvatar: "/avatars/03.png",
    providerSpecialty: "Family Medicine",
    providerExperience: "5 years",
    jobSpecialty: "Primary Care",
    jobLocation: "Rural Montana",
    jobSchedule: "9:00 AM — 5:00 PM",
    currentRate: 85,
    rateComparison: "Below average",
    demandLevel: "low",
  },
};

export const DarkTheme: Story = {
  parameters: {
    backgrounds: { default: "dark" },
  },
  args: {
    ...AllComponents.args,
  },
  render: (args) => (
    <div className="dark">
      <NegotiationCenterOverview {...args} />
    </div>
  ),
};

export const DarkThemeLoading: Story = {
  parameters: {
    backgrounds: { default: "dark" },
  },
  args: {
    ...AllComponents.args,
    loading: true,
  },
  render: (args) => (
    <div className="dark">
      <NegotiationCenterOverview {...args} />
    </div>
  ),
};
