import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { JobCard } from "@/ui/blocks/negotiation-center/job-card";

const meta: Meta<typeof JobCard> = {
  title: "Blocks/Negotiation Center/Job Card",
  component: JobCard,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
A comprehensive job information card displaying all essential details about the position being negotiated.

### Features
- **Job Details**: Specialty, location with map preview, schedule, and employment information
- **Organization**: Hospital or clinic information with avatar
- **Location Preview**: Integrated map pin icon with formatted address
- **Salary & Benefits**: Compensation information and job highlights
- **Action Link**: View full details functionality
- **Loading State**: Skeleton animation while data loads
- **Theme Integration**: Uses semantic color tokens
- **Interactive**: Hover states and optional click handlers

### Usage
Used within the negotiation center cockpit to display comprehensive job information prominently.
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    specialty: {
      control: "text",
      description: "Medical specialty for the job",
    },
    location: {
      control: "object",
      description: "Job location with address details",
    },
    schedule: {
      control: "text",
      description: "Work schedule details",
    },
    jobType: {
      control: "text",
      description: "Type of job (e.g., Locum, Permanent)",
    },
    employmentType: {
      control: "text",
      description: "Employment type (e.g., Full-time, Part-time)",
    },
    salary: {
      control: "text",
      description: "Salary or compensation information",
    },
    highlights: {
      control: "object",
      description: "Array of job highlights or benefits",
    },
    organization: {
      control: "object",
      description: "Organization details with avatar",
    },
    loading: {
      control: "boolean",
      description: "Show skeleton loading state",
    },
  },
};

export default meta;

type Story = StoryObj<typeof JobCard>;

export const Default: Story = {
  args: {
    specialty: "Emergency Medicine",
    location: {
      id: "loc-1",
      name: "Emory University Hospital",
      address: {
        formatted: "1364 Clifton Road NE, Atlanta, GA 30322",
      },
    },
    schedule: "7:00 AM — 7:00 PM",
    jobType: "Locum",
    employmentType: "Full-time",
    salary: "$350,000 - $400,000",
    highlights: [
      "Comprehensive medical benefits",
      "Continuing education allowance",
      "Flexible scheduling options",
    ],
    organization: {
      id: "org-1",
      name: "Emory Healthcare",
      avatar: "/avatars/emory-healthcare.png",
      description: "Leading academic medical center",
    },
    onViewDetails: () => alert("Viewing job details"),
    loading: false,
  },
};

export const LoadingState: Story = {
  args: {
    ...Default.args,
    loading: true,
  },
  parameters: {
    docs: {
      description: {
        story: `
**Loading State**

Displays skeleton placeholders that match the component structure:
- Circular skeleton for the section icon
- Text skeleton for the specialty title
- Line skeletons for location and schedule
- Small skeleton for the "View details" link

Perfect for showing loading feedback while job data is being fetched.
        `,
      },
    },
  },
};

export const Cardiology: Story = {
  args: {
    specialty: "Interventional Cardiology",
    location: {
      id: "loc-2",
      name: "Miami Heart Institute",
      address: {
        formatted: "4300 Alton Rd, Miami Beach, FL 33140",
      },
    },
    schedule: "8:00 AM — 6:00 PM",
    jobType: "Permanent",
    employmentType: "Full-time",
    salary: "$450,000 - $500,000",
    highlights: [
      "State-of-the-art cardiac facilities",
      "Research opportunities",
      "Collaborative team environment",
    ],
    organization: {
      id: "org-2",
      name: "Baptist Health South Florida",
      avatar: "/avatars/baptist-health.png",
      description: "Premier cardiac care provider",
    },
    onViewDetails: () => alert("Viewing cardiology job details"),
  },
};

export const Surgery: Story = {
  args: {
    specialty: "Orthopedic Surgery",
    location: {
      id: "loc-3",
      name: "Northwestern Memorial Hospital",
      address: {
        formatted: "251 E Huron St, Chicago, IL 60611",
      },
    },
    schedule: "6:00 AM — 4:00 PM",
    jobType: "Locum",
    employmentType: "Full-time",
    salary: "$400,000 - $450,000",
    highlights: [
      "Level 1 trauma center",
      "Advanced surgical suites",
      "Competitive compensation",
    ],
    organization: {
      id: "org-3",
      name: "Northwestern Medicine",
      avatar: "/avatars/northwestern-medicine.png",
      description: "Academic medical center",
    },
    onViewDetails: () => alert("Viewing surgery job details"),
  },
};

export const FamilyMedicine: Story = {
  args: {
    specialty: "Family Medicine",
    location: {
      id: "loc-4",
      name: "Billings Rural Health Clinic",
      address: {
        formatted: "123 Rural Road, Billings, MT 59101",
      },
    },
    schedule: "9:00 AM — 5:00 PM",
    jobType: "Permanent",
    employmentType: "Full-time",
    salary: "$220,000 - $250,000",
    highlights: [
      "Serve rural communities",
      "Loan forgiveness program",
      "Family-friendly environment",
    ],
    organization: {
      id: "org-4",
      name: "Montana Rural Healthcare",
      avatar: "/avatars/montana-rural.png",
      description: "Community-focused healthcare",
    },
    onViewDetails: () => alert("Viewing family medicine job details"),
  },
};

export const NightShift: Story = {
  args: {
    specialty: "Emergency Medicine",
    location: {
      id: "loc-5",
      name: "Cedars-Sinai Medical Center",
      address: {
        formatted: "8700 Beverly Blvd, Los Angeles, CA 90048",
      },
    },
    schedule: "7:00 PM — 7:00 AM",
    jobType: "Locum",
    employmentType: "Full-time",
    salary: "$375,000 - $425,000",
    highlights: [
      "Night shift premium",
      "Emergency medicine excellence",
      "Urban trauma cases",
    ],
    organization: {
      id: "org-5",
      name: "Cedars-Sinai",
      avatar: "/avatars/cedars-sinai.png",
      description: "World-renowned medical center",
    },
    onViewDetails: () => alert("Viewing night shift job details"),
  },
};

export const MinimalInformation: Story = {
  args: {
    specialty: "Internal Medicine",
    schedule: "9:00 AM — 5:00 PM",
    onViewDetails: () => alert("Viewing minimal job details"),
  },
  parameters: {
    docs: {
      description: {
        story: `
**Minimal Information**

Shows the job card with only the essential information provided.
This demonstrates how the component gracefully handles optional props.
        `,
      },
    },
  },
};

export const WithoutOrganization: Story = {
  args: {
    specialty: "Radiology",
    location: {
      id: "loc-6",
      name: "Private Practice",
      address: {
        formatted: "456 Medical Plaza, Phoenix, AZ 85001",
      },
    },
    schedule: "8:00 AM — 5:00 PM",
    jobType: "Contract",
    employmentType: "Part-time",
    salary: "$280,000 - $320,000",
    highlights: [
      "Flexible hours",
      "Remote reading opportunities",
      "Modern equipment",
    ],
    onViewDetails: () => alert("Viewing radiology job details"),
  },
  parameters: {
    docs: {
      description: {
        story: `
**Without Organization**

Job card without organization information, showing how the component
adapts when organization data is not available.
        `,
      },
    },
  },
};

export const ManyHighlights: Story = {
  args: {
    specialty: "Anesthesiology",
    location: {
      id: "loc-7",
      name: "Mayo Clinic",
      address: {
        formatted: "200 First Street SW, Rochester, MN 55905",
      },
    },
    schedule: "Variable OR schedule",
    jobType: "Permanent",
    employmentType: "Full-time",
    salary: "$380,000 - $420,000",
    highlights: [
      "World-class surgical facilities",
      "Continuing education support",
      "Excellent benefits package",
      "Research opportunities",
      "Collaborative environment",
      "Advanced technology",
    ],
    organization: {
      id: "org-6",
      name: "Mayo Clinic",
      avatar: "/avatars/mayo-clinic.png",
      description: "Leading medical institution",
    },
    onViewDetails: () => alert("Viewing anesthesiology job details"),
  },
  parameters: {
    docs: {
      description: {
        story: `
**Many Highlights**

Shows how the component handles multiple job highlights by displaying
the first two and indicating the count of remaining benefits.
        `,
      },
    },
  },
};
