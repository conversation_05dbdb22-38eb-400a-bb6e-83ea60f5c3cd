import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { RateInsightsPanel } from "@/ui/blocks/negotiation-center/rate-insights-panel";

const meta: Meta<typeof RateInsightsPanel> = {
  title: "Blocks/Negotiation Center/Rate Insights Panel",
  component: RateInsightsPanel,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A comprehensive panel that displays rate insights including current rate, comparison to market average, demand levels, and actionable recommendations with interactive charts and structured data.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    currentRate: {
      control: "number",
      description: "Current hourly rate",
    },
    currency: {
      control: "text",
      description: "Currency symbol (default: $)",
    },
    unit: {
      control: "text",
      description: "Unit text (default: per hour)",
    },
    loading: {
      control: "boolean",
      description: "Loading state",
    },
  },
};

export default meta;

type Story = StoryObj<typeof RateInsightsPanel>;

// Sample data generators
const generateRateHistory = (baseRate: number, volatility = 10) => {
  const months = [
    "2024-01",
    "2024-02",
    "2024-03",
    "2024-04",
    "2024-05",
    "2024-06",
  ];
  return months.map((date, index) => ({
    date,
    rate: baseRate + (Math.random() - 0.5) * volatility * 2,
    marketAverage: baseRate * 0.9 + (Math.random() - 0.5) * volatility,
  }));
};

const highDemandActions = [
  {
    id: "1",
    title: "Negotiate rate increase",
    description: "Market conditions favor a 15-20% rate increase",
    priority: "high" as const,
    impact: "+$25-35/hour potential",
  },
  {
    id: "2",
    title: "Expand skill certifications",
    description: "Add specialized certifications to justify premium rates",
    priority: "medium" as const,
    impact: "+$15-25/hour long-term",
  },
];

const mediumDemandActions = [
  {
    id: "1",
    title: "Optimize portfolio",
    description: "Highlight recent achievements and successful projects",
    priority: "medium" as const,
    impact: "+$5-15/hour potential",
  },
  {
    id: "2",
    title: "Network expansion",
    description: "Build relationships with high-value clients",
    priority: "low" as const,
    impact: "Better opportunities",
  },
];

const lowDemandActions = [
  {
    id: "1",
    title: "Skill development",
    description: "Focus on in-demand skills to increase marketability",
    priority: "high" as const,
    impact: "Improved positioning",
  },
  {
    id: "2",
    title: "Market research",
    description: "Identify emerging opportunities in related fields",
    priority: "medium" as const,
    impact: "Future opportunities",
  },
];

export const Default: Story = {
  args: {
    currentRate: 120,
    currency: "$",
    unit: "per hour",
    rateHistory: generateRateHistory(120, 8),
    marketComparison: {
      percentile: 65,
      difference: 15,
      trend: "above",
    },
    demandMetrics: {
      level: "medium",
      score: 65,
      trend: "stable",
    },
    actionItems: mediumDemandActions,
  },
};

export const HighDemand: Story = {
  args: {
    currentRate: 180,
    currency: "$",
    unit: "per hour",
    rateHistory: generateRateHistory(180, 12),
    marketComparison: {
      percentile: 85,
      difference: 35,
      trend: "above",
    },
    demandMetrics: {
      level: "high",
      score: 85,
      trend: "up",
    },
    actionItems: highDemandActions,
  },
};

export const LowDemand: Story = {
  args: {
    currentRate: 85,
    currency: "$",
    unit: "per hour",
    rateHistory: generateRateHistory(85, 6),
    marketComparison: {
      percentile: 25,
      difference: 20,
      trend: "below",
    },
    demandMetrics: {
      level: "low",
      score: 25,
      trend: "down",
    },
    actionItems: lowDemandActions,
  },
};

export const PremiumRate: Story = {
  args: {
    currentRate: 250,
    currency: "$",
    unit: "per hour",
    rateHistory: generateRateHistory(250, 20),
    marketComparison: {
      percentile: 95,
      difference: 75,
      trend: "above",
    },
    demandMetrics: {
      level: "high",
      score: 90,
      trend: "up",
    },
    actionItems: [
      {
        id: "1",
        title: "Maintain premium positioning",
        description:
          "Continue delivering exceptional value to justify premium rates",
        priority: "medium" as const,
        impact: "Sustained premium",
      },
      {
        id: "2",
        title: "Explore executive roles",
        description: "Consider transitioning to higher-level positions",
        priority: "low" as const,
        impact: "Career advancement",
      },
    ],
  },
};

export const BudgetRate: Story = {
  args: {
    currentRate: 65,
    currency: "$",
    unit: "per hour",
    rateHistory: generateRateHistory(65, 4),
    marketComparison: {
      percentile: 15,
      difference: 30,
      trend: "below",
    },
    demandMetrics: {
      level: "low",
      score: 20,
      trend: "down",
    },
    actionItems: [
      {
        id: "1",
        title: "Skills assessment",
        description: "Identify skill gaps and create development plan",
        priority: "high" as const,
        impact: "Foundation for growth",
      },
      {
        id: "2",
        title: "Rate adjustment strategy",
        description: "Develop plan to gradually increase rates",
        priority: "high" as const,
        impact: "+$10-20/hour target",
      },
    ],
  },
};

export const MarketRate: Story = {
  args: {
    currentRate: 110,
    currency: "$",
    unit: "per hour",
    rateHistory: generateRateHistory(110, 5),
    marketComparison: {
      percentile: 50,
      difference: 0,
      trend: "at",
    },
    demandMetrics: {
      level: "medium",
      score: 50,
      trend: "stable",
    },
    actionItems: [
      {
        id: "1",
        title: "Competitive analysis",
        description: "Research ways to differentiate from market average",
        priority: "medium" as const,
        impact: "Strategic positioning",
      },
      {
        id: "2",
        title: "Value proposition",
        description: "Strengthen unique selling points",
        priority: "medium" as const,
        impact: "Premium potential",
      },
    ],
  },
};

export const Loading: Story = {
  args: {
    currentRate: 120,
    currency: "$",
    unit: "per hour",
    rateHistory: [],
    marketComparison: {
      percentile: 0,
      difference: 0,
      trend: "at",
    },
    demandMetrics: {
      level: "medium",
      score: 0,
      trend: "stable",
    },
    actionItems: [],
    loading: true,
  },
};

export const EuroRates: Story = {
  args: {
    currentRate: 95,
    currency: "€",
    unit: "per hour",
    rateHistory: generateRateHistory(95, 8),
    marketComparison: {
      percentile: 70,
      difference: 12,
      trend: "above",
    },
    demandMetrics: {
      level: "high",
      score: 75,
      trend: "up",
    },
    actionItems: [
      {
        id: "1",
        title: "EU market expansion",
        description: "Leverage high demand across European markets",
        priority: "high" as const,
        impact: "+€15-25/hour potential",
      },
    ],
  },
};

export const DayRate: Story = {
  args: {
    currentRate: 800,
    currency: "$",
    unit: "per day",
    rateHistory: generateRateHistory(800, 50),
    marketComparison: {
      percentile: 80,
      difference: 150,
      trend: "above",
    },
    demandMetrics: {
      level: "high",
      score: 80,
      trend: "up",
    },
    actionItems: [
      {
        id: "1",
        title: "Project rate optimization",
        description: "Consider project-based pricing for better margins",
        priority: "medium" as const,
        impact: "+$200-400/day potential",
      },
    ],
  },
};
