import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type {
  Block,
  Milestone,
  Registry,
} from "@/ui/blocks/negotiation-timeline";
import type {
  ContractCreatedBlockData,
  ContractSignedBlockData,
  MessageBlockData,
  RateNegotiatedBlockData,
  RateOfferBlockData,
} from "@/ui/blocks/negotiation-timeline/templates";
import type { ProviderSecurityData } from "@/ui/blocks/provider-identity/types";

import { CockpitLayout } from "@/ui/blocks/negotiation-center/cockpit";
import {
  ContractCreatedBlock,
  ContractSignedBlock,
  MessageBlock,
  RateNegotiatedBlock,
  RateOfferBlock,
} from "@/ui/blocks/negotiation-timeline/templates";

// Helper functions for generating sample data
function generateRateHistory(baseRate: number, volatility = 10) {
  const months = [
    "2024-01",
    "2024-02",
    "2024-03",
    "2024-04",
    "2024-05",
    "2024-06",
  ];
  return months.map((date) => ({
    date,
    rate: baseRate + (Math.random() - 0.5) * volatility * 2,
    marketAverage: baseRate * 0.9 + (Math.random() - 0.5) * volatility,
  }));
}

function generateMarketComparison(
  currentRate: number,
  comparison: string,
  demandLevel: "low" | "medium" | "high",
) {
  const marketAverage = currentRate * 0.9; // Assume market average is 90% of current rate
  const difference = currentRate - marketAverage;

  let percentile: number;
  let trend: "above" | "below" | "at";

  if (comparison.toLowerCase().includes("above")) {
    percentile =
      demandLevel === "high" ? 85 : demandLevel === "medium" ? 65 : 55;
    trend = "above";
  } else if (comparison.toLowerCase().includes("below")) {
    percentile =
      demandLevel === "low" ? 25 : demandLevel === "medium" ? 35 : 45;
    trend = "below";
  } else {
    percentile = 50;
    trend = "at";
  }

  return {
    percentile,
    difference: Math.abs(difference),
    trend,
  };
}

function generateDemandMetrics(demandLevel: "low" | "medium" | "high") {
  const scoreMap = {
    low: 20 + Math.random() * 30, // 20-50
    medium: 40 + Math.random() * 30, // 40-70
    high: 70 + Math.random() * 30, // 70-100
  };

  return {
    level: demandLevel,
    score: Math.round(scoreMap[demandLevel]),
    trend: "stable" as const,
  };
}

function generateActionItems(
  demandLevel: "low" | "medium" | "high",
  currentRate: number,
) {
  const baseActions = {
    high: [
      {
        id: "1",
        title: "Negotiate rate increase",
        description: "Market conditions favor a 15-20% rate increase",
        priority: "high" as const,
        impact: `+$${Math.round(currentRate * 0.15)}-${Math.round(currentRate * 0.2)}/hour potential`,
      },
      {
        id: "2",
        title: "Expand skill certifications",
        description: "Add specialized certifications to justify premium rates",
        priority: "medium" as const,
        impact: `+$${Math.round(currentRate * 0.1)}-${Math.round(currentRate * 0.15)}/hour long-term`,
      },
    ],
    medium: [
      {
        id: "1",
        title: "Optimize portfolio",
        description: "Highlight recent achievements and successful projects",
        priority: "medium" as const,
        impact: `+$${Math.round(currentRate * 0.05)}-${Math.round(currentRate * 0.1)}/hour potential`,
      },
      {
        id: "2",
        title: "Network expansion",
        description: "Build relationships with high-value clients",
        priority: "low" as const,
        impact: "Better opportunities",
      },
    ],
    low: [
      {
        id: "1",
        title: "Skill development",
        description: "Focus on in-demand skills to increase marketability",
        priority: "high" as const,
        impact: "Improved positioning",
      },
      {
        id: "2",
        title: "Market research",
        description: "Identify emerging opportunities in related fields",
        priority: "medium" as const,
        impact: "Future opportunities",
      },
    ],
  };

  return baseActions[demandLevel];
}

// Create a comprehensive registry for the timeline
const negotiationRegistry: Registry = {
  "rate-negotiated": {
    type: "rate-negotiated",
    render: (block: Block) => {
      const data = block.data as RateNegotiatedBlockData;
      return <RateNegotiatedBlock data={data} />;
    },
  },
  message: {
    type: "message",
    render: (block: Block) => {
      const data = block.data as MessageBlockData;
      return <MessageBlock data={data} />;
    },
  },
  "rate-offer": {
    type: "rate-offer",
    render: (block: Block) => {
      const data = block.data as RateOfferBlockData;
      return <RateOfferBlock data={data} />;
    },
  },
  "contract-created": {
    type: "contract-created",
    render: (block: Block) => {
      const data = block.data as ContractCreatedBlockData;
      return <ContractCreatedBlock data={data} />;
    },
  },
  "contract-signed": {
    type: "contract-signed",
    render: (block: Block) => {
      const data = block.data as ContractSignedBlockData;
      return <ContractSignedBlock data={data} />;
    },
  },
};

// Sample milestone data for the negotiation timeline
const sampleMilestones: Milestone[] = [
  {
    id: "milestone-1",
    title: "Rate Negotiated",
    status: "completed",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    blocks: [
      {
        id: "block-1",
        type: "rate-negotiated",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        data: {
          doctor: "Dr. Rose Chen",
          rate: "$120.00/hr",
          message: "Let's proceed with these terms.",
          onViewAgreement: () => alert("Opening rate agreement"),
          onViewNegotiationHistory: () => alert("Showing negotiation timeline"),
        },
      },
      {
        id: "block-2",
        type: "message",
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        data: {
          author: "Dr. Rose Chen",
          message: "Perfect! Looking forward to working with you.",
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        },
      },
    ],
  },
  {
    id: "milestone-2",
    title: "Contract Preparation",
    status: "active",
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    blocks: [
      {
        id: "block-3",
        type: "contract-created",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        data: {
          organization: "Atlanta Emergency Center",
          status: "pending",
          contractId: "CT-2024-001",
          onViewContract: () => alert("Opening contract viewer"),
          onDownloadContract: () => alert("Downloading contract PDF"),
        },
      },
    ],
  },
  {
    id: "milestone-3",
    title: "Final Steps",
    status: "pending",
    timestamp: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    blocks: [],
  },
];

// Sample security data for the provider card
const sampleSecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-2024-001573",
  fullName: "Dr. Rose Chen",
  expirationDate: "2026-12-31",
  verificationStatus: "verified",
  issuingState: "California",
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "Medical Doctor",
  specialty: "Emergency Medicine",
  images: {
    front: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Back",
      type: "back",
    },
  },
};

// Enhanced CockpitLayout component that accepts props
interface Organization {
  id: string;
  name: string;
  avatar?: string | null;
  description?: string | null;
}

interface Location {
  id: string;
  name: string;
  address: {
    formatted: string;
  };
  description?: string | null;
}

interface CockpitLayoutStoryProps {
  providerName: string;
  providerAvatar: string;
  providerSpecialty: string;
  providerExperience: string;
  providerSecurityData?: ProviderSecurityData;
  providerAuthorized?: boolean;
  jobSpecialty: string;
  jobLocation?: Location;
  jobSchedule: string;
  jobType?: string;
  jobEmploymentType?: string;
  jobSalary?: string;
  jobHighlights?: string[];
  jobOrganization?: Organization;
  currentRate: number;
  rateComparison: string;
  demandLevel: "low" | "medium" | "high";
  milestones: Milestone[];
  registry: Registry;
  defaultExpanded?: boolean;
  showMessageInput?: boolean;
  loading?: boolean;
}

function CockpitLayoutStory({
  providerName,
  providerAvatar,
  providerSpecialty,
  providerExperience,
  providerSecurityData,
  providerAuthorized = false,
  jobSpecialty,
  jobLocation,
  jobSchedule,
  jobType,
  jobEmploymentType,
  jobSalary,
  jobHighlights,
  jobOrganization,
  currentRate,
  rateComparison,
  demandLevel,
  milestones,
  registry,
  defaultExpanded = true,
  showMessageInput = true,
  loading = false,
}: CockpitLayoutStoryProps) {
  const handleSendMessage = (message: string) => {
    alert(`Sending message: "${message}"`);
  };

  const handleViewProfile = () => {
    alert(`Viewing profile for ${providerName}`);
  };

  const handleViewJobDetails = () => {
    alert(
      `Viewing job details for ${jobSpecialty} in ${jobLocation?.name || "Unknown location"}`,
    );
  };

  // Generate structured data from simple props
  const rateHistory = generateRateHistory(currentRate, 10);
  const marketComparison = generateMarketComparison(
    currentRate,
    rateComparison,
    demandLevel,
  );
  const demandMetrics = generateDemandMetrics(demandLevel);
  const actionItems = generateActionItems(demandLevel, currentRate);

  return (
    <div className="bg-background">
      <CockpitLayout
        providerProps={{
          name: providerName,
          avatar: providerAvatar,
          specialty: providerSpecialty,
          experience: providerExperience,
          securityData: providerSecurityData,
          authorized: providerAuthorized,
        }}
        jobProps={{
          specialty: jobSpecialty,
          location: jobLocation,
          schedule: jobSchedule,
          jobType: jobType,
          employmentType: jobEmploymentType,
          salary: jobSalary,
          highlights: jobHighlights,
          organization: jobOrganization,
        }}
        rateInsightsProps={{
          currentRate,
          currency: "$",
          unit: "per hour",
          rateHistory,
          marketComparison,
          demandMetrics,
          actionItems,
        }}
        negotiationProps={{
          milestones,
          registry,
          defaultExpanded,
          showMessageInput,
          onSendMessage: handleSendMessage,
        }}
        onProviderViewProfile={handleViewProfile}
        onJobViewDetails={handleViewJobDetails}
        loading={loading}
      />
    </div>
  );
}

const meta: Meta<typeof CockpitLayoutStory> = {
  title: "Blocks/Negotiation Center/Cockpit Layout",
  component: CockpitLayoutStory,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
## Responsive Negotiation Center Cockpit

A comprehensive cockpit layout for managing negotiations with providers featuring:

### 📱 **Responsive Design**
- **Mobile (< 768px)**: Single column layout with natural stacking order
- **Tablet (768px - 1024px)**: Four-column grid with cards on top, timeline and insights below
- **Desktop (> 1024px)**: Three-column layout with left sidebar, center timeline, and right insights panel

### 🏗️ **Optimized Architecture**
- **Single Render**: Each component rendered exactly once - no hidden duplicates
- **CSS Grid Layout**: Uses responsive CSS Grid for all positioning and ordering
- **Performance**: No component duplication, better memory usage, cleaner DOM
- **Maintainability**: Single source of truth for each component

### 🎨 **Design System Integration**
- Uses proper theme colors: \`border-border\`, \`bg-card\`, \`text-foreground\`, \`text-muted-foreground\`
- Preserves specialty colors (teal) for branding and visual hierarchy
- No background on layout itself - relies on wrapping container
- Consistent with design system patterns across the application

### ⏳ **Inline Skeleton Loading**
- **Conditional Rendering**: \`{loading ? <Skeleton /> : <Content />}\` pattern throughout
- **Single DOM Structure**: Components maintain same layout whether loading or not
- **Performance Optimized**: No duplicate render trees, better memory usage
- **Clear Mapping**: Easy to see exactly which elements get skeleton treatment
- **Maintainable**: Single component structure to maintain and debug

### 🎛️ **Interactive Components**
- Negotiation timeline with customizable registry and milestone blocks
- Provider and job cards with action buttons
- Rate insights with market comparison and demand visualization
- Communication and urgency indicators

### 🔧 **Event Handling**
- Timeline message sending
- Provider profile viewing
- Job details viewing
- All timeline block interactions (rate acceptance, contract viewing, etc.)

### 🔗 **Integration**
- Works with existing negotiation timeline system
- Accepts custom registry for timeline blocks
- Supports all timeline events and interactions
- Fully themeable and responsive

### 🚀 **Technical Benefits**
- **No Hidden Elements**: Eliminates hidden/duplicate components
- **Better Performance**: Single render path, optimal memory usage
- **Debugging Friendly**: Clear component hierarchy, no state conflicts
- **CSS-Driven**: Layout handled entirely by CSS Grid - no JavaScript ordering
- **Inline Skeletons**: Built-in loading states with consistent DOM structure
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    providerName: {
      control: "text",
      description: "Provider's name",
    },
    providerAvatar: {
      control: "text",
      description: "Provider's avatar URL",
    },
    providerSpecialty: {
      control: "text",
      description: "Provider's medical specialty",
    },
    providerExperience: {
      control: "text",
      description: "Provider's experience level",
    },
    providerSecurityData: {
      control: "object",
      description: "Provider's security data for license verification",
    },
    providerAuthorized: {
      control: "boolean",
      description: "Whether the user is authorized to view security details",
    },
    jobSpecialty: {
      control: "text",
      description: "Job specialty",
    },
    jobLocation: {
      control: "object",
      description: "Job location with name and address",
    },
    jobSchedule: {
      control: "text",
      description: "Job schedule",
    },
    jobType: {
      control: "text",
      description: "Type of job (e.g., Locum, Permanent)",
    },
    jobEmploymentType: {
      control: "text",
      description: "Employment type (e.g., Full-time, Part-time)",
    },
    jobSalary: {
      control: "text",
      description: "Salary range",
    },
    jobHighlights: {
      control: "object",
      description: "Array of job highlights or benefits",
    },
    jobOrganization: {
      control: "object",
      description: "Organization details with avatar",
    },
    currentRate: {
      control: "number",
      description: "Current hourly rate",
    },
    rateComparison: {
      control: "text",
      description: "Rate comparison text",
    },
    demandLevel: {
      control: "select",
      options: ["low", "medium", "high"],
      description: "Current demand level",
    },
    defaultExpanded: {
      control: "boolean",
      description: "Whether timeline milestones are expanded by default",
    },
    showMessageInput: {
      control: "boolean",
      description: "Whether to show the message input in timeline",
    },
    loading: {
      control: "boolean",
      description: "Show skeleton loading states for all components",
    },
  },
};

export default meta;

type Story = StoryObj<typeof CockpitLayoutStory>;

export const Default: Story = {
  args: {
    providerName: "Dr. Rose Chen",
    providerAvatar: "/placeholder.svg?height=48&width=48",
    providerSpecialty: "Emergency Medicine",
    providerExperience: "10 years",
    providerSecurityData: sampleSecurityData,
    providerAuthorized: true,
    jobSpecialty: "Emergency Medicine",
    jobLocation: {
      id: "loc-1",
      name: "Emory University Hospital",
      address: {
        formatted: "1364 Clifton Road NE, Atlanta, GA 30322",
      },
    },
    jobSchedule: "7:00 AM — 7:00 PM",
    jobType: "Locum",
    jobEmploymentType: "Full-time",
    jobSalary: "$350,000 - $400,000",
    jobHighlights: [
      "Comprehensive medical benefits",
      "Continuing education allowance",
      "Flexible scheduling options",
    ],
    jobOrganization: {
      id: "org-1",
      name: "Emory Healthcare",
      avatar: "/avatars/emory-healthcare.png",
      description: "Leading academic medical center",
    },
    currentRate: 120,
    rateComparison: "Above average",
    demandLevel: "medium",
    milestones: sampleMilestones,
    registry: negotiationRegistry,
    defaultExpanded: true,
    showMessageInput: true,
    loading: false,
  },
};

export const LoadingState: Story = {
  args: {
    ...Default.args,
    loading: true,
  },
  parameters: {
    docs: {
      description: {
        story: `
**Inline Skeleton Loading States**

Shows all components with inline skeleton elements. This approach maintains the same DOM structure whether loading or not:

- **Consistent Structure**: Each element is conditionally rendered as either skeleton or content
- **Clear Mapping**: Easy to see exactly which elements get skeleton treatment
- **Performance**: No duplicate render trees, better memory usage
- **Maintainability**: Single component structure to maintain

**Skeleton Patterns:**
- **Provider Card**: \`{loading ? <Skeleton /> : <Avatar />}\`
- **Job Card**: \`{loading ? <Skeleton /> : <h3>{title}</h3>}\`
- **Rate Insights**: Chart bars, text, and gauge elements all inline-skeletonized
- **Communication/Urgency**: Simple icon + text conditional rendering

This pattern eliminates the complexity of managing two separate render trees while providing excellent loading feedback.
        `,
      },
    },
  },
};

export const HighDemandScenario: Story = {
  args: {
    ...Default.args,
    providerName: "Dr. Sarah Martinez",
    providerSpecialty: "Cardiology",
    providerExperience: "15 years",
    jobSpecialty: "Interventional Cardiology",
    jobLocation: {
      id: "loc-2",
      name: "Miami Heart Institute",
      address: {
        formatted: "4300 Alton Rd, Miami Beach, FL 33140",
      },
    },
    jobSalary: "$450,000 - $500,000",
    jobHighlights: [
      "State-of-the-art cardiac facilities",
      "Research opportunities",
      "Collaborative team environment",
    ],
    jobOrganization: {
      id: "org-2",
      name: "Baptist Health South Florida",
      avatar: "/avatars/baptist-health.png",
      description: "Premier cardiac care provider",
    },
    currentRate: 180,
    rateComparison: "Well above average",
    demandLevel: "high",
  },
};

export const LowDemandScenario: Story = {
  args: {
    ...Default.args,
    providerName: "Dr. Michael Johnson",
    providerSpecialty: "Family Medicine",
    providerExperience: "5 years",
    jobSpecialty: "Primary Care",
    jobLocation: {
      id: "loc-3",
      name: "Billings Rural Health Clinic",
      address: {
        formatted: "123 Rural Road, Billings, MT 59101",
      },
    },
    jobSalary: "$220,000 - $250,000",
    jobHighlights: [
      "Serve rural communities",
      "Loan forgiveness program",
      "Family-friendly environment",
    ],
    jobOrganization: {
      id: "org-3",
      name: "Montana Rural Healthcare",
      avatar: "/avatars/montana-rural.png",
      description: "Community-focused healthcare",
    },
    currentRate: 85,
    rateComparison: "Below average",
    demandLevel: "low",
  },
};

export const ActiveNegotiation: Story = {
  args: {
    ...Default.args,
    milestones: [
      {
        id: "milestone-active",
        title: "Rate Negotiation",
        status: "active",
        timestamp: new Date(),
        blocks: [
          {
            id: "block-offer",
            type: "rate-offer",
            timestamp: new Date(Date.now() - 30 * 60 * 1000),
            data: {
              rate: "$140.00/hr",
              status: "pending" as const,
              onAccept: () => alert("Rate accepted! 🎉"),
              onCounter: () => alert("Counter offer dialog would open"),
            },
          },
          {
            id: "block-message",
            type: "message",
            timestamp: new Date(Date.now() - 60 * 60 * 1000),
            data: {
              author: "Facility Manager",
              message:
                "We'd like to offer $140/hr for this position. Let me know your thoughts.",
              timestamp: new Date(Date.now() - 60 * 60 * 1000),
            },
          },
        ],
      },
    ],
  },
};

export const TimelineCollapsed: Story = {
  args: {
    ...Default.args,
    defaultExpanded: false,
    showMessageInput: false,
  },
};

export const DarkTheme: Story = {
  parameters: {
    backgrounds: { default: "dark" },
  },
  args: {
    ...Default.args,
  },
  render: (args) => (
    <div className="dark bg-background text-foreground">
      <CockpitLayoutStory {...args} />
    </div>
  ),
};

export const DarkThemeLoading: Story = {
  parameters: {
    backgrounds: { default: "dark" },
  },
  args: {
    ...Default.args,
    loading: true,
  },
  render: (args) => (
    <div className="dark bg-background text-foreground">
      <CockpitLayoutStory {...args} />
    </div>
  ),
};

export const SecurityCardUnauthorized: Story = {
  args: {
    ...Default.args,
    providerAuthorized: false,
  },
  parameters: {
    docs: {
      description: {
        story: `
**Security Card - Unauthorized Access**

Shows the security card when the user is not authorized to view detailed security information:

- **Access Denied State**: Red icon with "Access Denied" message
- **Privacy Protection**: Sensitive information is hidden behind authorization
- **Security Flip Card**: Shows verification status on front, access denial on back
- **Professional Design**: Maintains visual hierarchy while protecting sensitive data

The security card demonstrates proper access control for sensitive medical license information.
        `,
      },
    },
  },
};

export const SecurityCardAuthorized: Story = {
  args: {
    ...Default.args,
    providerAuthorized: true,
  },
  parameters: {
    docs: {
      description: {
        story: `
**Security Card - Authorized Access**

Shows the security card when the user is authorized to view detailed security information:

- **License Details**: Document ID, expiration date, NPI number, DEA number
- **Verification Status**: Green checkmark indicating verified status
- **Flip Card Interaction**: Click to reveal detailed security information
- **Professional Layout**: Clean display of medical credentials
- **Image Previews**: Front and back license images (click to expand)

The security card provides comprehensive verification information for authorized users.
        `,
      },
    },
  },
};
