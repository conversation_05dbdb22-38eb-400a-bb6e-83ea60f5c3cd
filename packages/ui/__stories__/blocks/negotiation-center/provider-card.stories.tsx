import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { ProviderSecurityData } from "@/ui/blocks/provider-identity/types";

import { ProviderCard } from "@/ui/blocks/negotiation-center/provider-card";

// Sample security data for different providers
const emergencyMedicineSecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-2024-001573",
  fullName: "Dr. <PERSON>",
  expirationDate: "2026-12-31",
  verificationStatus: "verified",
  issuingState: "California",
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "Medical Doctor",
  specialty: "Emergency Medicine",
  images: {
    front: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Back",
      type: "back",
    },
  },
};

const cardiologySecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-2023-002847",
  fullName: "Dr. <PERSON>",
  expirationDate: "2025-06-30",
  verificationStatus: "verified",
  issuingState: "Texas",
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "Medical Doctor",
  specialty: "Interventional Cardiology",
  images: {
    front: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Back",
      type: "back",
    },
  },
};

const familyMedicineSecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-2024-003921",
  fullName: "Dr. Michael Johnson",
  expirationDate: "2027-03-15",
  verificationStatus: "pending",
  issuingState: "Florida",
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "Medical Doctor",
  specialty: "Family Medicine",
  images: {
    front: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Back",
      type: "back",
    },
  },
};

const surgerySecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-2022-004156",
  fullName: "Dr. Emily Rodriguez",
  expirationDate: "2025-09-30",
  verificationStatus: "verified",
  issuingState: "New York",
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "Medical Doctor",
  specialty: "Orthopedic Surgery",
  images: {
    front: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=240&width=380",
      alt: "Medical License Back",
      type: "back",
    },
  },
};

const meta: Meta<typeof ProviderCard> = {
  title: "Blocks/Negotiation Center/Provider Card",
  component: ProviderCard,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
A provider information card displaying key details about the healthcare provider involved in the negotiation.

### Features
- **Provider Details**: Name, avatar, specialty, and experience
- **Security Verification**: Optional license verification card with flip interaction
- **Access Control**: Authorization-based security information display
- **Action Button**: View profile functionality with chevron indicator
- **Loading State**: Skeleton animation while data loads
- **Theme Integration**: Uses semantic color tokens
- **Interactive**: Hover states and optional click handlers
- **Graceful Fallback**: Works seamlessly with or without security data

### Security Features
- **License Verification**: Shows medical license, NPI, and DEA information
- **Verification Status**: Visual indicators for verified/pending/failed states
- **Access Control**: Security details protected behind authorization
- **Image Previews**: Front and back license images with expand functionality
- **Flip Card**: Interactive security card that reveals detailed information

### Usage
Used within the negotiation center cockpit to display provider information prominently. The security card provides additional verification and trust indicators for healthcare providers.
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    name: {
      control: "text",
      description: "Provider's full name",
    },
    avatar: {
      control: "text",
      description: "URL to provider's avatar image",
    },
    specialty: {
      control: "text",
      description: "Medical specialty",
    },
    experience: {
      control: "text",
      description: "Years of experience or experience description",
    },
    loading: {
      control: "boolean",
      description: "Show skeleton loading state",
    },
    securityData: {
      control: "object",
      description: "Provider's security data for license verification",
    },
    authorized: {
      control: "boolean",
      description: "Whether the user is authorized to view security details",
    },
  },
};

export default meta;

type Story = StoryObj<typeof ProviderCard>;

export const Default: Story = {
  args: {
    name: "Dr. Rose Chen",
    avatar: "/placeholder.svg?height=48&width=48",
    specialty: "Emergency Medicine",
    experience: "10 years",
    securityData: emergencyMedicineSecurityData,
    authorized: true,
    onViewProfile: () => alert("Viewing provider profile"),
    loading: false,
  },
};

export const LoadingState: Story = {
  args: {
    ...Default.args,
    loading: true,
  },
  parameters: {
    docs: {
      description: {
        story: `
**Inline Skeleton Loading**

Uses conditional rendering to show skeletons in place of actual content:

\`\`\`tsx
{loading ? (
  <Skeleton className="size-12 rounded-full" />
) : (
  <Avatar>...</Avatar>
)}
\`\`\`

- Maintains consistent DOM structure
- Easy to see skeleton/content mapping
- Better performance than separate loading trees
- Clear visual feedback during data loading
        `,
      },
    },
  },
};

export const Cardiologist: Story = {
  args: {
    name: "Dr. Sarah Martinez",
    avatar: "/avatars/02.png",
    specialty: "Interventional Cardiology",
    experience: "15 years",
    securityData: cardiologySecurityData,
    authorized: true,
    onViewProfile: () => alert("Viewing Dr. Martinez's profile"),
  },
};

export const FamilyMedicine: Story = {
  args: {
    name: "Dr. Michael Johnson",
    avatar: "/avatars/03.png",
    specialty: "Family Medicine",
    experience: "5 years",
    securityData: familyMedicineSecurityData,
    authorized: false,
    onViewProfile: () => alert("Viewing Dr. Johnson's profile"),
  },
};

export const Surgeon: Story = {
  args: {
    name: "Dr. Emily Rodriguez",
    avatar: "/avatars/04.png",
    specialty: "Orthopedic Surgery",
    experience: "12 years",
    securityData: surgerySecurityData,
    authorized: true,
    onViewProfile: () => alert("Viewing Dr. Rodriguez's profile"),
  },
};

export const LongName: Story = {
  args: {
    name: "Dr. Christopher Alexander Williamson",
    avatar: "/avatars/05.png",
    specialty: "Neurology",
    experience: "12 years",
    onViewProfile: () => alert("Viewing Dr. Williamson's profile"),
  },
};

export const WithoutAction: Story = {
  args: {
    name: "Dr. Rose Chen",
    avatar: "/placeholder.svg?height=48&width=48",
    specialty: "Emergency Medicine",
    experience: "10 years",
    securityData: emergencyMedicineSecurityData,
    authorized: true,
    // No onViewProfile - button will be non-functional
  },
};

export const WithoutSecurityData: Story = {
  args: {
    name: "Dr. Jennifer Smith",
    avatar: "/avatars/01.png",
    specialty: "Pediatrics",
    experience: "8 years",
    // No securityData - shows graceful fallback
    onViewProfile: () => alert("Viewing Dr. Smith's profile"),
  },
  parameters: {
    docs: {
      description: {
        story: `
**Graceful Fallback - No Security Data**

Shows how the provider card handles the absence of security data gracefully:

- **Clean Layout**: Card maintains consistent layout without security section
- **No Errors**: Component doesn't break when security data is missing
- **Professional Display**: Focus remains on core provider information
- **Flexible Design**: Works with or without security verification

This demonstrates the component's robustness when security data is unavailable.
        `,
      },
    },
  },
};

export const UnauthorizedAccess: Story = {
  args: {
    name: "Dr. Michael Johnson",
    avatar: "/avatars/03.png",
    specialty: "Family Medicine",
    experience: "5 years",
    securityData: familyMedicineSecurityData,
    authorized: false,
    onViewProfile: () => alert("Viewing Dr. Johnson's profile"),
  },
  parameters: {
    docs: {
      description: {
        story: `
**Security Card - Unauthorized Access**

Shows the security card behavior when user lacks authorization:

- **Access Control**: Security information is protected behind authorization
- **Visual Feedback**: Clear indication when access is denied
- **Privacy Protection**: Sensitive license details remain hidden
- **Professional Design**: Maintains card aesthetic while enforcing security

Click the security card to see the "Access Denied" message.
        `,
      },
    },
  },
};

export const PendingVerification: Story = {
  args: {
    name: "Dr. Michael Johnson",
    avatar: "/avatars/03.png",
    specialty: "Family Medicine",
    experience: "5 years",
    securityData: familyMedicineSecurityData,
    authorized: true,
    onViewProfile: () => alert("Viewing Dr. Johnson's profile"),
  },
  parameters: {
    docs: {
      description: {
        story: `
**Security Card - Pending Verification**

Shows the security card when verification is in progress:

- **Status Indicator**: Shows "Pending" instead of "Verified"
- **Visual Distinction**: Different styling for pending state
- **Complete Information**: All license details visible when authorized
- **Professional Display**: Maintains trust while indicating pending status

This provider's verification is still in progress but details are accessible.
        `,
      },
    },
  },
};
