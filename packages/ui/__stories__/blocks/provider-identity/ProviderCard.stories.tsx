import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type {
  ProviderData,
  ProviderSecurityData,
  ProviderStatus,
} from "@/ui/blocks/provider-identity";

import {
  ProviderCard,
  ProviderContact,
  ProviderLicense,
  ProviderProfile,
  ProviderSecurityId,
} from "@/ui/blocks/provider-identity";

// Simple mock data
const mockProvider: ProviderData = {
  id: "prov-001",
  name: "Dr. <PERSON>",
  specialty: "Cardiology",
  title: "Senior Cardiologist",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  licenseExpiry: "2025-12-31",
  profileImage: "/placeholder.svg?height=120&width=100",
  status: "active" as ProviderStatus,
};

const mockSecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-NY-123456789",
  fullName: "Dr. <PERSON>",
  expirationDate: "2025-12-31",
  verificationStatus: "verified",
  issuingState: "NEW YORK",
  images: {
    front: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Back",
      type: "back",
    },
  },
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "MEDICAL LICENSE",
  specialty: "Cardiology",
};

const meta: Meta<typeof ProviderCard> = {
  title: "Blocks/Provider Identity/ProviderCard",
  component: ProviderCard,
  parameters: {
    layout: "centered",
  },
  argTypes: {
    authorized: {
      control: "boolean",
      description: "Whether user is authorized to view sensitive security data",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderCard>;

export const Default: Story = {
  args: {
    provider: mockProvider,
  },
};

export const Loading: Story = {
  args: {
    provider: mockProvider,
    loading: true,
  },
};

export const WithSecurityId: Story = {
  args: {
    provider: mockProvider,
    securityData: mockSecurityData,
    authorized: true,
  },
};

export const UnauthorizedSecurityId: Story = {
  args: {
    provider: mockProvider,
    securityData: mockSecurityData,
    authorized: false,
  },
};

// Individual component stories
export const ProfileOnly: Story = {
  render: () => <ProviderProfile provider={mockProvider} />,
};

export const ProfileLoading: Story = {
  render: () => <ProviderProfile provider={mockProvider} loading={true} />,
};

export const ContactOnly: Story = {
  render: () => <ProviderContact provider={mockProvider} />,
};

export const ContactLoading: Story = {
  render: () => <ProviderContact provider={mockProvider} loading={true} />,
};

export const SecurityIdOnly: Story = {
  render: () => (
    <ProviderSecurityId
      provider={mockProvider}
      securityData={mockSecurityData}
      authorized={true}
    />
  ),
};

export const SecurityIdLoading: Story = {
  render: () => (
    <ProviderSecurityId
      provider={mockProvider}
      securityData={mockSecurityData}
      authorized={true}
      loading={true}
    />
  ),
};

export const LicenseOnly: Story = {
  render: () => (
    <div className="h-48 w-80 overflow-hidden rounded-lg border">
      <ProviderLicense securityData={mockSecurityData} />
    </div>
  ),
};

export const LicenseLoading: Story = {
  render: () => (
    <div className="h-48 w-80 overflow-hidden rounded-lg border">
      <ProviderLicense securityData={mockSecurityData} loading={true} />
    </div>
  ),
};
