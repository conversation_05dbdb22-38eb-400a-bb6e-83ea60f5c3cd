import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type {
  ProviderData,
  ProviderSecurityData,
  ProviderStatus,
} from "@/ui/blocks/provider-identity";

import { ProviderSecurityId } from "@/ui/blocks/provider-identity";

// Mock provider data
const mockProvider: ProviderData = {
  id: "prov-001",
  name: "Dr. <PERSON>",
  specialty: "Cardiology",
  title: "Senior Cardiologist",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  licenseExpiry: "2025-12-31",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockNurseProvider: ProviderData = {
  id: "prov-002",
  name: "<PERSON>, R<PERSON>",
  specialty: "ICU Nursing",
  title: "Registered Nurse",
  email: "<EMAIL>",
  phone: "+****************",
  location: "Chicago, IL",
  licenseExpiry: "2025-06-30",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

// Mock security data for different scenarios
const mockVerifiedSecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-NY-123456789",
  fullName: "Dr. Sarah Johnson",
  expirationDate: "2025-12-31",
  verificationStatus: "verified",
  issuingState: "NEW YORK",
  images: {
    front: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Back",
      type: "back",
    },
  },
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "MEDICAL LICENSE",
  specialty: "Cardiology",
};

const mockPendingSecurityData: ProviderSecurityData = {
  ...mockVerifiedSecurityData,
  verificationStatus: "pending",
  documentId: "MD-CA-987654321",
  issuingState: "CALIFORNIA",
  specialty: "Dermatology",
};

const mockNurseSecurityData: ProviderSecurityData = {
  ...mockVerifiedSecurityData,
  documentId: "RN-TX-456789123",
  fullName: "Jennifer Thompson, RN",
  issuingState: "TEXAS",
  licenseType: "RN LICENSE",
  specialty: "ICU Nursing",
  npiNumber: "**********",
  deaNumber: undefined, // Nurses typically don't have DEA numbers
};

const mockExpiredSecurityData: ProviderSecurityData = {
  ...mockVerifiedSecurityData,
  expirationDate: "2023-12-31",
  verificationStatus: "failed",
  documentId: "MD-FL-999888777",
  issuingState: "FLORIDA",
};

const mockMinimalSecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-TX-111222333",
  fullName: "Dr. Michael Chen",
  expirationDate: "2024-08-15",
  verificationStatus: "verified",
  issuingState: "TEXAS",
  images: {
    front: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Back",
      type: "back",
    },
  },
  licenseType: "MEDICAL LICENSE",
  specialty: "Family Medicine",
};

const meta: Meta<typeof ProviderSecurityId> = {
  title: "Blocks/Provider Identity/ProviderSecurityId",
  component: ProviderSecurityId,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A flip card component that displays provider license information on the front and detailed security data on the back. Click to flip between views.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    provider: {
      description: "Provider data object",
      control: { type: "object" },
    },
    securityData: {
      description: "Security data with license information",
      control: { type: "object" },
    },
    authorized: {
      description: "Whether user is authorized to view sensitive data",
      control: { type: "boolean" },
    },
    className: {
      description: "Additional CSS classes for customization",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderSecurityId>;

export const VerifiedAuthorized: Story = {
  args: {
    provider: mockProvider,
    securityData: mockVerifiedSecurityData,
    authorized: true,
  },
};

export const Loading: Story = {
  args: {
    provider: mockProvider,
    securityData: mockVerifiedSecurityData,
    authorized: true,
    loading: true,
  },
};

export const VerifiedUnauthorized: Story = {
  args: {
    provider: mockProvider,
    securityData: mockVerifiedSecurityData,
    authorized: false,
  },
};

export const PendingVerification: Story = {
  args: {
    provider: mockProvider,
    securityData: mockPendingSecurityData,
    authorized: true,
  },
};

export const NurseLicense: Story = {
  args: {
    provider: mockNurseProvider,
    securityData: mockNurseSecurityData,
    authorized: true,
  },
};

export const ExpiredLicense: Story = {
  args: {
    provider: mockProvider,
    securityData: mockExpiredSecurityData,
    authorized: true,
  },
};

export const MinimalData: Story = {
  args: {
    provider: mockProvider,
    securityData: mockMinimalSecurityData,
    authorized: true,
  },
};

export const CustomStyling: Story = {
  args: {
    provider: mockProvider,
    securityData: mockVerifiedSecurityData,
    authorized: true,
    className: "border-2 border-blue-300 shadow-lg",
  },
};

// Comparison of authorization states
export const AuthorizationComparison: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Authorized View</h3>
        <ProviderSecurityId
          provider={mockProvider}
          securityData={mockVerifiedSecurityData}
          authorized={true}
        />
      </div>
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Unauthorized View</h3>
        <ProviderSecurityId
          provider={mockProvider}
          securityData={mockVerifiedSecurityData}
          authorized={false}
        />
      </div>
    </div>
  ),
};

// Different license types
export const LicenseTypes: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-muted-foreground">
          Medical Doctor
        </h3>
        <ProviderSecurityId
          provider={mockProvider}
          securityData={mockVerifiedSecurityData}
          authorized={true}
        />
      </div>
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-muted-foreground">
          Registered Nurse
        </h3>
        <ProviderSecurityId
          provider={mockNurseProvider}
          securityData={mockNurseSecurityData}
          authorized={true}
        />
      </div>
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-muted-foreground">
          Pending Verification
        </h3>
        <ProviderSecurityId
          provider={mockProvider}
          securityData={mockPendingSecurityData}
          authorized={true}
        />
      </div>
    </div>
  ),
};

// Interactive playground
export const Playground: Story = {
  args: {
    provider: mockProvider,
    securityData: mockVerifiedSecurityData,
    authorized: true,
  },
  argTypes: {
    provider: {
      control: {
        type: "object",
      },
    },
    securityData: {
      control: {
        type: "object",
      },
    },
  },
};
