import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type {
  ProviderData,
  ProviderStatus,
} from "@/ui/blocks/provider-identity";

import { ProviderProfile } from "@/ui/blocks/provider-identity";

// Mock provider data for different scenarios
const mockActiveProvider: ProviderData = {
  id: "prov-001",
  name: "Dr. <PERSON>",
  specialty: "Cardiology",
  title: "Senior Cardiologist",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  licenseExpiry: "2025-12-31",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockPendingProvider: ProviderData = {
  id: "prov-002",
  name: "Dr. <PERSON>",
  specialty: "Dermatology",
  title: "Dermatologist",
  email: "<EMAIL>",
  phone: "+****************",
  location: "Los Angeles, CA",
  licenseExpiry: "2024-08-15",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "pending" as ProviderStatus,
};

const mockInactiveProvider: ProviderData = {
  id: "prov-003",
  name: "Dr. Emily <PERSON>",
  specialty: "Emergency Medicine",
  title: "Emergency Physician",
  email: "<EMAIL>",
  phone: "+****************",
  location: "Miami, FL",
  licenseExpiry: "2023-12-31", // Expired
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "inactive" as ProviderStatus,
};

const mockNurseProvider: ProviderData = {
  id: "prov-004",
  name: "Jennifer Thompson, RN",
  specialty: "ICU Nursing",
  title: "Registered Nurse",
  email: "<EMAIL>",
  phone: "+****************",
  location: "Chicago, IL",
  licenseExpiry: "2025-06-30",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockLongNameProvider: ProviderData = {
  id: "prov-005",
  name: "Dr. Alexander Montgomery Fitzgerald III",
  specialty: "Otolaryngology (ENT)",
  title: "Chief of Otolaryngology Department",
  email: "<EMAIL>",
  phone: "+****************",
  location: "Boston, Massachusetts",
  licenseExpiry: "2026-03-15",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockProviderWithoutImage: ProviderData = {
  ...mockActiveProvider,
  id: "prov-006",
  name: "Dr. Lisa Wang",
  specialty: "Pediatrics",
  profileImage: undefined,
};

const meta: Meta<typeof ProviderProfile> = {
  title: "Blocks/Provider Identity/ProviderProfile",
  component: ProviderProfile,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Displays provider profile information including avatar, name, specialty, and status indicators.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="w-80 rounded-lg border p-4">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    provider: {
      description: "Provider data object with profile information",
      control: { type: "object" },
    },
    className: {
      description: "Additional CSS classes for customization",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderProfile>;

export const ActiveProvider: Story = {
  args: {
    provider: mockActiveProvider,
  },
};

export const Loading: Story = {
  args: {
    provider: mockActiveProvider,
    loading: true,
  },
};

export const PendingProvider: Story = {
  args: {
    provider: mockPendingProvider,
  },
};

export const InactiveProvider: Story = {
  args: {
    provider: mockInactiveProvider,
  },
};

export const NurseProvider: Story = {
  args: {
    provider: mockNurseProvider,
  },
};

export const LongName: Story = {
  args: {
    provider: mockLongNameProvider,
  },
};

export const WithoutProfileImage: Story = {
  args: {
    provider: mockProviderWithoutImage,
  },
};

export const CustomStyling: Story = {
  args: {
    provider: mockActiveProvider,
    className: "bg-blue-50 border-blue-200",
  },
};

// All status variations
export const AllStatuses: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="w-80 rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          Active
        </h3>
        <ProviderProfile provider={mockActiveProvider} />
      </div>
      <div className="w-80 rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          Pending
        </h3>
        <ProviderProfile provider={mockPendingProvider} />
      </div>
      <div className="w-80 rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          Inactive
        </h3>
        <ProviderProfile provider={mockInactiveProvider} />
      </div>
    </div>
  ),
};

// Interactive playground
export const Playground: Story = {
  args: {
    provider: mockActiveProvider,
  },
  argTypes: {
    provider: {
      control: {
        type: "object",
      },
    },
  },
};
