import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type {
  ProviderData,
  ProviderSecurityData,
  ProviderStatus,
} from "@/ui/blocks/provider-identity";

import {
  ProviderCard,
  ProviderContact,
  ProviderLicense,
  ProviderProfile,
  ProviderSecurityId,
} from "@/ui/blocks/provider-identity";

// Mock data
const mockProvider: ProviderData = {
  id: "prov-001",
  name: "Dr. <PERSON>",
  specialty: "Cardiology",
  title: "Senior Cardiologist",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  licenseExpiry: "2025-12-31",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockSecurityData: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-NY-123456789",
  fullName: "Dr. <PERSON>",
  expirationDate: "2025-12-31",
  verificationStatus: "verified",
  issuingState: "NEW YORK",
  images: {
    front: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Back",
      type: "back",
    },
  },
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "MEDICAL LICENSE",
  specialty: "Cardiology",
};

const meta: Meta = {
  title: "Blocks/Provider Identity/Overview",
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "Complete overview of all Provider Identity components showcasing their individual functionality and composition.",
      },
    },
  },
};

export default meta;
type Story = StoryObj;

export const ComponentOverview: Story = {
  render: () => (
    <div className="mx-auto max-w-7xl space-y-12 p-8">
      {/* Complete Provider Card */}
      <section className="space-y-4">
        <div>
          <h2 className="text-2xl font-bold">Complete Provider Card</h2>
          <p className="text-muted-foreground">
            The main component that composes all sub-components together
          </p>
        </div>
        <div className="flex justify-center">
          <ProviderCard
            provider={mockProvider}
            securityData={mockSecurityData}
            authorized={true}
          />
        </div>
      </section>

      {/* Individual Components */}
      <section className="space-y-8">
        <div>
          <h2 className="text-2xl font-bold">Individual Components</h2>
          <p className="text-muted-foreground">
            Components can be used independently for different layouts and use
            cases
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {/* Provider Profile */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Provider Profile</h3>
              <p className="text-sm text-muted-foreground">
                Displays basic provider information with avatar and status
              </p>
            </div>
            <div className="rounded-lg border p-4">
              <ProviderProfile provider={mockProvider} />
            </div>
          </div>

          {/* Provider Contact */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Provider Contact</h3>
              <p className="text-sm text-muted-foreground">
                Shows contact information including email, phone, and location
              </p>
            </div>
            <div className="rounded-lg border p-4">
              <ProviderContact provider={mockProvider} />
            </div>
          </div>

          {/* Provider License */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Provider License</h3>
              <p className="text-sm text-muted-foreground">
                Semantic license information display for card fronts
              </p>
            </div>
            <div className="h-48 w-80 overflow-hidden rounded-lg border">
              <ProviderLicense securityData={mockSecurityData} />
            </div>
          </div>

          {/* Provider Security ID */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Provider Security ID</h3>
              <p className="text-sm text-muted-foreground">
                Interactive flip card showing license and security information
              </p>
            </div>
            <div className="flex justify-center">
              <ProviderSecurityId
                provider={mockProvider}
                securityData={mockSecurityData}
                authorized={true}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Different States */}
      <section className="space-y-8">
        <div>
          <h2 className="text-2xl font-bold">Component States</h2>
          <p className="text-muted-foreground">
            How components adapt to different provider statuses and data states
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-green-700">
              Active Provider
            </h3>
            <ProviderCard
              provider={{
                ...mockProvider,
                status: "active" as ProviderStatus,
              }}
              securityData={mockSecurityData}
              authorized={true}
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-yellow-700">
              Pending Provider
            </h3>
            <ProviderCard
              provider={{
                ...mockProvider,
                status: "pending" as ProviderStatus,
              }}
              securityData={{
                ...mockSecurityData,
                verificationStatus: "pending",
              }}
              authorized={true}
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-red-700">
              Inactive Provider
            </h3>
            <ProviderCard
              provider={{
                ...mockProvider,
                status: "inactive" as ProviderStatus,
              }}
              securityData={{
                ...mockSecurityData,
                verificationStatus: "failed",
              }}
              authorized={true}
            />
          </div>
        </div>
      </section>

      {/* Authorization States */}
      <section className="space-y-8">
        <div>
          <h2 className="text-2xl font-bold">Authorization States</h2>
          <p className="text-muted-foreground">
            How security components behave with different authorization levels
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-green-700">
              Authorized View
            </h3>
            <ProviderSecurityId
              provider={mockProvider}
              securityData={mockSecurityData}
              authorized={true}
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-red-700">
              Unauthorized View
            </h3>
            <ProviderSecurityId
              provider={mockProvider}
              securityData={mockSecurityData}
              authorized={false}
            />
          </div>
        </div>
      </section>
    </div>
  ),
};

export const ComponentComposition: Story = {
  render: () => (
    <div className="mx-auto max-w-4xl space-y-8 p-8">
      <div>
        <h2 className="text-2xl font-bold">Component Composition Examples</h2>
        <p className="text-muted-foreground">
          Different ways to combine and layout provider identity components
        </p>
      </div>

      {/* Side by side layout */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Side-by-Side Layout</h3>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="space-y-4">
            <ProviderProfile provider={mockProvider} />
            <ProviderContact provider={mockProvider} />
          </div>
          <div>
            <ProviderSecurityId
              provider={mockProvider}
              securityData={mockSecurityData}
              authorized={true}
            />
          </div>
        </div>
      </div>

      {/* Stacked layout */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Stacked Layout</h3>
        <div className="mx-auto max-w-md space-y-4">
          <div className="rounded-lg border p-4">
            <ProviderProfile provider={mockProvider} />
          </div>
          <div className="rounded-lg border p-4">
            <ProviderContact provider={mockProvider} />
          </div>
          <div className="flex justify-center">
            <ProviderSecurityId
              provider={mockProvider}
              securityData={mockSecurityData}
              authorized={true}
            />
          </div>
        </div>
      </div>
    </div>
  ),
};
