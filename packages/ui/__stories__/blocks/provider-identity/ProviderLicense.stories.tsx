import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { ProviderSecurityData } from "@/ui/blocks/provider-identity";

import { ProviderLicense } from "@/ui/blocks/provider-identity";

// Mock security data for different scenarios
const mockVerifiedLicense: ProviderSecurityData = {
  type: "government-id",
  documentId: "MD-NY-123456789",
  fullName: "Dr. <PERSON>",
  expirationDate: "2025-12-31",
  verificationStatus: "verified",
  issuingState: "NEW YORK",
  images: {
    front: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Front",
      type: "front",
    },
    back: {
      url: "/placeholder.svg?height=200&width=300",
      alt: "Medical License Back",
      type: "back",
    },
  },
  npiNumber: "**********",
  deaNumber: "*********",
  licenseType: "MEDICAL LICENSE",
  specialty: "Cardiology",
};

const mockPendingLicense: ProviderSecurityData = {
  ...mockVerifiedLicense,
  verificationStatus: "pending",
  documentId: "MD-CA-987654321",
  issuingState: "CALIFORNIA",
  specialty: "Dermatology",
  licenseType: "MEDICAL LICENSE",
};

const mockNurseLicense: ProviderSecurityData = {
  ...mockVerifiedLicense,
  documentId: "RN-TX-456789123",
  issuingState: "TEXAS",
  licenseType: "RN LICENSE",
  specialty: "ICU Nursing",
  npiNumber: "**********",
  deaNumber: undefined, // Nurses typically don't have DEA numbers
};

const mockExpiringSoonLicense: ProviderSecurityData = {
  ...mockVerifiedLicense,
  expirationDate: "2024-02-15", // Soon to expire
  documentId: "MD-FL-555666777",
  issuingState: "FLORIDA",
  specialty: "Emergency Medicine",
};

const meta: Meta<typeof ProviderLicense> = {
  title: "Blocks/Provider Identity/ProviderLicense",
  component: ProviderLicense,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A semantic component that displays provider license information on the front of security ID cards. Uses semantic HTML with section and header elements.",
      },
    },
  },
  argTypes: {
    securityData: {
      description: "Provider security data including license information",
      control: { type: "object" },
    },
    className: {
      description: "Additional CSS classes for customization",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderLicense>;

export const VerifiedMedicalLicense: Story = {
  args: {
    securityData: mockVerifiedLicense,
  },
};

export const Loading: Story = {
  args: {
    securityData: mockVerifiedLicense,
    loading: true,
  },
};

export const PendingVerification: Story = {
  args: {
    securityData: mockPendingLicense,
  },
};

export const NursingLicense: Story = {
  args: {
    securityData: mockNurseLicense,
  },
};

export const ExpiringSoon: Story = {
  args: {
    securityData: mockExpiringSoonLicense,
  },
};

export const WithoutNPI: Story = {
  args: {
    securityData: {
      ...mockVerifiedLicense,
      npiNumber: undefined,
    },
  },
};

export const CustomStyling: Story = {
  args: {
    securityData: mockVerifiedLicense,
    className: "bg-blue-50 border border-blue-200",
  },
};

// Interactive playground
export const Playground: Story = {
  args: {
    securityData: mockVerifiedLicense,
  },
  argTypes: {
    securityData: {
      control: {
        type: "object",
      },
    },
  },
};
