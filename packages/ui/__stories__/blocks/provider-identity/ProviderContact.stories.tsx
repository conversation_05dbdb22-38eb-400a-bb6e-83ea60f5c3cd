import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type {
  ProviderData,
  ProviderStatus,
} from "@/ui/blocks/provider-identity";

import { ProviderContact } from "@/ui/blocks/provider-identity";

// Mock provider data for different contact scenarios
const mockStandardProvider: ProviderData = {
  id: "prov-001",
  name: "Dr. <PERSON>",
  specialty: "Cardiology",
  title: "Senior Cardiologist",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  licenseExpiry: "2025-12-31",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockInternationalProvider: ProviderData = {
  id: "prov-002",
  name: "Dr. <PERSON>",
  specialty: "Neurology",
  title: "Neurologist",
  email: "<EMAIL>",
  phone: "+39 06 1234 5678",
  location: "Rome, Italy",
  licenseExpiry: "2025-12-31",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockMobileOnlyProvider: ProviderData = {
  id: "prov-003",
  name: "Dr. <PERSON> <PERSON>",
  specialty: "Family Medicine",
  title: "Family Physician",
  email: "<EMAIL>",
  phone: "+****************",
  location: "Mobile Clinic Services",
  licenseExpiry: "2024-08-15",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "pending" as ProviderStatus,
};

const mockLongLocationProvider: ProviderData = {
  id: "prov-004",
  name: "Dr. Kumar Patel",
  specialty: "Orthopedic Surgery",
  title: "Orthopedic Surgeon",
  email: "<EMAIL>",
  phone: "+****************",
  location:
    "University Medical Center, Department of Orthopedic Surgery, Building A, Floor 3",
  licenseExpiry: "2026-03-15",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockShortContactProvider: ProviderData = {
  id: "prov-005",
  name: "Dr. Li Wei",
  specialty: "Radiology",
  title: "Radiologist",
  email: "<EMAIL>",
  phone: "****** 123 4567",
  location: "LA",
  licenseExpiry: "2025-06-30",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const mockExtensionProvider: ProviderData = {
  id: "prov-006",
  name: "Dr. Maria Santos",
  specialty: "Pediatrics",
  title: "Pediatrician",
  email: "<EMAIL>",
  phone: "+**************** ext. 1234",
  location: "Children's Hospital - East Wing",
  licenseExpiry: "2025-09-30",
  profileImage: "/placeholder.svg?height=120&width=120",
  status: "active" as ProviderStatus,
};

const meta: Meta<typeof ProviderContact> = {
  title: "Blocks/Provider Identity/ProviderContact",
  component: ProviderContact,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Displays provider contact information including email, phone, and location details.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="w-80 rounded-lg border p-4">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    provider: {
      description: "Provider data object with contact information",
      control: { type: "object" },
    },
    className: {
      description: "Additional CSS classes for customization",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderContact>;

export const StandardContact: Story = {
  args: {
    provider: mockStandardProvider,
  },
};

export const Loading: Story = {
  args: {
    provider: mockStandardProvider,
    loading: true,
  },
};

export const InternationalContact: Story = {
  args: {
    provider: mockInternationalProvider,
  },
};

export const MobileClinic: Story = {
  args: {
    provider: mockMobileOnlyProvider,
  },
};

export const LongLocation: Story = {
  args: {
    provider: mockLongLocationProvider,
  },
};

export const ShortContact: Story = {
  args: {
    provider: mockShortContactProvider,
  },
};

export const WithExtension: Story = {
  args: {
    provider: mockExtensionProvider,
  },
};

export const CustomStyling: Story = {
  args: {
    provider: mockStandardProvider,
    className: "bg-green-50 border-green-200",
  },
};

// Comparison of different contact formats
export const ContactVariations: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="w-80 rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          Standard US
        </h3>
        <ProviderContact provider={mockStandardProvider} />
      </div>
      <div className="w-80 rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          International
        </h3>
        <ProviderContact provider={mockInternationalProvider} />
      </div>
      <div className="w-80 rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          With Extension
        </h3>
        <ProviderContact provider={mockExtensionProvider} />
      </div>
      <div className="w-80 rounded-lg border p-4">
        <h3 className="mb-2 text-sm font-medium text-muted-foreground">
          Long Location
        </h3>
        <ProviderContact provider={mockLongLocationProvider} />
      </div>
    </div>
  ),
};

// Interactive playground
export const Playground: Story = {
  args: {
    provider: mockStandardProvider,
  },
  argTypes: {
    provider: {
      control: {
        type: "object",
      },
    },
  },
};
