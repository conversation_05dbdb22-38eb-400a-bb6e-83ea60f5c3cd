import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";
import {
  Building2,
  Credit<PERSON>ard,
  MoreHorizontal,
  <PERSON>tings,
  Trash2,
  User<PERSON>he<PERSON>,
} from "lucide-react";

import type {
  GenericAccount,
  GenericAccountWithOriginal,
} from "@/ui/blocks/org-accounts";

import {
  createAccountAdapter,
  OrganizationAccountsList,
} from "@/ui/blocks/org-accounts";
import { Button } from "@/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/ui/primitives/dropdown-menu";

const meta: Meta<typeof OrganizationAccountsList> = {
  title: "Blocks/Organization Accounts List",
  component: OrganizationAccountsList,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A flexible, reusable component for displaying organization accounts with support for pagination, custom rendering, and actions. Uses the PreviewOrganization component to display organization information.",
      },
    },
  },
  argTypes: {
    gridCols: {
      control: "select",
      options: [1, 2, 3, 4],
      description: "Number of grid columns for account cards",
    },
    title: {
      control: "text",
      description: "Header title for the accounts list",
    },
    description: {
      control: "text",
      description: "Description text shown below the title",
    },
    emptyMessage: {
      control: "text",
      description: "Message shown when no accounts are present",
    },
    loading: {
      control: "boolean",
      description: "Whether the component is in a loading state",
    },
  },
};

export default meta;
type Story = StoryObj<typeof OrganizationAccountsList>;

// Mock data generators
function createMockAccount(
  overrides?: Partial<GenericAccount>,
): GenericAccount {
  const statuses = ["active", "pending", "suspended", "inactive"];
  const types = [
    "primary",
    "secondary",
    "billing",
    "development",
    "production",
  ];
  const orgNames = [
    "AXA Medical Group",
    "Regional Health Network",
    "Metro Hospital System",
    "Community Care Partners",
    "Advanced Surgical Center",
    "Family Practice Associates",
    "Specialty Medical Group",
    "Emergency Care Network",
  ];

  return {
    id: faker.string.uuid(),
    organization: {
      id: faker.string.uuid(),
      name: faker.helpers.arrayElement(orgNames),
      avatar: faker.helpers.maybe(() => faker.image.avatar(), {
        probability: 0.6,
      }),
      description: faker.helpers.maybe(() => faker.company.catchPhrase(), {
        probability: 0.8,
      }),
    },
    status: faker.helpers.arrayElement(statuses),
    type: faker.helpers.arrayElement(types),
    createdAt: faker.date.recent({ days: 365 }).toISOString(),
    metadata: {
      contactEmail: faker.internet.email(),
      accountManager: faker.person.fullName(),
      region: faker.location.state(),
    },
    ...overrides,
  };
}

function createMockAccounts(count: number): GenericAccount[] {
  return Array.from({ length: count }, () => createMockAccount());
}

// Example domain-specific account type for stories
interface ExampleOrgAccount {
  id: string;
  organization: {
    id: string;
    name: string;
    logoUrl?: string;
    description: string;
  };
  accountType: "ENTERPRISE" | "PROFESSIONAL" | "BASIC";
  subscriptionStatus: "ACTIVE" | "SUSPENDED" | "TRIAL" | "CANCELLED";
  createdAt: string;
  metadata: {
    contactPerson: string;
    billingEmail: string;
    contractValue: number;
  };
}

function createMockOrgAccount(): ExampleOrgAccount {
  return {
    id: faker.string.uuid(),
    organization: {
      id: faker.string.uuid(),
      name: faker.company.name(),
      logoUrl: faker.helpers.maybe(() => faker.image.avatar(), {
        probability: 0.5,
      }),
      description: faker.company.catchPhrase(),
    },
    accountType: faker.helpers.arrayElement([
      "ENTERPRISE",
      "PROFESSIONAL",
      "BASIC",
    ]),
    subscriptionStatus: faker.helpers.arrayElement([
      "ACTIVE",
      "SUSPENDED",
      "TRIAL",
      "CANCELLED",
    ]),
    createdAt: faker.date.recent().toISOString(),
    metadata: {
      contactPerson: faker.person.fullName(),
      billingEmail: faker.internet.email(),
      contractValue: faker.number.int({ min: 1000, max: 100000 }),
    },
  };
}

// Custom menu component for stories
function ExampleAccountMenu({ account }: { account: GenericAccount }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="size-8 p-0">
          <MoreHorizontal className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem>
          <UserCheck className="mr-2 size-4" />
          View Details
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 size-4" />
          Account Settings
        </DropdownMenuItem>
        <DropdownMenuItem>
          <CreditCard className="mr-2 size-4" />
          Billing Info
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-destructive">
          <Trash2 className="mr-2 size-4" />
          Suspend Account
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Default story
export const Default: Story = {
  args: {
    accounts: createMockAccounts(6),
    total: 6,
    title: "Organization Accounts",
    description: "Manage organization accounts and access",
    gridCols: 2,
  },
};

// Loading state
export const Loading: Story = {
  args: {
    accounts: [],
    total: 0,
    loading: true,
    title: "Organization Accounts",
    description: "Loading account information...",
  },
};

// Empty state
export const Empty: Story = {
  args: {
    accounts: [],
    total: 0,
    title: "Organization Accounts",
    description: "Manage organization accounts and access",
    emptyMessage:
      "No accounts have been created yet. Add your first organization account to get started!",
  },
};

// Error state
export const ErrorState: Story = {
  args: {
    accounts: [],
    total: 0,
    error: new Error("Failed to load organization accounts. Please try again."),
    title: "Organization Accounts",
    description: "Manage organization accounts and access",
  },
};

// With custom actions
export const WithActions: Story = {
  args: {
    accounts: createMockAccounts(4),
    total: 4,
    title: "Organization Accounts",
    description: "Manage organization accounts and access",
    onAddClick: () => {
      console.log("Add account button clicked");
    },
    addButtonLabel: "Add Account",
  },
};

// With account menu
export const WithAccountMenu: Story = {
  args: {
    accounts: createMockAccounts(4),
    total: 4,
    title: "Organization Accounts",
    description: "Manage organization accounts and access",
    renderAccountMenu: (account) => <ExampleAccountMenu account={account} />,
  },
};

// With filters
export const WithFilters: Story = {
  args: {
    accounts: createMockAccounts(8),
    total: 8,
    title: "Organization Accounts",
    description: "Manage organization accounts and access",
    renderAccountMenu: (account) => <ExampleAccountMenu account={account} />,
    onAddClick: () => {
      console.log("Add account button clicked");
    },
    addButtonLabel: "Add Account",
    filters: [
      {
        id: "status",
        label: "Status",
        options: [
          { label: "All Statuses", value: null },
          { label: "Active", value: "active" },
          { label: "Pending", value: "pending" },
          { label: "Suspended", value: "suspended" },
          { label: "Inactive", value: "inactive" },
        ],
      },
      {
        id: "type",
        label: "Account Type",
        options: [
          { label: "All Types", value: null },
          { label: "Primary", value: "primary" },
          { label: "Secondary", value: "secondary" },
          { label: "Billing", value: "billing" },
          { label: "Development", value: "development" },
          { label: "Production", value: "production" },
        ],
      },
    ],
    searchPlaceholder: "Search accounts by organization...",
    searchNamespace: "accounts-demo",
  },
};

// Different grid layouts
export const SingleColumn: Story = {
  args: {
    accounts: createMockAccounts(3),
    total: 3,
    title: "Organization Accounts",
    description: "Single column layout",
    gridCols: 1,
  },
};

export const ThreeColumns: Story = {
  args: {
    accounts: createMockAccounts(9),
    total: 9,
    title: "Organization Accounts",
    description: "Three column layout",
    gridCols: 3,
  },
};

export const FourColumns: Story = {
  args: {
    accounts: createMockAccounts(12),
    total: 12,
    title: "Organization Accounts",
    description: "Four column layout",
    gridCols: 4,
  },
};

// With pagination
export const WithPagination: Story = {
  args: {
    accounts: createMockAccounts(4),
    total: 47,
    title: "Organization Accounts",
    description: "Showing paginated results",
    pagination: {
      pageIndex: 0,
      pageSize: 4,
    },
    onPaginationChange: (pagination) => {
      console.log("Pagination changed:", pagination);
    },
    itemNoun: { singular: "account", plural: "accounts" },
  },
};

// Using the adapter pattern with domain-specific data
export const WithAdapter: Story = {
  render: () => {
    // Create mock domain-specific data
    const orgAccounts = Array.from({ length: 5 }, () => createMockOrgAccount());

    // Adapt to generic format
    const adaptedAccounts = orgAccounts.map((account) =>
      createAccountAdapter(account, {
        getId: (a) => a.id,
        getOrganization: (a) => ({
          id: a.organization.id,
          name: a.organization.name,
          avatar: a.organization.logoUrl || null,
          description: a.organization.description,
        }),
        getStatus: (a) => a.subscriptionStatus.toLowerCase(),
        getType: (a) => a.accountType.toLowerCase(),
        getCreatedAt: (a) => a.createdAt,
        getMetadata: (a) => ({
          contactPerson: a.metadata.contactPerson,
          billingEmail: a.metadata.billingEmail,
          contractValue: a.metadata.contractValue,
        }),
      }),
    );

    return (
      <OrganizationAccountsList
        accounts={adaptedAccounts}
        total={adaptedAccounts.length}
        title="Enterprise Accounts"
        description="Using adapter pattern with domain-specific data"
        renderAccountMenu={(account) => (
          <ExampleAccountMenu account={account} />
        )}
        onAddClick={() => {
          console.log("Add account button clicked");
        }}
        addButtonLabel="Create Account"
        onAccountClick={(account) => {
          console.log("Clicked account:", account);
          // Access original data: account.originalData
        }}
      />
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example showing how to use the adapter pattern to transform domain-specific data into the generic format. This demonstrates using enterprise subscription data.",
      },
    },
  },
};

// Mixed account statuses and types
export const MixedStatusesAndTypes: Story = {
  args: {
    accounts: [
      createMockAccount({
        organization: {
          id: "1",
          name: "AXA Medical Group",
          description: "Primary healthcare provider",
        },
        status: "active",
        type: "primary",
      }),
      createMockAccount({
        organization: {
          id: "2",
          name: "Regional Health Network",
          description: "Multi-location healthcare system",
        },
        status: "pending",
        type: "secondary",
      }),
      createMockAccount({
        organization: {
          id: "3",
          name: "Metro Billing Services",
          description: "Financial services partner",
        },
        status: "active",
        type: "billing",
      }),
      createMockAccount({
        organization: {
          id: "4",
          name: "DevCorp Solutions",
          description: "Development and testing partner",
        },
        status: "suspended",
        type: "development",
      }),
      createMockAccount({
        organization: {
          id: "5",
          name: "Production Systems Inc",
          description: "Live environment management",
        },
        status: "active",
        type: "production",
      }),
      createMockAccount({
        organization: {
          id: "6",
          name: "Inactive Legacy Corp",
          description: "Former partner organization",
        },
        status: "inactive",
        type: "secondary",
      }),
    ],
    total: 6,
    title: "Mixed Account Types",
    description: "Example showing different account statuses and types",
  },
};

// Large dataset
export const LargeDataset: Story = {
  args: {
    accounts: createMockAccounts(20),
    total: 156,
    title: "Large Account Portfolio",
    description: "Example with many organization accounts",
    gridCols: 3,
    pagination: {
      pageIndex: 0,
      pageSize: 20,
    },
    onPaginationChange: (pagination) => {
      console.log("Pagination changed:", pagination);
    },
  },
};
