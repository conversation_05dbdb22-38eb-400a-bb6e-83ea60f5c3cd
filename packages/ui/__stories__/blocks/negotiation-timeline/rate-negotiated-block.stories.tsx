import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import { RateNegotiatedBlock } from "@/ui/blocks/negotiation-timeline/templates";

const meta: Meta<typeof RateNegotiatedBlock> = {
  title: "Blocks/Negotiation Timeline/Templates/Rate Negotiated Block",
  component: RateNegotiatedBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A timeline block for displaying successful rate negotiations with viewing actions. Shows the agreed-upon rate and optional message.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Rate negotiated block data",
      control: { type: "object" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    data: {
      doctor: "Dr. <PERSON>",
      rate: "$120.00/hr",
    },
  },
};

export const WithMessage: Story = {
  args: {
    data: {
      doctor: "Dr. <PERSON>",
      rate: "$135.00/hr",
      message: "Let's proceed with these terms.",
    },
  },
};

export const WithActions: Story = {
  args: {
    data: {
      doctor: "Dr. <PERSON>",
      rate: "$150.00/hr",
      message: "Perfect rate for this specialized position.",
      onViewAgreement: () => alert("Opening rate agreement document"),
      onViewNegotiationHistory: () => alert("Showing negotiation timeline"),
    },
  },
};

export const ViewAgreementOnly: Story = {
  args: {
    data: {
      doctor: "Dr. Emily Carter",
      rate: "$125.00/hr",
      message: "Agreed after productive discussion.",
      onViewAgreement: () => alert("Opening formal rate agreement"),
    },
  },
};

export const ViewHistoryOnly: Story = {
  args: {
    data: {
      doctor: "Dr. Michael Torres",
      rate: "$140.00/hr",
      onViewNegotiationHistory: () => alert("Showing full negotiation thread"),
    },
  },
};

export const ComplexNegotiation: Story = {
  args: {
    data: {
      doctor: "Dr. Lisa Park",
      rate: "$165.00/hr",
      message:
        "Finalized after considering the specialized requirements and weekend coverage needs.",
      onViewAgreement: () => alert("Opening detailed agreement with terms"),
      onViewNegotiationHistory: () =>
        alert("Showing 8-message negotiation thread"),
    },
  },
};

export const HighRateSpecialist: Story = {
  args: {
    data: {
      doctor: "Dr. James Wilson",
      rate: "$220.00/hr",
      message: "Cardiac surgery specialist rate approved.",
      onViewAgreement: () => alert("Opening specialist rate agreement"),
      onViewNegotiationHistory: () =>
        alert("Showing specialist negotiation process"),
    },
  },
};

export const Interactive: Story = {
  args: {
    data: {
      doctor: "Dr. Maria Rodriguez",
      rate: "$145.00/hr",
      message: "Great outcome for both parties!",
      onViewAgreement: () => {
        alert(
          "📋 Rate Agreement\n\nOpening formal agreement document:\n• Final agreed rate\n• Effective dates\n• Terms and conditions\n• Both parties' acceptance",
        );
      },
      onViewNegotiationHistory: () => {
        alert(
          "💬 Negotiation History\n\nShowing complete negotiation timeline:\n• Initial offer: $130/hr\n• Counter offer: $160/hr\n• Final agreement: $145/hr\n• 6 messages exchanged",
        );
      },
    },
  },
};
