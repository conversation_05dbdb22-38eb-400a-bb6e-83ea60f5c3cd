import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import { ContractCreatedBlock } from "@/ui/blocks/negotiation-timeline/templates";

const meta: Meta<typeof ContractCreatedBlock> = {
  title: "Blocks/Negotiation Timeline/Templates/Contract Created Block",
  component: ContractCreatedBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A timeline block for displaying contract creation status with viewing, downloading, and signing actions. Supports different contract states and conditional action buttons or placeholders.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Contract created block data",
      control: { type: "object" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Created: Story = {
  args: {
    data: {
      organization: "Ohio Health",
      status: "created",
      contractId: "CT-2024-001",
      onViewContract: () => alert("Opening contract viewer"),
      onDownloadContract: () => alert("Downloading contract PDF"),
    },
  },
};

export const PendingSignature: Story = {
  args: {
    data: {
      organization: "Mayo Clinic",
      status: "pending_signature",
      contractId: "CT-2024-002",
      onViewContract: () => alert("Opening contract for review"),
      onSignContract: () => alert("Opening DocuSign signature flow"),
    },
  },
};

export const Signed: Story = {
  args: {
    data: {
      organization: "Cleveland Clinic",
      status: "signed",
      contractId: "CT-2024-003",
      onViewContract: () => alert("Opening signed contract"),
      onDownloadContract: () => alert("Downloading executed contract"),
    },
  },
};

export const WaitingForProvider: Story = {
  args: {
    data: {
      organization: "Johns Hopkins",
      status: "pending_signature",
      contractId: "CT-2024-004",
      expectedParty: "provider",
    },
  },
};

export const WaitingForOrganization: Story = {
  args: {
    data: {
      organization: "Mass General",
      status: "created",
      contractId: "CT-2024-005",
      expectedParty: "organization",
    },
  },
};

export const WithoutContractId: Story = {
  args: {
    data: {
      organization: "Stanford Health",
      status: "created",
      onViewContract: () => alert("Opening contract draft"),
    },
  },
};

export const AllActions: Story = {
  args: {
    data: {
      organization: "UCSF Health",
      status: "pending_signature",
      contractId: "CT-2024-006",
      onViewContract: () => alert("Opening contract viewer"),
      onDownloadContract: () => alert("Downloading contract PDF"),
      onSignContract: () => alert("Starting e-signature process"),
    },
  },
};

export const Interactive: Story = {
  args: {
    data: {
      organization: "NYU Langone",
      status: "pending_signature",
      contractId: "CT-2024-007",
      onViewContract: () => {
        alert(
          "📄 Contract Viewer\n\nOpening contract in new window with:\n• Full contract terms\n• Highlighting of key sections\n• Comments and annotations",
        );
      },
      onSignContract: () => {
        alert(
          "✍️ Digital Signature\n\nRedirecting to secure signing portal:\n• Identity verification\n• Contract review\n• Digital signature capture",
        );
      },
    },
  },
};
