import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import {
  BackgroundCheckBlock,
  BankSetupBlock,
  ContractCreatedBlock,
  ContractSignedBlock,
  IdentityVerificationBlock,
  MessageBlock,
  RateNegotiatedBlock,
  RateOfferBlock,
} from "@/ui/blocks/negotiation-timeline/templates";

const meta: Meta = {
  title: "Blocks/Negotiation Timeline/Templates/Overview",
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "Overview of all available template blocks for the negotiation timeline. Each block represents a different step or interaction in the negotiation process.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

function TemplateOverview() {
  return (
    <div className="space-y-8">
      <div>
        <h2 className="mb-4 text-2xl font-bold">Message Templates</h2>
        <div className="space-y-4">
          <MessageBlock
            data={{
              author: "Dr. <PERSON>",
              message: "I'd like to discuss the rate for this position.",
              timestamp: new Date(),
            }}
          />
        </div>
      </div>

      <div>
        <h2 className="mb-4 text-2xl font-bold">Rate Negotiation Templates</h2>
        <div className="space-y-4">
          <RateOfferBlock
            data={{
              rate: "$150.00/hr",
              status: "pending",
              onAccept: () => alert("Rate accepted!"),
              onCounter: () => alert("Counter offer dialog"),
            }}
          />
          <RateNegotiatedBlock
            data={{
              doctor: "Dr. Mark Chen",
              rate: "$120.00/hr",
              message: "Let's proceed with these terms.",
              onViewAgreement: () => alert("Opening rate agreement"),
              onViewNegotiationHistory: () =>
                alert("Showing negotiation history"),
            }}
          />
        </div>
      </div>

      <div>
        <h2 className="mb-4 text-2xl font-bold">Contract Templates</h2>
        <div className="space-y-4">
          <ContractCreatedBlock
            data={{
              organization: "Ohio Health",
              status: "pending_signature",
              contractId: "CT-2024-001",
              onViewContract: () => alert("Opening contract"),
              onSignContract: () => alert("Opening signature flow"),
            }}
          />
          <ContractSignedBlock
            data={{
              signer: "Dr. Mark Chen",
              signedAt: new Date(),
              onViewSignedContract: () => alert("Opening signed contract"),
              onDownloadSignedContract: () => alert("Downloading contract"),
            }}
          />
        </div>
      </div>

      <div>
        <h2 className="mb-4 text-2xl font-bold">Verification Templates</h2>
        <div className="space-y-4">
          <BackgroundCheckBlock
            data={{
              status: "completed",
              provider: "Checkr",
              onViewDetails: () => alert("Viewing background check report"),
            }}
          />
          <IdentityVerificationBlock
            data={{
              status: "pending",
              verificationType: "full",
              provider: "Jumio",
              onStartVerification: () =>
                alert("Starting identity verification"),
            }}
          />
          <BankSetupBlock
            data={{
              status: "completed",
              bankName: "Wells Fargo",
              accountType: "Business Checking",
              onViewDetails: () => alert("Viewing account details"),
            }}
          />
        </div>
      </div>
    </div>
  );
}

export const AllTemplates: Story = {
  render: () => <TemplateOverview />,
};

export const PendingStates: Story = {
  render: () => (
    <div className="space-y-4">
      <h2 className="mb-4 text-2xl font-bold">All Pending States</h2>
      <RateOfferBlock
        data={{
          rate: "$150.00/hr",
          status: "pending",
          onAccept: () => alert("Rate accepted!"),
          onCounter: () => alert("Counter offer dialog"),
        }}
      />
      <BackgroundCheckBlock
        data={{
          status: "pending",
          provider: "Checkr",
          onStart: () => alert("Starting background check"),
        }}
      />
      <IdentityVerificationBlock
        data={{
          status: "pending",
          verificationType: "full",
          provider: "Jumio",
          onStartVerification: () => alert("Starting identity verification"),
        }}
      />
      <BankSetupBlock
        data={{
          status: "pending",
          onStartSetup: () => alert("Starting bank account setup"),
        }}
      />
    </div>
  ),
};

export const CompletedStates: Story = {
  render: () => (
    <div className="space-y-4">
      <h2 className="mb-4 text-2xl font-bold">All Completed States</h2>
      <RateNegotiatedBlock
        data={{
          doctor: "Dr. Mark Chen",
          rate: "$120.00/hr",
          message: "Let's proceed with these terms.",
        }}
      />
      <ContractSignedBlock
        data={{
          signer: "Dr. Mark Chen",
          signedAt: new Date(),
        }}
      />
      <BackgroundCheckBlock
        data={{
          status: "completed",
          provider: "Checkr",
          onViewDetails: () => alert("Viewing background check report"),
        }}
      />
      <IdentityVerificationBlock
        data={{
          status: "completed",
          verificationType: "id_document",
          provider: "Jumio",
          onViewDetails: () => alert("Viewing verification details"),
        }}
      />
      <BankSetupBlock
        data={{
          status: "completed",
          bankName: "Wells Fargo",
          accountType: "Business Checking",
          onViewDetails: () => alert("Viewing account details"),
        }}
      />
    </div>
  ),
};

export const WaitingStates: Story = {
  render: () => (
    <div className="space-y-4">
      <h2 className="mb-4 text-2xl font-bold">All Waiting States</h2>
      <RateOfferBlock
        data={{
          rate: "$165.00/hr",
          status: "pending",
          expectedParty: "organization",
        }}
      />
      <ContractCreatedBlock
        data={{
          organization: "Johns Hopkins",
          status: "pending_signature",
          contractId: "CT-2024-004",
          expectedParty: "provider",
        }}
      />
      <BackgroundCheckBlock
        data={{
          status: "pending",
          provider: "Checkr",
          expectedParty: "provider",
        }}
      />
      <IdentityVerificationBlock
        data={{
          status: "pending",
          verificationType: "full",
          provider: "Jumio",
          expectedParty: "organization",
        }}
      />
      <BankSetupBlock
        data={{
          status: "pending",
          expectedParty: "provider",
        }}
      />
    </div>
  ),
};
