import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import { ContractSignedBlock } from "@/ui/blocks/negotiation-timeline/templates";

const meta: Meta<typeof ContractSignedBlock> = {
  title: "Blocks/Negotiation Timeline/Templates/Contract Signed Block",
  component: ContractSignedBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A timeline block for displaying contract signing completion with viewing and downloading actions. Shows who signed the contract and when.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Contract signed block data",
      control: { type: "object" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    data: {
      signer: "Dr. <PERSON>",
    },
  },
};

export const WithTimestamp: Story = {
  args: {
    data: {
      signer: "Dr. <PERSON>",
      signedAt: new Date(),
    },
  },
};

export const WithActions: Story = {
  args: {
    data: {
      signer: "<PERSON>. <PERSON>",
      signedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      onViewSignedContract: () => alert("Opening signed contract"),
      onDownloadSignedContract: () => alert("Downloading executed contract"),
    },
  },
};

export const ViewOnly: Story = {
  args: {
    data: {
      signer: "Dr. Emily Carter",
      signedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      onViewSignedContract: () => alert("Opening contract in read-only mode"),
    },
  },
};

export const DownloadOnly: Story = {
  args: {
    data: {
      signer: "Dr. Michael Torres",
      signedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
      onDownloadSignedContract: () =>
        alert("Downloading contract PDF with signatures"),
    },
  },
};

export const OlderContract: Story = {
  args: {
    data: {
      signer: "Dr. Lisa Park",
      signedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      onViewSignedContract: () => alert("Opening archived contract"),
      onDownloadSignedContract: () => alert("Downloading from secure archive"),
    },
  },
};

export const Interactive: Story = {
  args: {
    data: {
      signer: "Dr. James Wilson",
      signedAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      onViewSignedContract: () => {
        alert(
          "📄 Signed Contract\n\nOpening fully executed contract with:\n• All parties' signatures\n• Timestamp verification\n• Legal validity confirmation",
        );
      },
      onDownloadSignedContract: () => {
        alert(
          "💾 Download Contract\n\nPreparing secure download:\n• PDF with embedded signatures\n• Certificate of completion\n• Audit trail included",
        );
      },
    },
  },
};
