import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs-vite";

import type {
  Block,
  Milestone,
  Registry,
} from "@/ui/blocks/negotiation-timeline";
import type {
  BackgroundCheckBlockData,
  BankSetupBlockData,
  ContractCreatedBlockData,
  ContractSignedBlockData,
  IdentityVerificationBlockData,
  MessageBlockData,
  RateNegotiatedBlockData,
  RateOfferBlockData,
} from "@/ui/blocks/negotiation-timeline/templates";

import { NegotiationTimeline } from "@/ui/blocks/negotiation-timeline";
import {
  BackgroundCheckBlock,
  BankSetupBlock,
  ContractCreatedBlock,
  ContractSignedBlock,
  IdentityVerificationBlock,
  MessageBlock,
  RateNegotiatedBlock,
  RateOfferBlock,
} from "@/ui/blocks/negotiation-timeline/templates";

// Enhanced registry using template components
const registry: Registry = {
  "rate-negotiated": {
    type: "rate-negotiated",
    render: (block: Block) => {
      const data = block.data as RateNegotiatedBlockData;
      return <RateNegotiatedBlock data={data} />;
    },
  },
  message: {
    type: "message",
    render: (block: Block) => {
      const data = block.data as MessageBlockData;
      return <MessageBlock data={data} />;
    },
  },
  "rate-offer": {
    type: "rate-offer",
    render: (block: Block) => {
      const data = block.data as RateOfferBlockData;
      return <RateOfferBlock data={data} />;
    },
  },
  "contract-created": {
    type: "contract-created",
    render: (block: Block) => {
      const data = block.data as ContractCreatedBlockData;
      return <ContractCreatedBlock data={data} />;
    },
  },
  "contract-signed": {
    type: "contract-signed",
    render: (block: Block) => {
      const data = block.data as ContractSignedBlockData;
      return <ContractSignedBlock data={data} />;
    },
  },
  "background-check": {
    type: "background-check",
    render: (block: Block) => {
      const data = block.data as BackgroundCheckBlockData;
      return <BackgroundCheckBlock data={data} />;
    },
  },
  "bank-setup": {
    type: "bank-setup",
    render: (block: Block) => {
      const data = block.data as BankSetupBlockData;
      return <BankSetupBlock data={data} />;
    },
  },
  "identity-verification": {
    type: "identity-verification",
    render: (block: Block) => {
      const data = block.data as IdentityVerificationBlockData;
      return <IdentityVerificationBlock data={data} />;
    },
  },
};

const meta: Meta<typeof NegotiationTimeline> = {
  title: "Blocks/Negotiation Timeline/Timeline",
  component: NegotiationTimeline,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A chat-widget style negotiation timeline with collapsible milestones, message input, and integrated conversation flow",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

// Sample data matching the image design
const completeMilestones: Milestone[] = [
  {
    id: "milestone-1",
    title: "Rate Negotiated",
    status: "completed",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    blocks: [
      {
        id: "block-1",
        type: "rate-negotiated",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        data: {
          doctor: "Dr. Mark Chen",
          rate: "$120.00/hr",
          message: "Let's proceed with these terms.",
          onViewAgreement: () => alert("Opening rate agreement"),
          onViewNegotiationHistory: () => alert("Showing negotiation timeline"),
        },
      },
      {
        id: "block-2",
        type: "message",
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        data: {
          author: "Dr. Emily Carter",
          message: "Sounds good.",
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        },
      },
      {
        id: "block-3",
        type: "rate-offer",
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
        data: {
          rate: "$120.00/hr",
          status: "accepted" as const,
        },
      },
    ],
  },
  {
    id: "milestone-2",
    title: "Contract Created",
    status: "completed",
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    blocks: [
      {
        id: "block-4",
        type: "contract-signed",
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        data: {
          signer: "Dr. Mark Chen",
          signedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
          onViewSignedContract: () => alert("Opening signed contract"),
          onDownloadSignedContract: () => alert("Downloading signed contract"),
        },
      },
      {
        id: "block-5",
        type: "contract-created",
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
        data: {
          organization: "Ohio Health",
          status: "signed",
          contractId: "CT-2024-001",
          onViewContract: () => alert("Opening contract viewer"),
          onDownloadContract: () => alert("Downloading contract PDF"),
        },
      },
    ],
  },
];

const activeMilestones: Milestone[] = [
  {
    id: "milestone-active-1",
    title: "Rate Negotiation",
    status: "active",
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
    blocks: [
      {
        id: "block-active-1",
        type: "rate-offer",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        data: {
          rate: "$150.00/hr",
          status: "pending" as const,
          onAccept: () => alert("Rate accepted! 🎉"),
          onCounter: () => alert("Counter offer dialog would open"),
        },
      },
      {
        id: "block-active-2",
        type: "message",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        data: {
          author: "Dr. Sarah Johnson",
          message: "I'd like to discuss the rate for this position.",
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        },
      },
    ],
  },
  {
    id: "milestone-pending-2",
    title: "Contract Creation",
    status: "pending",
    timestamp: new Date(),
    blocks: [],
  },
];

// Chat Widget - matches the image exactly
export const Default: Story = {
  args: {
    milestones: completeMilestones,
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) => alert(`Sending message: "${message}"`),
  },
};

// Chat Widget without message input
export const WithoutMessageInput: Story = {
  args: {
    milestones: completeMilestones,
    registry: registry,
    defaultExpanded: true,
    showMessageInput: false,
  },
};

// Initially collapsed milestones
export const InitiallyCollapsed: Story = {
  args: {
    milestones: completeMilestones,
    registry: registry,
    defaultExpanded: false,
    showMessageInput: true,
    onSendMessage: (message: string) => console.log("Message:", message),
  },
};

// Active negotiation with pending offers
export const ActiveNegotiation: Story = {
  args: {
    milestones: activeMilestones,
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) => alert(`New message: "${message}"`),
  },
};

// Full conversation flow
export const FullConversation: Story = {
  args: {
    milestones: [...completeMilestones, ...activeMilestones],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) => alert(`Message sent: "${message}"`),
  },
};

// Verification Flow Demo
export const VerificationFlow: Story = {
  args: {
    milestones: [
      {
        id: "verification-milestone-1",
        title: "Background Check",
        status: "completed",
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        blocks: [
          {
            id: "bg-check-1",
            type: "background-check",
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
            data: {
              status: "completed",
              provider: "Checkr",
              onViewDetails: () => alert("Viewing background check report"),
            },
          },
        ],
      },
      {
        id: "verification-milestone-2",
        title: "Identity Verification",
        status: "active",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        blocks: [
          {
            id: "id-verify-1",
            type: "identity-verification",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            data: {
              status: "in_progress",
              verificationType: "full",
              provider: "Jumio",
              onViewDetails: () => alert("Viewing verification status"),
            },
          },
        ],
      },
      {
        id: "verification-milestone-3",
        title: "Bank Account Setup",
        status: "pending",
        timestamp: new Date(),
        blocks: [
          {
            id: "bank-setup-1",
            type: "bank-setup",
            timestamp: new Date(),
            data: {
              status: "pending",
              expectedParty: "provider",
            },
          },
        ],
      },
    ],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) => alert(`Message sent: "${message}"`),
  },
};

// Background Check States
export const BackgroundCheckStates: Story = {
  args: {
    milestones: [
      {
        id: "bg-pending",
        title: "Background Check - Pending",
        status: "active",
        timestamp: new Date(),
        blocks: [
          {
            id: "bg-1",
            type: "background-check",
            timestamp: new Date(),
            data: {
              status: "pending",
              provider: "Checkr",
              onStart: () => alert("Starting background check"),
            },
          },
        ],
      },
      {
        id: "bg-progress",
        title: "Background Check - In Progress",
        status: "active",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        blocks: [
          {
            id: "bg-2",
            type: "background-check",
            timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
            data: {
              status: "in_progress",
              provider: "Checkr",
            },
          },
        ],
      },
      {
        id: "bg-failed",
        title: "Background Check - Failed",
        status: "active",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        blocks: [
          {
            id: "bg-3",
            type: "background-check",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            data: {
              status: "failed",
              provider: "Checkr",
              onRetry: () => alert("Retrying background check"),
              onViewDetails: () => alert("Viewing failure details"),
            },
          },
        ],
      },
    ],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: false,
  },
};

// Bank Setup States
export const BankSetupStates: Story = {
  args: {
    milestones: [
      {
        id: "bank-pending",
        title: "Bank Setup - Ready to Start",
        status: "active",
        timestamp: new Date(),
        blocks: [
          {
            id: "bank-1",
            type: "bank-setup",
            timestamp: new Date(),
            data: {
              status: "pending",
              onStartSetup: () => alert("Starting bank account setup"),
            },
          },
        ],
      },
      {
        id: "bank-progress",
        title: "Bank Setup - In Progress",
        status: "active",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        blocks: [
          {
            id: "bank-2",
            type: "bank-setup",
            timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
            data: {
              status: "in_progress",
              bankName: "Chase Bank",
              accountType: "Business Checking",
            },
          },
        ],
      },
      {
        id: "bank-completed",
        title: "Bank Setup - Completed",
        status: "completed",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        blocks: [
          {
            id: "bank-3",
            type: "bank-setup",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            data: {
              status: "completed",
              bankName: "Wells Fargo",
              accountType: "Business Checking",
              onViewDetails: () => alert("Viewing account details"),
            },
          },
        ],
      },
    ],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: false,
  },
};

// Identity Verification States
export const IdentityVerificationStates: Story = {
  args: {
    milestones: [
      {
        id: "id-pending",
        title: "Identity Verification - Ready",
        status: "active",
        timestamp: new Date(),
        blocks: [
          {
            id: "id-1",
            type: "identity-verification",
            timestamp: new Date(),
            data: {
              status: "pending",
              verificationType: "full",
              provider: "Jumio",
              onStartVerification: () =>
                alert("Starting identity verification"),
            },
          },
        ],
      },
      {
        id: "id-ssn",
        title: "SSN Verification - In Progress",
        status: "active",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        blocks: [
          {
            id: "id-2",
            type: "identity-verification",
            timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
            data: {
              status: "in_progress",
              verificationType: "ssn",
              provider: "Persona",
            },
          },
        ],
      },
      {
        id: "id-completed",
        title: "Identity Verification - Verified",
        status: "completed",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        blocks: [
          {
            id: "id-3",
            type: "identity-verification",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            data: {
              status: "completed",
              verificationType: "id_document",
              provider: "Jumio",
              onViewDetails: () => alert("Viewing verification details"),
            },
          },
        ],
      },
    ],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: false,
  },
};

// Waiting States (with placeholders)
export const WaitingStates: Story = {
  args: {
    milestones: [
      {
        id: "waiting-bg",
        title: "Waiting for Provider - Background Check",
        status: "active",
        timestamp: new Date(),
        blocks: [
          {
            id: "waiting-bg-1",
            type: "background-check",
            timestamp: new Date(),
            data: {
              status: "pending",
              expectedParty: "provider",
              provider: "Checkr",
            },
          },
        ],
      },
      {
        id: "waiting-bank",
        title: "Waiting for Organization - Bank Setup",
        status: "active",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        blocks: [
          {
            id: "waiting-bank-1",
            type: "bank-setup",
            timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
            data: {
              status: "pending",
              expectedParty: "organization",
            },
          },
        ],
      },
      {
        id: "waiting-id",
        title: "Waiting for Provider - Identity Verification",
        status: "active",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        blocks: [
          {
            id: "waiting-id-1",
            type: "identity-verification",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            data: {
              status: "pending",
              expectedParty: "provider",
              verificationType: "full",
              provider: "Jumio",
            },
          },
        ],
      },
      {
        id: "waiting-rate",
        title: "Waiting for Organization - Rate Response",
        status: "active",
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        blocks: [
          {
            id: "waiting-rate-1",
            type: "rate-offer",
            timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
            data: {
              rate: "$180.00/hr",
              status: "pending",
              expectedParty: "organization",
            },
          },
        ],
      },
      {
        id: "waiting-contract",
        title: "Waiting for Provider - Contract Signature",
        status: "active",
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        blocks: [
          {
            id: "waiting-contract-1",
            type: "contract-created",
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
            data: {
              organization: "Cleveland Clinic",
              status: "pending_signature",
              contractId: "CT-2024-003",
              expectedParty: "provider",
            },
          },
        ],
      },
    ],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) => alert(`Message sent: "${message}"`),
  },
};

// Interactive demo with all features
export const InteractiveDemo: Story = {
  args: {
    milestones: [
      {
        id: "demo-milestone",
        title: "Live Negotiation",
        status: "active",
        timestamp: new Date(),
        blocks: [
          {
            id: "demo-offer",
            type: "rate-offer",
            timestamp: new Date(),
            data: {
              rate: "$135.00/hr",
              status: "pending" as const,
              onAccept: () =>
                alert(
                  "🎉 Rate accepted! Contract will be generated automatically.",
                ),
              onCounter: () =>
                alert(
                  "💬 Counter offer form would appear here with rate input.",
                ),
            },
          },
          {
            id: "demo-message-2",
            type: "message",
            timestamp: new Date(Date.now() - 15 * 60 * 1000),
            data: {
              author: "Hiring Manager",
              message: "We're flexible on the rate. What works for you?",
              timestamp: new Date(Date.now() - 15 * 60 * 1000),
            },
          },
          {
            id: "demo-message-1",
            type: "message",
            timestamp: new Date(Date.now() - 30 * 60 * 1000),
            data: {
              author: "Dr. Alex Rivera",
              message:
                "I'm interested in this position. Can we discuss the compensation?",
              timestamp: new Date(Date.now() - 30 * 60 * 1000),
            },
          },
        ],
      },
    ],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) =>
      alert(`📨 Your message: "${message}" has been sent to the conversation!`),
  },
};

// Enhanced Template Demonstrations
export const EnhancedTemplateFeatures: Story = {
  args: {
    milestones: [
      {
        id: "enhanced-rate",
        title: "Rate Offer - Waiting State",
        status: "active",
        timestamp: new Date(),
        blocks: [
          {
            id: "rate-waiting",
            type: "rate-offer",
            timestamp: new Date(),
            data: {
              rate: "$150.00/hr",
              status: "pending",
              expectedParty: "provider",
            },
          },
        ],
      },
      {
        id: "enhanced-contract",
        title: "Contract - Ready to Sign",
        status: "active",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        blocks: [
          {
            id: "contract-pending",
            type: "contract-created",
            timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
            data: {
              organization: "Mayo Clinic",
              status: "pending_signature",
              contractId: "CT-2024-002",
              onViewContract: () => alert("Opening contract for review"),
              onSignContract: () => alert("Opening signature flow"),
            },
          },
        ],
      },
      {
        id: "enhanced-completed",
        title: "Completed with Actions",
        status: "completed",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        blocks: [
          {
            id: "rate-negotiated-enhanced",
            type: "rate-negotiated",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            data: {
              doctor: "Dr. Sarah Wilson",
              rate: "$135.00/hr",
              message: "Perfect rate for this position.",
              onViewAgreement: () => alert("Opening rate agreement document"),
              onViewNegotiationHistory: () =>
                alert("Showing full negotiation history"),
            },
          },
          {
            id: "contract-signed-enhanced",
            type: "contract-signed",
            timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
            data: {
              signer: "Dr. Sarah Wilson",
              signedAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
              onViewSignedContract: () =>
                alert("Opening fully executed contract"),
              onDownloadSignedContract: () => alert("Downloading contract PDF"),
            },
          },
        ],
      },
    ],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: false,
  },
};
