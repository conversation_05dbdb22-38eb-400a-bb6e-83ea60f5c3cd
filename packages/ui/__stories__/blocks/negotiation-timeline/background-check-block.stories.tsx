import type { <PERSON>a, StoryObj } from "@storybook/nextjs-vite";

import { BackgroundCheckBlock } from "@/ui/blocks/negotiation-timeline/templates";

const meta: Meta<typeof BackgroundCheckBlock> = {
  title: "Blocks/Negotiation Timeline/Templates/Background Check Block",
  component: BackgroundCheckBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A timeline block for displaying background check status and actions. Supports different statuses, provider information, and conditional action buttons or placeholders.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Background check block data",
      control: { type: "object" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    data: {
      status: "pending",
      provider: "Checkr",
      onStart: () => alert("Starting background check"),
    },
  },
};

export const InProgress: Story = {
  args: {
    data: {
      status: "in_progress",
      provider: "Checkr",
    },
  },
};

export const Completed: Story = {
  args: {
    data: {
      status: "completed",
      provider: "Checkr",
      onViewDetails: () => alert("Viewing background check report"),
    },
  },
};

export const Failed: Story = {
  args: {
    data: {
      status: "failed",
      provider: "Checkr",
      onRetry: () => alert("Retrying background check"),
      onViewDetails: () => alert("Viewing failure details"),
    },
  },
};

export const WaitingForProvider: Story = {
  args: {
    data: {
      status: "pending",
      provider: "Checkr",
      expectedParty: "provider",
    },
  },
};

export const WaitingForOrganization: Story = {
  args: {
    data: {
      status: "pending",
      provider: "Sterling",
      expectedParty: "organization",
    },
  },
};

export const WithoutProvider: Story = {
  args: {
    data: {
      status: "pending",
      onStart: () => alert("Starting background check"),
    },
  },
};

export const Interactive: Story = {
  args: {
    data: {
      status: "pending",
      provider: "HireRight",
      onStart: () => {
        alert("Background check initiated!");
        // In real implementation, this would trigger state change
      },
    },
  },
};
