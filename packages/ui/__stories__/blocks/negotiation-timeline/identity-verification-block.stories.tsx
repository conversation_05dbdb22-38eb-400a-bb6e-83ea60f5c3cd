import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import { IdentityVerificationBlock } from "@/ui/blocks/negotiation-timeline/templates";

const meta: Meta<typeof IdentityVerificationBlock> = {
  title: "Blocks/Negotiation Timeline/Templates/Identity Verification Block",
  component: IdentityVerificationBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A timeline block for displaying identity verification status and actions. Supports different verification types, statuses, provider information, and conditional action buttons or placeholders.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Identity verification block data",
      control: { type: "object" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    data: {
      status: "pending",
      verificationType: "full",
      provider: "Jumio",
      onStartVerification: () => alert("Starting identity verification"),
    },
  },
};

export const InProgress: Story = {
  args: {
    data: {
      status: "in_progress",
      verificationType: "full",
      provider: "Jumio",
    },
  },
};

export const Completed: Story = {
  args: {
    data: {
      status: "completed",
      verificationType: "id_document",
      provider: "Jumio",
      onViewDetails: () => alert("Viewing verification details"),
    },
  },
};

export const Failed: Story = {
  args: {
    data: {
      status: "failed",
      verificationType: "ssn",
      provider: "Persona",
      onRetry: () => alert("Retrying identity verification"),
      onViewDetails: () => alert("Viewing failure details"),
    },
  },
};

export const SSNVerification: Story = {
  args: {
    data: {
      status: "in_progress",
      verificationType: "ssn",
      provider: "Persona",
    },
  },
};

export const AddressVerification: Story = {
  args: {
    data: {
      status: "completed",
      verificationType: "address",
      provider: "Melissa Global",
      onViewDetails: () => alert("Address verification successful"),
    },
  },
};

export const IDDocumentVerification: Story = {
  args: {
    data: {
      status: "pending",
      verificationType: "id_document",
      provider: "Onfido",
      onStartVerification: () => alert("Starting ID document verification"),
    },
  },
};

export const WaitingForProvider: Story = {
  args: {
    data: {
      status: "pending",
      verificationType: "full",
      provider: "Jumio",
      expectedParty: "provider",
    },
  },
};

export const WaitingForOrganization: Story = {
  args: {
    data: {
      status: "pending",
      verificationType: "address",
      provider: "LexisNexis",
      expectedParty: "organization",
    },
  },
};

export const WithoutVerificationType: Story = {
  args: {
    data: {
      status: "pending",
      provider: "Jumio",
      onStartVerification: () =>
        alert("Starting default identity verification"),
    },
  },
};

export const Interactive: Story = {
  args: {
    data: {
      status: "pending",
      verificationType: "full",
      provider: "Jumio",
      onStartVerification: () => {
        alert("Redirecting to identity verification portal...");
        // In real implementation, this would trigger verification flow
      },
    },
  },
};
