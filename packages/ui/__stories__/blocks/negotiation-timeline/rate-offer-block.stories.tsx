import type { <PERSON>a, StoryObj } from "@storybook/nextjs-vite";

import { RateOfferBlock } from "@/ui/blocks/negotiation-timeline/templates";

const meta: Meta<typeof RateOfferBlock> = {
  title: "Blocks/Negotiation Timeline/Templates/Rate Offer Block",
  component: RateOfferBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A timeline block for displaying rate offers with accept/counter actions. Supports pending, accepted, and countered states with conditional action buttons or placeholders.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Rate offer block data",
      control: { type: "object" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    data: {
      rate: "$150.00/hr",
      status: "pending",
      onAccept: () => alert("Rate accepted! 🎉"),
      onCounter: () => alert("Counter offer dialog would open"),
    },
  },
};

export const Accepted: Story = {
  args: {
    data: {
      rate: "$125.00/hr",
      status: "accepted",
    },
  },
};

export const Countered: Story = {
  args: {
    data: {
      rate: "$200.00/hr",
      status: "countered",
    },
  },
};

export const WaitingForProvider: Story = {
  args: {
    data: {
      rate: "$135.00/hr",
      status: "pending",
      expectedParty: "provider",
    },
  },
};

export const WaitingForOrganization: Story = {
  args: {
    data: {
      rate: "$175.00/hr",
      status: "pending",
      expectedParty: "organization",
    },
  },
};

export const PendingWithoutActions: Story = {
  args: {
    data: {
      rate: "$160.00/hr",
      status: "pending",
    },
  },
};

export const Interactive: Story = {
  args: {
    data: {
      rate: "$145.00/hr",
      status: "pending",
      onAccept: () => {
        alert(
          "🎉 Rate accepted!\n\nNext steps:\n• Contract will be generated\n• You'll receive signing instructions",
        );
      },
      onCounter: () => {
        alert(
          "💬 Counter Offer\n\nA form would open here allowing you to:\n• Enter new rate\n• Add justification\n• Submit counter proposal",
        );
      },
    },
  },
};
