import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import { BankSetupBlock } from "@/ui/blocks/negotiation-timeline/templates";

const meta: Meta<typeof BankSetupBlock> = {
  title: "Blocks/Negotiation Timeline/Templates/Bank Setup Block",
  component: BankSetupBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A timeline block for displaying bank account setup status and actions. Supports different statuses, bank information, and conditional action buttons or placeholders.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Bank setup block data",
      control: { type: "object" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    data: {
      status: "pending",
      onStartSetup: () => alert("Starting bank account setup"),
    },
  },
};

export const InProgress: Story = {
  args: {
    data: {
      status: "in_progress",
      bankName: "Chase Bank",
      accountType: "Business Checking",
    },
  },
};

export const Completed: Story = {
  args: {
    data: {
      status: "completed",
      bankName: "Wells Fargo",
      accountType: "Business Checking",
      onViewDetails: () => alert("Viewing account details"),
    },
  },
};

export const Failed: Story = {
  args: {
    data: {
      status: "failed",
      bankName: "Bank of America",
      accountType: "Business Savings",
      onRetry: () => alert("Retrying bank setup"),
      onViewDetails: () => alert("Viewing failure details"),
    },
  },
};

export const WaitingForProvider: Story = {
  args: {
    data: {
      status: "pending",
      expectedParty: "provider",
    },
  },
};

export const WaitingForOrganization: Story = {
  args: {
    data: {
      status: "pending",
      expectedParty: "organization",
    },
  },
};

export const WithBankInfo: Story = {
  args: {
    data: {
      status: "in_progress",
      bankName: "TD Bank",
      accountType: "Business Premium Checking",
    },
  },
};

export const CompletedWithDetails: Story = {
  args: {
    data: {
      status: "completed",
      bankName: "Capital One",
      accountType: "Business Checking",
      onViewDetails: () =>
        alert("Account Details:\n- Routing: *********\n- Account: ****1234"),
    },
  },
};

export const Interactive: Story = {
  args: {
    data: {
      status: "pending",
      onStartSetup: () => {
        alert("Redirecting to bank setup portal...");
        // In real implementation, this would trigger bank setup flow
      },
    },
  },
};
