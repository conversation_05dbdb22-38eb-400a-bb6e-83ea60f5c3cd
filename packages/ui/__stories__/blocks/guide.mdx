import { Meta } from "@storybook/addon-docs";

<Meta title="Blocks/Guide" />

# Reusable Blocks Components Guide

The AXA UI library provides a comprehensive set of block components designed for building complex application workflows and user interfaces. These components handle everything from organization management to negotiation workflows, content presentation, and identity verification processes.

## Key Features

- **Modular Architecture**: Flexible composition patterns for complex workflows
- **Generic Data Handling**: Adapter patterns for working with different data sources
- **Built-in State Management**: Automatic handling of loading, error, and empty states
- **Rich Interactions**: Built-in menus, actions, and user interaction patterns
- **Responsive Design**: Mobile-first approach with adaptive grid layouts
- **Accessibility**: Full keyboard navigation and screen reader support
- **TypeScript Support**: Complete type safety with flexible generic interfaces

## Installation & Setup

```bash
npm install @axa/ui
```

The blocks components are exported from the main package:

```tsx
import {
  // Content & Marketing
  AnimatedTestimonials,
  createInvitationAdapter,
  createInvitationMenu,
  // Organization Management
  InvitationsList,
  MembersList,
  // Negotiation & Workflow
  NegotiationTimeline,
  OrganizationInvitationsList,
  OrganizationMembersList,
  ProviderIdCard,
  TestimonialCarousel,
  TimelineBlock,
  TimelineInput,
  TimelineMilestone,
} from "@axa/ui/blocks";
```

## Component Categories

### Organization Management Blocks

These components handle organization membership, invitations, and user management workflows.

#### Organization Invitations

The `InvitationsList` component provides a flexible interface for managing organization invitations with support for different data sources through adapter patterns.

##### Key Features

- **Generic Data Support**: Works with any invitation data structure via adapters
- **Built-in Actions**: Resend, revoke, and custom actions with confirmation dialogs
- **Search & Filtering**: Integrated search and filter capabilities
- **Grid Layouts**: Configurable 1-4 column grid layouts
- **Custom Rendering**: Override default card rendering for specific needs

##### Basic Usage

```tsx
import { createInvitationAdapter, InvitationsList } from "@axa/ui/blocks";

import type { RouterOutputs } from "@/api";

// Your API data type
type APIInvitation =
  RouterOutputs["invitations"]["getMany"]["invitations"][number];

function OrganizationInvitations({ invitations, loading }: Props) {
  // Transform your data using the adapter pattern
  const adaptedInvitations = invitations.map((invitation) =>
    createInvitationAdapter(invitation, {
      getId: (item) => item.id,
      getEmailAddress: (item) => item.email,
      getRole: (item) => item.role,
      getStatus: (item) => item.status,
      getCreatedAt: (item) => item.createdAt,
      getMetadata: (item) => ({ invitedBy: item.invitedBy }),
    }),
  );

  return (
    <InvitationsList
      invitations={adaptedInvitations}
      total={invitations.length}
      loading={loading}
      title="Pending Invitations"
      description="Manage organization invites and access requests"
      gridCols={2}
      onInviteClick={() => openInviteModal()}
      inviteButtonLabel="Invite Member"
    />
  );
}
```

##### Advanced Usage with Custom Actions

```tsx
import { Send, Trash2 } from "lucide-react";

import { createInvitationMenu } from "@axa/ui/blocks";

const invitationMenu = createInvitationMenu<YourInvitationType>([
  {
    label: "Resend Invitation",
    icon: Send,
    onClick: async (invitation) => {
      await api.invitations.resend.mutate({ id: invitation.id });
      toast.success("Invitation resent");
    },
  },
  {
    label: "Revoke Invitation",
    icon: Trash2,
    onClick: async (invitation) => {
      await api.invitations.revoke.mutate({ id: invitation.id });
    },
    variant: "destructive",
    confirmMessage: (invitation) =>
      `Are you sure you want to revoke the invitation for ${invitation.emailAddress}?`,
  },
]);

<InvitationsList
  invitations={invitations}
  total={total}
  renderInvitationMenu={invitationMenu}
  // ... other props
/>;
```

##### With Filters and Search

```tsx
const filters = [
  {
    id: "status",
    label: "Status",
    options: [
      { label: "All Statuses", value: null },
      { label: "Pending", value: "pending" },
      { label: "Accepted", value: "accepted" },
      { label: "Revoked", value: "revoked" },
    ],
  },
  {
    id: "role",
    label: "Role",
    options: [
      { label: "All Roles", value: null },
      { label: "Admin", value: "org:admin" },
      { label: "Member", value: "org:member" },
    ],
  },
];

<InvitationsList
  invitations={invitations}
  total={total}
  filters={filters}
  searchPlaceholder="Search invitations..."
  searchNamespace="invitations"
  // ... other props
/>;
```

#### Organization Members

The `MembersList` component handles organization member management with similar flexibility to invitations.

##### Basic Usage

```tsx
import { MembersList } from "@axa/ui/blocks";

function OrganizationMembers({ members, loading }: Props) {
  return (
    <MembersList
      members={members}
      total={members.length}
      loading={loading}
      title="Organization Members"
      description="Manage your organization's members and their roles"
      gridCols={3}
      onMemberClick={(member) => router.push(`/members/${member.id}`)}
    />
  );
}
```

##### Member Interface

```tsx
interface GenericMember {
  id: string;
  displayName: string;
  avatar?: string | null;
  initials?: string;
  role: string;
  status?: string;
  metadata?: Record<string, unknown>;
}
```

### Content & Marketing Blocks

These components handle content presentation, testimonials, and marketing-focused UI elements.

#### Animated Testimonials

The `AnimatedTestimonials` component creates engaging testimonial carousels with smooth animations and company trust indicators.

##### Basic Usage

```tsx
import { AnimatedTestimonials } from "@axa/ui/blocks";

const testimonials = [
  {
    id: 1,
    name: "Alex Johnson",
    role: "Full Stack Developer",
    company: "TechFlow",
    content:
      "This platform has transformed how we work with healthcare professionals...",
    rating: 5,
    avatar: "https://example.com/avatar1.jpg",
  },
  {
    id: 2,
    name: "Dr. Sarah Miller",
    role: "Emergency Physician",
    company: "Metro Health",
    content:
      "The scheduling system is intuitive and saves us hours each week...",
    rating: 5,
    avatar: "https://example.com/avatar2.jpg",
  },
];

function TestimonialsSection() {
  return (
    <AnimatedTestimonials
      testimonials={testimonials}
      trustedCompanies={[
        "Metro Health",
        "City Medical",
        "Regional Care",
        "Unity Hospital",
      ]}
    />
  );
}
```

##### Testimonial Carousel

For simpler testimonial displays:

```tsx
import { TestimonialCarousel } from "@axa/ui/blocks";

<TestimonialCarousel testimonials={testimonials} autoplay interval={5000} />;
```

#### Provider Identity Card

The `ProviderIdCard` component displays professional identity information with government ID verification support.

##### Basic Usage

```tsx
import type { ProviderData } from "@axa/ui/blocks";
import { ProviderIdCard } from "@axa/ui/blocks";

const providerData: ProviderData = {
  id: "provider-123",
  firstName: "Dr. John",
  lastName: "Smith",
  title: "Emergency Physician",
  avatar: "https://example.com/avatar.jpg",
  license: {
    number: "MD123456",
    state: "California",
    expirationDate: "2025-12-31",
    status: "active",
  },
  governmentId: {
    type: "drivers_license",
    state: "CA",
    verified: true,
    verifiedAt: "2024-01-15T10:30:00Z",
  },
};

function ProviderProfile() {
  return (
    <ProviderIdCard
      provider={providerData}
      onVerifyIdentity={() => startVerificationFlow()}
      onUpdateProfile={() => openProfileModal()}
    />
  );
}
```

### Negotiation & Workflow Blocks

These components handle complex multi-step workflows, negotiations, and process timelines.

#### Negotiation Timeline

The `NegotiationTimeline` component provides a flexible timeline system for displaying negotiation processes, onboarding workflows, and multi-step procedures.

##### Architecture Overview

The timeline follows a composition-based architecture:

```
Timeline Container
├── Milestone (collapsible sections)
│   ├── Block 1 (template-rendered content)
│   ├── Block 2 (template-rendered content)
│   └── Block N (template-rendered content)
├── Milestone 2
└── Message Input
```

##### Registry Pattern

The system uses a registry pattern for rendering different block types:

```tsx
import type { Registry } from "@axa/ui/blocks";
import { NegotiationTimeline } from "@axa/ui/blocks";

// Define your custom block renderers
const registry: Registry = {
  "rate-offer": {
    type: "rate-offer",
    render: (block) => <RateOfferBlock {...block} />,
  },
  "contract-signing": {
    type: "contract-signing",
    render: (block) => <ContractSigningBlock {...block} />,
  },
  "background-check": {
    type: "background-check",
    render: (block) => <BackgroundCheckBlock {...block} />,
  },
};

const milestones = [
  {
    id: "negotiation",
    title: "Rate Negotiation",
    status: "completed",
    blocks: [
      {
        id: "rate-1",
        type: "rate-offer",
        timestamp: new Date("2024-01-15"),
        data: {
          rate: 85,
          currency: "USD",
          period: "hour",
          status: "accepted",
        },
      },
    ],
  },
  {
    id: "verification",
    title: "Identity Verification",
    status: "in_progress",
    blocks: [
      {
        id: "bg-check",
        type: "background-check",
        timestamp: new Date("2024-01-16"),
        data: {
          status: "pending",
          expectedParty: "provider",
        },
      },
    ],
  },
];

function NegotiationWorkflow() {
  return (
    <NegotiationTimeline
      milestones={milestones}
      registry={registry}
      variant="default"
      onSendMessage={(message) => sendMessage(message)}
    />
  );
}
```

##### Timeline Components

**Individual Components:**

```tsx
import {
  TimelineMilestone,
  TimelineBlock,
  TimelineInput
} from "@axa/ui/blocks";

// Individual milestone
<TimelineMilestone
  milestone={milestone}
  registry={registry}
  isCollapsed={false}
  onToggle={() => setCollapsed(!collapsed)}
/>

// Individual block
<TimelineBlock
  block={block}
  registry={registry}
/>

// Message input
<TimelineInput
  onSend={(message) => handleSend(message)}
  placeholder="Send a message..."
  disabled={loading}
/>
```

##### Block Template Conventions

All template blocks follow consistent patterns:

```tsx
interface BlockData {
  status: "pending" | "in_progress" | "completed" | "failed";
  expectedParty?: "provider" | "organization" | undefined;
  // Block-specific data...
}

function CustomBlock({ id, timestamp, data, onAction }: BlockProps) {
  return (
    <div className="rounded-lg border bg-card p-4">
      {/* Content */}
      <div className="space-y-2">
        <h4 className="font-medium">{data.title}</h4>
        <p className="text-sm text-muted-foreground">{data.description}</p>
      </div>

      {/* Actions based on expected party */}
      {!data.expectedParty && onAction && (
        <Button onClick={onAction}>Take Action</Button>
      )}

      {data.expectedParty === "provider" && (
        <div className="text-sm text-muted-foreground">
          Waiting for provider to complete this step...
        </div>
      )}
    </div>
  );
}
```

## Data Patterns & Best Practices

### Generic Interfaces

All block components use generic interfaces for maximum flexibility:

```tsx
// Invitations work with any data structure
interface GenericInvitation {
  id: string;
  emailAddress: string;
  role: string;
  status: string;
  createdAt: string | Date;
  metadata?: Record<string, unknown>;
}

// Members work with any member data
interface GenericMember {
  id: string;
  displayName: string;
  avatar?: string | null;
  role: string;
  status?: string;
  metadata?: Record<string, unknown>;
}
```

### Adapter Pattern

Use the adapter pattern to transform your API data:

```tsx
// Transform your specific data types to generic interfaces
const adaptedData = rawData.map((item) =>
  createInvitationAdapter(item, {
    getId: (item) => item.uuid,
    getEmailAddress: (item) => item.email_address,
    getRole: (item) => item.user_role,
    getStatus: (item) => item.invitation_status,
    getCreatedAt: (item) => item.created_timestamp,
  }),
);
```

### State Management

Components handle their own state but expose callbacks for integration:

```tsx
function IntegratedInvitations() {
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

  const { data, loading } = useInvitations({
    page: pagination.pageIndex,
    limit: pagination.pageSize,
  });

  return (
    <InvitationsList
      invitations={data?.invitations ?? []}
      total={data?.total ?? 0}
      loading={loading}
      pagination={pagination}
      onPaginationChange={setPagination}
    />
  );
}
```

### Error Handling

Components provide error state handling:

```tsx
<InvitationsList
  invitations={[]}
  total={0}
  error={new Error("Failed to load invitations")}
  // Component will display error state automatically
/>
```

## Styling & Customization

### Grid Layouts

Most list components support grid layouts:

```tsx
<InvitationsList gridCols={3} /> // 3-column grid
<MembersList gridCols={2} />     // 2-column grid
```

### Custom Rendering

Override default rendering for specific needs:

```tsx
<InvitationsList
  renderInvitation={(invitation) => (
    <CustomInvitationCard
      invitation={invitation}
      onClick={() => handleClick(invitation)}
    />
  )}
/>
```

### Timeline Design System

Timeline components follow the 80px left gutter pattern:

- **Total gutter width**: 80px
- **Content area**: Flexible width starting at 80px
- **Timeline line**: Centered at 40px from left
- **Icon position**: Centered within gutter

## Integration Examples

### With Search Parameters

```tsx
import { useSearchParams } from "next/navigation";

function SearchableInvitations() {
  const searchParams = useSearchParams();
  const statusFilter = searchParams.get("status");
  const searchQuery = searchParams.get("q");

  return (
    <InvitationsList
      // Data filtered based on URL params
      invitations={filteredInvitations}
      // Pass current params for URL sync
      filters={filters}
      searchNamespace="invitations"
    />
  );
}
```

### With API Integration

```tsx
function APIIntegratedMembers() {
  const { data, loading, error } = api.members.getMany.useQuery({
    organizationId,
    include: { role: true, avatar: true },
  });

  const removeMember = api.members.remove.useMutation({
    onSuccess: () => {
      toast.success("Member removed");
      refetch();
    },
  });

  return (
    <MembersList
      members={data?.members ?? []}
      total={data?.total ?? 0}
      loading={loading}
      error={error}
      renderMemberMenu={(member) => (
        <DropdownMenu>
          <DropdownMenuItem
            onClick={() => removeMember.mutate({ id: member.id })}
          >
            Remove Member
          </DropdownMenuItem>
        </DropdownMenu>
      )}
    />
  );
}
```

### Complex Workflow Integration

```tsx
function ContractNegotiationFlow({ contractId }: Props) {
  const { data: contract } = api.contracts.get.useQuery({ id: contractId });
  const { data: timeline } = api.negotiations.getTimeline.useQuery({
    contractId,
  });

  const sendMessage = api.negotiations.sendMessage.useMutation();

  // Define block renderers for this workflow
  const registry: Registry = {
    "rate-offer": { type: "rate-offer", render: RateOfferBlock },
    "contract-draft": { type: "contract-draft", render: ContractDraftBlock },
    signature: { type: "signature", render: SignatureBlock },
  };

  return (
    <div className="space-y-6">
      <ContractHeader contract={contract} />

      <NegotiationTimeline
        milestones={timeline?.milestones ?? []}
        registry={registry}
        onSendMessage={(message) => sendMessage.mutate({ contractId, message })}
      />
    </div>
  );
}
```

## Performance Considerations

### Lazy Loading

For large datasets, implement pagination:

```tsx
const [page, setPage] = useState(0);
const { data } = useInfiniteQuery({
  queryKey: ["invitations", page],
  queryFn: ({ pageParam }) => fetchInvitations({ page: pageParam }),
});

<InvitationsList
  invitations={data?.pages.flatMap((p) => p.invitations) ?? []}
  pagination={{ pageIndex: page, pageSize: 20 }}
  onPaginationChange={({ pageIndex }) => setPage(pageIndex)}
/>;
```

### Memoization

Memoize expensive computations:

```tsx
const adaptedInvitations = useMemo(
  () =>
    rawInvitations.map((invitation) =>
      createInvitationAdapter(invitation, config),
    ),
  [rawInvitations],
);

const invitationMenu = useMemo(
  () => createInvitationMenu(menuConfig),
  [menuConfig],
);
```

This guide provides a comprehensive overview of the AXA UI blocks components. Each component is designed for flexibility, type safety, and integration with modern React patterns while maintaining consistent design and behavior across your application.
