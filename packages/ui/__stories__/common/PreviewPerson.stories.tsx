import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import Preview<PERSON>erson from "@/ui/common/PreviewPerson";

const meta: Meta<typeof PreviewPerson> = {
  title: "Common/PreviewPerson",
  component: PreviewPerson,
  parameters: {
    layout: "centered",
  },
  argTypes: {
    person: {
      description: "Person object containing id, name, and optional avatar",
      control: "object",
      table: {
        type: {
          summary:
            "{ id: string; name: string; avatar?: string | null; } | null",
        },
      },
    },
    size: {
      description: "Size variant of the preview person component",
      control: "select",
      options: ["sm", "md", "lg", "xl"],
      table: {
        type: { summary: "sm | md | lg | xl" },
        defaultValue: { summary: "md" },
      },
    },
    loading: {
      description: "Shows loading skeleton when true",
      control: "boolean",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    link: {
      description: "Makes the person name clickable as a link",
      control: "boolean",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    shadow: {
      description: "Adds shadow to the avatar",
      control: "boolean",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "true" },
      },
    },
    description: {
      description: "Optional description text displayed below the name",
      control: "text",
      table: {
        type: { summary: "string" },
      },
    },
    className: {
      description: "Additional CSS classes to apply to the component",
      control: "text",
      table: {
        type: { summary: "string" },
      },
    },
    children: {
      description: "Additional content to display on the right side",
      control: false,
      table: {
        type: { summary: "React.ReactNode" },
      },
    },
  },
  args: {
    person: {
      id: "1",
      name: "John Doe",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    },
    size: "md",
    loading: false,
    link: false,
    shadow: true,
  },
};

export default meta;
type Story = StoryObj<typeof PreviewPerson>;

// Basic variations
export const Default: Story = {};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithDescription: Story = {
  args: {
    description: "Senior Software Engineer",
  },
};

export const AsLink: Story = {
  args: {
    link: true,
    description: "Click to view profile",
  },
};

export const WithoutAvatar: Story = {
  args: {
    person: {
      id: "2",
      name: "Jane Smith",
      avatar: null,
    },
    description: "Product Manager",
  },
};

export const WithoutShadow: Story = {
  args: {
    shadow: false,
    description: "No shadow on avatar",
  },
};

// Size variations
export const SmallSize: Story = {
  args: {
    size: "sm",
    description: "Small size variant",
  },
};

export const MediumSize: Story = {
  args: {
    size: "md",
    description: "Medium size variant (default)",
  },
};

export const LargeSize: Story = {
  args: {
    size: "lg",
    description: "Large size variant",
  },
};

export const ExtraLargeSize: Story = {
  args: {
    size: "xl",
    description: "Extra large size variant",
  },
};

// Complex scenarios
export const LongNameAndDescription: Story = {
  args: {
    person: {
      id: "3",
      name: "Dr. Alexander Christopher Wellington III",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    },
    description:
      "Chief Technology Officer & Senior Principal Software Engineering Manager",
  },
};

export const WithCustomChildren: Story = {
  args: {
    description: "Team Lead",
    children: (
      <div className="flex gap-1">
        <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
          Online
        </span>
      </div>
    ),
  },
};

export const EmptyPerson: Story = {
  args: {
    person: null,
    description: "No person data",
  },
};

// Showcase all sizes together
export const SizeComparison: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <PreviewPerson
        person={{
          id: "1",
          name: "Small Size",
          avatar:
            "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
        }}
        size="sm"
        description="Small variant"
      />
      <PreviewPerson
        person={{
          id: "2",
          name: "Medium Size",
          avatar:
            "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
        }}
        size="md"
        description="Medium variant (default)"
      />
      <PreviewPerson
        person={{
          id: "3",
          name: "Large Size",
          avatar:
            "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
        }}
        size="lg"
        description="Large variant"
      />
      <PreviewPerson
        person={{
          id: "4",
          name: "Extra Large Size",
          avatar:
            "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",
        }}
        size="xl"
        description="Extra large variant"
      />
    </div>
  ),
  parameters: {
    controls: { disable: true },
  },
};
