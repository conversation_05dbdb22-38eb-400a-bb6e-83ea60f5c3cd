import { Meta } from "@storybook/addon-docs";

<Meta title="Component Catalog" />

# AXA UI Component Catalog

A comprehensive inventory of all available components in the AXA UI library, organized by category and purpose.

> **📝 Naming Convention Note**: This library is transitioning from PascalCase to kebab-case for file names. While some legacy components still use PascalCase, new components should follow kebab-case conventions.¹

## 📋 Quick Stats

- **59 Primitives** - Core UI building blocks (based on ShadCN at the core and 21st.dev components, with magicui components)
- **15 Shared Utilities** - Common reusable components
- **14 Common Components** - Business domain components
- **29 Field Components** - Form input specializations
- **13 Form Components** - Complete form implementations
- **15 Search Components** - URL-based search interfaces
- **6 Selector Components** - Advanced selection interfaces
- **7 Table Components** - Data table implementations
- **20+ Block Components** - Complex composite components

---

## 🧱 Primitives

**Core UI building blocks** - Low-level components following design system standards.

> **🔧 Naming**: Primitives use kebab-case (e.g., `alert-dialog.tsx`, `input-otp.tsx`) as the modern standard.

### Layout & Structure

- **`accordion`** - Collapsible content sections with proper accessibility
- **`card`** - Container component with header, content, and footer areas
- **`sheet`** - Slide-out panels from screen edges
- **`drawer`** - Mobile-friendly slide-up panel
- **`sidebar`** - Navigation sidebar with collapsible states
- **`separator`** - Visual dividers between content sections
- **`resizable`** - Draggable resize handles between panels
- **`scroll-area`** - Custom scrollbar styling and behavior

#### Complex Layout Components (not primitives)

- **`AppLayout`** - Complete application shell with responsive sidebar navigation for authenticated dashboard applications
- **`AppView`** - Page view wrapper with title, breadcrumb navigation, and error boundary handling for main content areas
- **`Footer`** - Full website footer with brand, navigation links, contact information, and legal compliance links
- **`Header`** - Website header with brand logo, navigation menu, theme toggle, and authentication controls
- **`PageLayout`** - Simple page structure combining header and footer for marketing and content pages
- **`common`** - Brand logo and copyright utilities shared across layout components

### Navigation

- **`breadcrumb`** - Hierarchical navigation trail
- **`navigation-menu`** - Complex dropdown navigation systems
- **`menubar`** - Horizontal menu bar with dropdowns
- **`pagination`** - Page navigation controls
- **`tabs`** - Tabbed interface component

### Feedback & Overlays

- **`alert`** - Status messages and notifications
- **`alert-dialog`** - Modal confirmation dialogs
- **`dialog`** - General purpose modal dialogs
- **`toast`** - Temporary notification messages
- **`sonner`** - Advanced toast notification system
- **`skeleton`** - Loading state placeholders
- **`progress`** - Progress bar indicators
- **`hover-card`** - Contextual information on hover
- **`tooltip`** - Brief helpful text on hover
- **`popover`** - Floating content containers

### Form Controls

- **`input`** - Text input fields with variants
- **`textarea`** - Multi-line text input
- **`select`** - Dropdown selection component
- **`multiselect`** - Multi-option selection with tags
- **`checkbox`** - Binary selection control
- **`radio-group`** - Single selection from multiple options
- **`switch`** - Toggle control for binary states
- **`slider`** - Range selection control
- **`input-otp`** - One-time password input fields

### Interactive Elements

- **`button`** - Primary interactive element with variants
- **`toggle`** - Binary state button
- **`toggle-group`** - Multiple toggle buttons
- **`badge`** - Status labels and tags
- **`avatar`** - User profile images with fallbacks

### Data Display

- **`table`** - Structured data presentation
- **`chart`** - Data visualization components
- **`carousel`** - Image and content sliders
- **`gnatt`** - Project timeline visualization (34KB - complex)

### Date & Time

- **`calendar`** - Date picker interface
- **`calendar-rac`** - React Aria Calendar implementation
- **`datefield`** - Date input field
- **`datefield-rac`** - React Aria date field
- **`datetime-picker`** - Combined date and time selection
- **`time-picker`** - Time-only selection

### Utility & System

- **`form`** - Form validation and state management
- **`field`** - Form field wrapper with labels and validation
- **`label`** - Accessible form labels
- **`command`** - Command palette interface
- **`context-menu`** - Right-click context menus
- **`collapsible`** - Simple show/hide content
- **`aspect-ratio`** - Maintain content aspect ratios
- **`theme`** - Theme provider and utilities

---

## 🔍 Search Components

**URL-based search interfaces** - Components that sync state with URL parameters for persistent search experiences.

> **🔧 Naming**: Search components use PascalCase (legacy) but internal files use kebab-case. New search components should use kebab-case.

### Core Search Components

- **`SearchText`** - Text-based search input with URL sync
- **`SearchSelect`** - Dropdown search filters with URL persistence
- **`SearchDate`** - Date range picker with URL state management
- **`SearchValue`** - Value-based search selector
- **`SearchFilter`** - Multi-group filter interface
- **`SearchPagination`** - URL-based pagination controls
- **`SearchParams`** - URL parameter management context

### Implementation Utilities

- **`date.tsx`** _(kebab-case)_ - Date search logic utilities
- **`filter.tsx`** _(kebab-case)_ - Filter state management
- **`pagination.tsx`** _(kebab-case)_ - Pagination logic and utilities
- **`select.tsx`** _(kebab-case)_ - Select component search logic
- **`text.tsx`** _(kebab-case)_ - Text search implementation
- **`value.tsx`** _(kebab-case)_ - Value search implementation

---

## 🎯 Selectors

**Advanced selection interfaces** - High-level components for entity selection with search, preview, and multi-select capabilities.

> **🔧 Naming**: Selectors currently use PascalCase but should transition to kebab-case for new components.

### Base Selector

- **`Selector`** - Highly flexible, headless base component (19KB - complex) with popover/dialog modes, multi-select, custom rendering, and comprehensive search functionality

### Specialized Selectors

- **`SelectDocument`** - Document selection with preview and metadata
- **`SelectLocation`** - Geographic location selector with address search
- **`SelectOrganization`** - Organization selector with company preview
- **`SelectPerson`** - Person/contact selector with profile display
- **`SelectValue`** - Generic value selector for any entity type

---

## 📝 Fields

**Specialized form inputs** - Domain-specific input components with validation and formatting.

> **🔧 Naming**: Field components mix PascalCase (main components) and kebab-case (subdirectories). New fields should use kebab-case.

### Text-Based Fields

- **`Text`** - Basic text input field
- **`Email`** - Email validation and formatting
- **`URL`** - URL validation and formatting
- **`ID`** - Identifier input with specific formatting
- **`Description`** - Multi-line description field

### Numeric Fields

- **`Number`** - Numeric input with validation
- **`Currency`** - Money amount input with currency selection
- **`PhoneNumber`** - Phone number formatting and validation
- **`Duration`** - Time duration input (hours, minutes, etc.)
- **`Temperature`** - Temperature input with unit conversion
- **`Volume`** - Volume measurements with unit selection
- **`Weight`** - Weight input with unit conversion

### Date & Time Fields

- **7 date-time components** _(organized in `date-time/` subdirectory)_ - Specialized date/time inputs for various use cases

### File Management

- **4 file components** _(organized in `files/` subdirectory)_ - File upload, preview, and management interfaces

### Company Data

- **4 company components** _(organized in `company/` subdirectory)_ - Business-specific data entry fields

### Core Field Components

- **`Select`** - Generic selection field wrapper
- **`AddressAutocomplete`** - Address search with autocomplete (7KB - complex)

---

## 📄 Forms

**Complete form implementations** - Pre-built forms for common business entities.

> **🔧 Naming**: Form components use PascalCase but should transition to kebab-case for consistency.

### Entity Forms

- **`Address`** - Location and address data entry
- **`Contact`** - Contact information forms
- **`Document`** - Document metadata and file upload
- **`Entity`** - Generic entity creation form
- **`Person`** - Individual person data entry

### Specialized Form Groups

- **Organizations** _(subdirectory)_ (3 components) - Company and organization forms
- **Public** _(subdirectory)_ (3 components) - Public-facing form interfaces
- **Users** _(subdirectory)_ (2 components) - User profile and account forms

---

## 🔧 Shared Utilities

**Common reusable components** - Utility components used across the application.

> **🔧 Naming**: Shared utilities use PascalCase but new utilities should use kebab-case.

### Display Components

- **`Currency`** - Formatted currency display
- **`DateTime`** - Formatted date/time display with timezone
- **`TimeAgo`** - Relative time display ("2 hours ago")
- **`TimeRange`** - Time span display formatting
- **`TimeZone`** - Timezone display component
- **`Hours`** - Working hours display

### Interactive Utilities

- **`CopyButton`** - One-click copy to clipboard functionality
- **`PriceCounter`** - Animated price change indicator
- **`FloatingAvatar`** - Positioned avatar with status indicators

### Dialog & Modal Components

- **`DialogConfirmation`** - Confirmation dialog with actions
- **`DialogForm`** - Form dialog wrapper (5.5KB - complex)

### State & Feedback

- **`EmptyList`** - Empty state display for lists
- **`ErrorAlert`** - Error message display
- **`DashboardGreeting`** - Personalized dashboard welcome
- **`PaginatedCard`** - Card with built-in pagination (9.3KB - complex)

---

## 🏗️ Common Components

**Business domain components** - Components specific to business entities and workflows.

> **🔧 Naming**: Common components use PascalCase but should migrate to kebab-case for new components.

### Contact Management

- **`ContactEmail`** - Email display with actions
- **`ContactName`** - Name formatting and display
- **`ContactPhone`** - Phone number display with actions
- **`ContactInformation`** - Complete contact info display

### Location & Geographic

- **`LocationAddress`** - Address formatting and display
- **`PreviewLocation`** - Location preview card

### Document Management

- **`DocumentType`** - Document type classification
- **`DocumentViewer`** - Document preview interface
- **`PreviewDocument`** - Document preview card

### Organization & People

- **`PreviewContact`** - Contact preview card
- **`PreviewOrganization`** - Organization preview card (3.1KB - complex)
- **`PreviewPerson`** - Person preview card
- **`MemberRole`** - Role display for team members

---

## 📊 Tables

**Data table implementations** - Components for displaying and interacting with tabular data.

The flagship **`table`** component is a production-ready solution that combines powerful filtering, multi-row selection with specialized bulk actions, and fully configurable columns into a single, strongly-typed component that automatically adapts to your data structure and integrates seamlessly with pagination.

> **🔧 Naming**: Table components use kebab-case (modern) with some PascalCase legacy components.

### Core Table Components

- **`table`** _(kebab-case)_ - Advanced data table with sorting, filtering, and pagination (12.5KB - complex)
  - **Features**: Built-in search & filters, multi-select with bulk actions, configurable columns, full TypeScript integration
  - **Use cases**: Admin panels, data management, content listings, order tables
- **`data-table`** _(kebab-case)_ - Simplified data table wrapper for basic display needs

### Table Utilities

- **`actions`** _(kebab-case)_ - Row action buttons and menus
- **`columns`** _(kebab-case)_ - Column definition utilities
- **`helpers`** _(kebab-case)_ - Table helper functions and utilities (14KB - complex)

---

## 🧩 Blocks

**Complex composite components** - Large, feature-complete component groups for complete workflows and business processes.

> **🔧 Naming**: Blocks use kebab-case for directories and should use kebab-case for internal components going forward.

### Organization Management

- **org-invitations** _(kebab-case directory)_ (6 components) - Complete invitation workflow with search, filtering, and bulk actions
- **org-members** _(kebab-case directory)_ (6 components) - Member management interface with role-based controls
- **org-accounts** _(kebab-case directory)_ (2 components) - Organization account management utilities

### Identity & Verification

- **provider-id-card** _(kebab-case directory)_ (3 components) - Professional identity card with government ID verification support

### Content & Marketing

- **testimonials** _(kebab-case directory)_ (2 components) - Customer testimonial carousels with animated transitions

### Workflow & Process Management

- **negotiation-timeline** _(kebab-case directory)_ (5+ components) - Flexible timeline system for multi-step workflows, negotiations, and onboarding processes
- **negotiation-center** _(kebab-case directory)_ (7+ components) - _Work in progress_ - Advanced negotiation dashboard and workflow management

### System Components

- **`types.ts`** - Shared TypeScript definitions for blocks
- **`index.ts`** - Block component exports and utilities

---

## 🗂️ Additional Categories

### Authentication (8 components)

User authentication interfaces and flows.

### Editors (8 components)

Rich text and content editing interfaces.

### Layouts (6 components)

Page layout and structure components.

### Maps (5 components)

Geographic mapping and location components.

### Pages (4 components)

Complete page templates and layouts.

### Widgets (1 component)

Dashboard and sidebar widgets.

### Brand (2 components)

Brand-specific styling and components.

### Hooks (10 components)

Custom React hooks for common functionality.

### Styles (5 components)

Styling utilities and theme definitions.

---

## 🎨 Component Patterns

### Size Variants

Most interactive components support size variants: `sm`, `md`, `lg`, `xl`

### Styling Variants

Components typically include variants like: `default`, `outline`, `ghost`, `secondary`

### State Management

Many components support: `loading`, `pending`, `disabled`, `error` states

### Accessibility

All components include proper ARIA attributes, keyboard navigation, and screen reader support

### URL Integration

Search components automatically sync with URL parameters for bookmarkable states

---

_This catalog represents the current state of the AXA UI library. For implementation details and examples, refer to the individual component stories in Storybook._

> **🔧 Migration Note**: Existing block components use PascalCase but should be migrated to kebab-case files with PascalCase exports.

{/* {{ ... }} */}
