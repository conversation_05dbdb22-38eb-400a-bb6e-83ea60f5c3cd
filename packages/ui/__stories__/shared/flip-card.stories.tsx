import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import React, { useState } from "react";
import { EyeO<PERSON>, Info, RotateCcw, Shield } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/ui/primitives/avatar";
import { Badge } from "@/ui/primitives/badge";
import { But<PERSON> } from "@/ui/primitives/button";
import FlipCard from "@/ui/shared/flip-card";
import FloatingCard from "@/ui/shared/floating-card";

// Mock data extracted from the original component
const mockProvider = {
  id: "prov-001",
  name: "Dr. <PERSON>",
  specialty: "Cardiology",
  title: "Senior Cardiologist",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  licenseExpiry: "2025-12-31",
  profileImage: "/placeholder.svg?height=120&width=100",
  status: "active" as const,
};

const mockGovernmentID = {
  licenseNumber: "MD-NY-123456789",
  issueDate: "2020-01-15",
  expiryDate: "2025-12-31",
  issuingAuthority: "New York State Department of Health",
  npiNumber: "**********",
  deaNumber: "*********",
  state: "NEW YORK",
  licenseType: "MEDICAL LICENSE",
};

const meta: Meta<typeof FlipCard> = {
  title: "Shared/FlipCard",
  component: FlipCard,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A versatile flip card component with 3D hover effects using the `useFloatingHover` hook. Features customizable aspect ratios, sizes, and flip interactions. Content is completely dynamic through frontContent and backContent props.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    aspectRatio: {
      control: "select",
      options: ["square", "portrait", "landscape", "wide", "card", "photo"],
      description: "Aspect ratio of the card",
    },
    size: {
      control: "select",
      options: ["sm", "md", "lg", "xl", "2xl"],
      description: "Predefined size variants",
    },
    flipOnClick: {
      control: "boolean",
      description: "Enable flip on click interaction",
    },
    flipOnHover: {
      control: "boolean",
      description: "Enable flip on hover interaction",
    },
    perspective: {
      control: { type: "range", min: 500, max: 2000, step: 100 },
      description: "3D perspective value",
    },
    customWidth: {
      control: { type: "number", min: 200, max: 800, step: 50 },
      description: "Custom width in pixels",
    },
    customHeight: {
      control: { type: "number", min: 150, max: 600, step: 50 },
      description: "Custom height in pixels",
    },
  },
};

export default meta;
type Story = StoryObj<typeof FlipCard>;

// Simple front and back content
const SimpleFront = (
  <div className="flex h-full items-center justify-center bg-gradient-to-br from-blue-400 to-blue-600 text-white">
    <div className="text-center">
      <h3 className="text-2xl font-bold">Front Side</h3>
      <p className="mt-2 text-blue-100">Click to flip!</p>
    </div>
  </div>
);

const SimpleBack = (
  <div className="flex h-full items-center justify-center bg-gradient-to-br from-purple-400 to-purple-600 text-white">
    <div className="text-center">
      <h3 className="text-2xl font-bold">Back Side</h3>
      <p className="mt-2 text-purple-100">Click to flip back!</p>
    </div>
  </div>
);

export const Default: Story = {
  args: {
    frontContent: SimpleFront,
    backContent: SimpleBack,
    aspectRatio: "card",
    size: "md",
    flipOnClick: true,
    flipOnHover: false,
    perspective: 1000,
  },
};

export const HoverToFlip: Story = {
  args: {
    ...Default.args,
    flipOnClick: false,
    flipOnHover: true,
  },
  parameters: {
    docs: {
      description: {
        story: "Card flips automatically on hover instead of click.",
      },
    },
  },
};

export const DifferentAspectRatios: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-6 lg:grid-cols-3">
      <FlipCard
        aspectRatio="square"
        size="sm"
        frontContent={
          <div className="flex h-full items-center justify-center bg-red-500 text-white">
            <span className="font-bold">Square</span>
          </div>
        }
        backContent={
          <div className="flex h-full items-center justify-center bg-red-600 text-white">
            <span className="font-bold">1:1</span>
          </div>
        }
      />
      <FlipCard
        aspectRatio="card"
        size="sm"
        frontContent={
          <div className="flex h-full items-center justify-center bg-green-500 text-white">
            <span className="font-bold">Card</span>
          </div>
        }
        backContent={
          <div className="flex h-full items-center justify-center bg-green-600 text-white">
            <span className="font-bold">1.6:1</span>
          </div>
        }
      />
      <FlipCard
        aspectRatio="wide"
        size="sm"
        frontContent={
          <div className="flex h-full items-center justify-center bg-orange-500 text-white">
            <span className="font-bold">Wide</span>
          </div>
        }
        backContent={
          <div className="flex h-full items-center justify-center bg-orange-600 text-white">
            <span className="font-bold">16:9</span>
          </div>
        }
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different aspect ratios showcase the flexibility of the component.",
      },
    },
  },
};

// Government ID Card Example (extracted from original component)
function GovernmentIDCard() {
  const [showSensitiveData, setShowSensitiveData] = useState(false);

  const frontContent = (
    <div className="h-full bg-gradient-to-br from-gray-50 to-gray-200 p-4">
      {/* Background pattern lines */}
      <div className="absolute inset-0 opacity-10">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute h-px bg-blue-300"
            style={{
              top: `${(i + 1) * 5}%`,
              left: "0",
              right: "0",
              transform: `skew(-15deg)`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10">
        <div className="mb-3 flex items-start justify-between">
          <div>
            <h3 className="text-xs font-bold uppercase tracking-wider text-blue-900">
              {mockGovernmentID.state}
            </h3>
            <p className="text-xs font-semibold text-blue-800">
              {mockGovernmentID.licenseType}
            </p>
          </div>
          <div className="flex size-8 items-center justify-center rounded-full bg-blue-900">
            <Shield className="size-4 text-white" />
          </div>
        </div>

        <div className="space-y-2">
          <div>
            <p className="text-xs uppercase tracking-wide text-gray-600">
              Name
            </p>
            <p className="text-sm font-bold text-gray-900">
              {mockProvider.name}
            </p>
          </div>

          <div>
            <p className="text-xs uppercase tracking-wide text-gray-600">
              Specialty
            </p>
            <p className="text-sm font-semibold text-gray-800">
              {mockProvider.specialty}
            </p>
          </div>

          <div className="mt-3 grid grid-cols-2 gap-2">
            <div>
              <p className="text-xs uppercase tracking-wide text-gray-600">
                Issue
              </p>
              <p className="font-mono text-xs text-gray-800">
                {mockGovernmentID.issueDate}
              </p>
            </div>
            <div>
              <p className="text-xs uppercase tracking-wide text-gray-600">
                Expires
              </p>
              <p className="font-mono text-xs text-gray-800">
                {mockGovernmentID.expiryDate}
              </p>
            </div>
          </div>
        </div>

        <div className="mt-4 border-t border-gray-200 pt-2">
          <p className="text-center text-xs text-gray-500">
            {mockGovernmentID.issuingAuthority}
          </p>
        </div>
      </div>
    </div>
  );

  const backContent = (
    <div className="h-full bg-gradient-to-br from-blue-50 to-blue-200 p-4">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200" />
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-px bg-blue-300"
            style={{
              left: `${(i + 1) * 6.67}%`,
              top: "0",
              bottom: "0",
              transform: `skew(0deg, 15deg)`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 flex h-full flex-col">
        <div className="mb-4 flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Avatar className="size-16 border-2 border-white shadow-lg">
              <AvatarImage
                src={mockProvider.profileImage || "/placeholder.svg"}
                alt={mockProvider.name}
              />
              <AvatarFallback className="bg-blue-100 font-semibold text-blue-600">
                {mockProvider.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-bold text-gray-900">
                {mockProvider.name}
              </p>
              <p className="text-xs text-gray-600">{mockProvider.specialty}</p>
            </div>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setShowSensitiveData(!showSensitiveData);
            }}
            className="size-8 rounded-full p-0 hover:bg-blue-100"
          >
            <Info className="size-4 text-blue-600" />
          </Button>
        </div>

        {showSensitiveData && (
          <div className="space-y-3 rounded-lg border border-blue-200 bg-white/80 p-3 text-xs backdrop-blur-sm">
            <div className="mb-2 flex items-center gap-2">
              <Shield className="size-3 text-red-500" />
              <span className="font-semibold uppercase tracking-wide text-red-600">
                Sensitive Data
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowSensitiveData(false);
                }}
                className="ml-auto size-4 p-0"
              >
                <EyeOff className="size-3" />
              </Button>
            </div>

            <div className="space-y-2">
              <div>
                <span className="font-medium text-gray-500">License #:</span>
                <p className="font-mono text-gray-900">
                  {mockGovernmentID.licenseNumber}
                </p>
              </div>
              <div>
                <span className="font-medium text-gray-500">NPI:</span>
                <p className="font-mono text-gray-900">
                  {mockGovernmentID.npiNumber}
                </p>
              </div>
              {mockGovernmentID.deaNumber && (
                <div>
                  <span className="font-medium text-gray-500">DEA:</span>
                  <p className="font-mono text-gray-900">
                    {mockGovernmentID.deaNumber}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="mt-auto">
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <RotateCcw className="size-3" />
            <span>Click to flip back</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <FlipCard
      aspectRatio="card"
      size="lg"
      frontContent={frontContent}
      backContent={backContent}
    />
  );
}

export const GovernmentIDExample: Story = {
  render: () => <GovernmentIDCard />,
  parameters: {
    docs: {
      description: {
        story:
          "Complex example showing a government ID card with interactive elements, extracted from the original component as a usage example.",
      },
    },
  },
};

export const CustomSizing: Story = {
  args: {
    ...Default.args,
    customWidth: 400,
    customHeight: 250,
  },
  parameters: {
    docs: {
      description: {
        story: "Custom pixel-based sizing overrides the preset size variants.",
      },
    },
  },
};

export const BusinessCard: Story = {
  render: () => (
    <FlipCard
      aspectRatio="card"
      size="md"
      frontContent={
        <div className="h-full bg-gradient-to-br from-slate-900 to-slate-700 p-6 text-white">
          <div className="flex h-full flex-col justify-between">
            <div className="self-end">
              <Badge variant="secondary" className="bg-white/20 text-white">
                Premium
              </Badge>
            </div>
            <div>
              <h3 className="text-xl font-bold">John Doe</h3>
              <p className="text-slate-300">Senior Developer</p>
              <p className="mt-2 text-sm text-slate-400">Acme Corporation</p>
            </div>
          </div>
        </div>
      }
      backContent={
        <div className="h-full bg-gradient-to-br from-slate-800 to-slate-600 p-6 text-white">
          <div className="flex h-full flex-col justify-center space-y-3">
            <div>
              <p className="text-xs text-slate-400">Email</p>
              <p className="text-sm"><EMAIL></p>
            </div>
            <div>
              <p className="text-xs text-slate-400">Phone</p>
              <p className="text-sm">+****************</p>
            </div>
            <div>
              <p className="text-xs text-slate-400">Website</p>
              <p className="text-sm">www.acme.com</p>
            </div>
          </div>
        </div>
      }
    />
  ),
  parameters: {
    docs: {
      description: {
        story:
          "A business card example showing professional contact information.",
      },
    },
  },
};

export const DarkModeGovernmentID: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="space-y-4">
        <h3 className="text-center text-lg font-semibold text-gray-900 dark:text-gray-100">
          Government ID Card - Dark Mode
        </h3>

        {/* Light mode */}
        <div className="space-y-2">
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            Light Mode
          </p>
          <div className="rounded-lg bg-white p-8">
            <FlipCard
              aspectRatio="card"
              size="lg"
              frontContent={
                <div className="h-full bg-gradient-to-br from-gray-50 to-gray-200 p-4">
                  <div className="absolute inset-0 opacity-10">
                    {Array.from({ length: 20 }).map((_, i) => (
                      <div
                        key={i}
                        className="absolute h-px bg-blue-300"
                        style={{
                          top: `${(i + 1) * 5}%`,
                          left: "0",
                          right: "0",
                          transform: `skew(-15deg)`,
                        }}
                      />
                    ))}
                  </div>
                  <div className="relative z-10">
                    <div className="mb-3 flex items-start justify-between">
                      <div>
                        <h3 className="text-xs font-bold uppercase tracking-wider text-blue-900">
                          NEW YORK
                        </h3>
                        <p className="text-xs font-semibold text-blue-800">
                          MEDICAL LICENSE
                        </p>
                      </div>
                      <div className="flex size-8 items-center justify-center rounded-full bg-blue-900">
                        <Shield className="size-4 text-white" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div>
                        <p className="text-xs uppercase tracking-wide text-gray-600">
                          Name
                        </p>
                        <p className="text-sm font-bold text-gray-900">
                          Dr. Sarah Johnson
                        </p>
                      </div>
                      <div>
                        <p className="text-xs uppercase tracking-wide text-gray-600">
                          Specialty
                        </p>
                        <p className="text-sm font-semibold text-gray-800">
                          Cardiology
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              }
              backContent={
                <div className="h-full bg-gradient-to-br from-blue-50 to-blue-200 p-4">
                  <div className="relative z-10 flex h-full flex-col">
                    <div className="mb-4 flex items-center gap-2">
                      <div className="flex size-12 items-center justify-center rounded-full bg-blue-100">
                        <span className="font-semibold text-blue-600">SJ</span>
                      </div>
                      <div>
                        <p className="text-sm font-bold text-gray-900">
                          Dr. Sarah Johnson
                        </p>
                        <p className="text-xs text-gray-600">Cardiology</p>
                      </div>
                    </div>
                    <div className="mt-auto">
                      <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
                        <RotateCcw className="size-3" />
                        <span>Click to flip back</span>
                      </div>
                    </div>
                  </div>
                </div>
              }
            />
          </div>
        </div>

        {/* Dark mode */}
        <div className="space-y-2">
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            Dark Mode
          </p>
          <div className="rounded-lg bg-gray-900 p-8">
            <div className="dark">
              <FlipCard
                aspectRatio="card"
                size="lg"
                frontContent={
                  <div className="h-full bg-gradient-to-br from-gray-700 to-gray-800 p-4">
                    <div className="absolute inset-0 opacity-10">
                      {Array.from({ length: 20 }).map((_, i) => (
                        <div
                          key={i}
                          className="absolute h-px bg-blue-400"
                          style={{
                            top: `${(i + 1) * 5}%`,
                            left: "0",
                            right: "0",
                            transform: `skew(-15deg)`,
                          }}
                        />
                      ))}
                    </div>
                    <div className="relative z-10">
                      <div className="mb-3 flex items-start justify-between">
                        <div>
                          <h3 className="text-xs font-bold uppercase tracking-wider text-blue-100">
                            NEW YORK
                          </h3>
                          <p className="text-xs font-semibold text-blue-200">
                            MEDICAL LICENSE
                          </p>
                        </div>
                        <div className="flex size-8 items-center justify-center rounded-full bg-blue-600">
                          <Shield className="size-4 text-white" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div>
                          <p className="text-xs uppercase tracking-wide text-gray-300">
                            Name
                          </p>
                          <p className="text-sm font-bold text-gray-100">
                            Dr. Sarah Johnson
                          </p>
                        </div>
                        <div>
                          <p className="text-xs uppercase tracking-wide text-gray-300">
                            Specialty
                          </p>
                          <p className="text-sm font-semibold text-gray-200">
                            Cardiology
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                }
                backContent={
                  <div className="h-full bg-gradient-to-br from-blue-900/50 to-blue-800/50 p-4">
                    <div className="relative z-10 flex h-full flex-col">
                      <div className="mb-4 flex items-center gap-2">
                        <div className="flex size-12 items-center justify-center rounded-full bg-blue-200">
                          <span className="font-semibold text-blue-800">
                            SJ
                          </span>
                        </div>
                        <div>
                          <p className="text-sm font-bold text-gray-100">
                            Dr. Sarah Johnson
                          </p>
                          <p className="text-xs text-gray-300">Cardiology</p>
                        </div>
                      </div>
                      <div className="mt-auto">
                        <div className="flex items-center justify-center gap-2 text-xs text-gray-400">
                          <RotateCcw className="size-3" />
                          <span>Click to flip back</span>
                        </div>
                      </div>
                    </div>
                  </div>
                }
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstration of FlipCard component with government ID card styling in both light and dark modes, showing proper contrast and readability.",
      },
    },
  },
};

export const WithFloatingCards: Story = {
  render: () => (
    <FlipCard
      aspectRatio="card"
      size="lg"
      flipOnClick={true}
      frontContent={
        <FloatingCard
          aspectRatio="card"
          size="lg"
          background={null} // No background since FlipCard handles the container
          className="border-0 shadow-none" // Remove conflicting styles
        >
          <div className="flex h-full flex-col justify-between p-2">
            <div className="self-end">
              <Badge variant="secondary" className="bg-white/20 text-white">
                Front Card
              </Badge>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold text-white">Front Side</h3>
              <p className="text-slate-300">Floating + Flip</p>
              <p className="mt-2 text-sm text-slate-400">Click to flip!</p>
            </div>
          </div>
        </FloatingCard>
      }
      backContent={
        <FloatingCard
          aspectRatio="card"
          size="lg"
          background={null} // No background since FlipCard handles the container
          className="border-0 shadow-none" // Remove conflicting styles
        >
          <div className="flex h-full flex-col justify-center space-y-3 p-4">
            <div className="text-center">
              <Badge variant="secondary" className="bg-white/20 text-white">
                Back Card
              </Badge>
            </div>
            <div className="space-y-2 text-center">
              <div>
                <p className="text-xs text-slate-400">Back Side Info</p>
                <p className="text-sm text-white">Floating effects active</p>
              </div>
              <div>
                <p className="text-xs text-slate-400">Interaction</p>
                <p className="text-sm text-white">Hover to see 3D effect</p>
              </div>
              <div>
                <p className="text-xs text-slate-400">Action</p>
                <p className="text-sm text-white">Click to flip back</p>
              </div>
            </div>
          </div>
        </FloatingCard>
      }
    />
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Example of using FloatingCard components as content for both sides of a FlipCard, combining flip and floating hover effects.",
      },
    },
  },
};

export const FloatingCardComposition: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="text-center">
        <h3 className="mb-4 text-lg font-semibold">
          FloatingCard + FlipCard Composition
        </h3>
        <p className="mb-6 text-sm text-gray-600">
          Testing different combinations and configurations
        </p>
      </div>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        {/* Version 1: No background, clean composition */}
        <div className="space-y-2">
          <p className="text-center text-sm font-medium">Clean Composition</p>
          <FlipCard
            aspectRatio="card"
            size="md"
            frontContent={
              <FloatingCard
                aspectRatio="card"
                size="md"
                background={null}
                className="border-0 shadow-none"
              >
                <div className="flex h-full items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-700 text-white">
                  <div className="text-center">
                    <h4 className="font-bold">Clean Front</h4>
                    <p className="mt-1 text-xs">No background overlap</p>
                  </div>
                </div>
              </FloatingCard>
            }
            backContent={
              <FloatingCard
                aspectRatio="card"
                size="md"
                background={null}
                className="border-0 shadow-none"
              >
                <div className="flex h-full items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-700 text-white">
                  <div className="text-center">
                    <h4 className="font-bold">Clean Back</h4>
                    <p className="mt-1 text-xs">Smooth integration</p>
                  </div>
                </div>
              </FloatingCard>
            }
          />
        </div>

        {/* Version 2: With backgrounds */}
        <div className="space-y-2">
          <p className="text-center text-sm font-medium">With Backgrounds</p>
          <FlipCard
            aspectRatio="card"
            size="md"
            frontContent={
              <FloatingCard
                aspectRatio="card"
                size="md"
                // Default background will be applied
              >
                <div className="text-center">
                  <Badge className="mb-2">Front</Badge>
                  <h4 className="font-bold">With Background</h4>
                  <p className="mt-1 text-xs">Official tender pattern</p>
                </div>
              </FloatingCard>
            }
            backContent={
              <FloatingCard
                aspectRatio="card"
                size="md"
                // Default background will be applied
              >
                <div className="text-center">
                  <Badge className="mb-2">Back</Badge>
                  <h4 className="font-bold">With Background</h4>
                  <p className="mt-1 text-xs">Official tender pattern</p>
                </div>
              </FloatingCard>
            }
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Comprehensive testing of FloatingCard and FlipCard composition with different background configurations to identify the best integration approach.",
      },
    },
  },
};
