import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { useState } from "react";
import { faker } from "@faker-js/faker";
import { Bell, FileText, Plus, Settings, Users } from "lucide-react";

import { Badge } from "../../src/primitives/badge";
import { But<PERSON> } from "../../src/primitives/button";
import { Card, CardContent } from "../../src/primitives/card";
import {
  PaginatedCardContent,
  PaginatedCardFooter,
  PaginatedCardHeader,
  PaginatedCardRoot,
} from "../../src/shared/PaginatedCard";

const meta: Meta<typeof PaginatedCardRoot> = {
  title: "Shared/PaginatedCard",
  component: PaginatedCardRoot,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A flexible, reusable card component with built-in pagination, loading states, and consistent structure for displaying lists of data.",
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof PaginatedCardRoot>;

// Mock data generators
interface MockItem {
  id: string;
  title: string;
  description: string;
  status: "active" | "inactive" | "pending";
  createdAt: string;
}

function createMockItem(): MockItem {
  return {
    id: faker.string.uuid(),
    title: faker.company.name(),
    description: faker.lorem.sentence(),
    status: faker.helpers.arrayElement(["active", "inactive", "pending"]),
    createdAt: faker.date.recent().toISOString(),
  };
}

function createMockItems(count: number): MockItem[] {
  return Array.from({ length: count }, () => createMockItem());
}

// Sample item card component
function ItemCard({ item }: { item: MockItem }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "inactive":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Card className="p-4 transition-all hover:shadow-md">
      <CardContent className="p-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="mb-1 text-sm font-semibold">{item.title}</h3>
            <p className="mb-2 text-xs text-muted-foreground">
              {item.description}
            </p>
            <div className="text-xs text-muted-foreground">
              Created {new Date(item.createdAt).toLocaleDateString()}
            </div>
          </div>
          <Badge
            variant="outline"
            className={`ml-2 text-xs ${getStatusColor(item.status)}`}
          >
            {item.status}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}

// Interactive story with state management
export const Interactive: Story = {
  render: () => {
    const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 6 });
    const [loading, setLoading] = useState(false);

    const allItems = createMockItems(47);
    const total = allItems.length;
    const startIndex = pagination.pageIndex * pagination.pageSize;
    const currentItems = allItems.slice(
      startIndex,
      startIndex + pagination.pageSize,
    );

    const handleRefresh = () => {
      setLoading(true);
      setTimeout(() => setLoading(false), 1500);
    };

    return (
      <PaginatedCardRoot>
        <PaginatedCardHeader
          icon={<FileText className="size-5 text-muted-foreground" />}
          title="Project Items"
          count={total}
          description="Manage your project items and their status"
          loading={loading}
          actions={
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                Refresh
              </Button>
              <Button size="sm">
                <Plus className="mr-2 size-4" />
                Add Item
              </Button>
            </div>
          }
        />

        <PaginatedCardContent
          isLoading={loading}
          error={null}
          isEmpty={currentItems.length === 0}
          emptyComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <FileText className="mb-4 size-12 text-muted-foreground" />
              <p className="text-muted-foreground">No items found</p>
            </div>
          }
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {currentItems.map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </PaginatedCardContent>

        <PaginatedCardFooter
          pagination={pagination}
          setPagination={setPagination}
          totalItems={total}
          itemNoun={{ singular: "item", plural: "items" }}
          loading={loading}
        />
      </PaginatedCardRoot>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive example with working pagination, loading states, and actions.",
      },
    },
  },
};

// Basic usage
export const Basic: Story = {
  render: () => {
    const items = createMockItems(6);

    return (
      <PaginatedCardRoot>
        <PaginatedCardHeader
          icon={<Users className="size-5 text-muted-foreground" />}
          title="Team Members"
          count={items.length}
          description="View and manage team members"
        />

        <PaginatedCardContent isLoading={false} error={null} isEmpty={false}>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {items.map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </PaginatedCardContent>
      </PaginatedCardRoot>
    );
  },
};

// Loading state
export const Loading: Story = {
  render: () => (
    <PaginatedCardRoot>
      <PaginatedCardHeader
        loading={true}
        icon={<FileText className="size-5 text-muted-foreground" />}
        title="Documents"
        count={0}
        description="Manage your documents and files"
      />
    </PaginatedCardRoot>
  ),
};

// Empty state
export const Empty: Story = {
  render: () => (
    <PaginatedCardRoot>
      <PaginatedCardHeader
        icon={<Bell className="size-5 text-muted-foreground" />}
        title="Notifications"
        count={0}
        description="Stay updated with the latest notifications"
        actions={
          <Button variant="outline" size="sm">
            <Settings className="mr-2 size-4" />
            Settings
          </Button>
        }
      />

      <PaginatedCardContent
        isLoading={false}
        error={null}
        isEmpty={true}
        emptyComponent={
          <div className="flex flex-col items-center justify-center py-16">
            <Bell className="mb-4 size-16 text-muted-foreground" />
            <h3 className="mb-2 text-lg font-semibold">No notifications</h3>
            <p className="max-w-sm text-center text-muted-foreground">
              You're all caught up! New notifications will appear here when you
              receive them.
            </p>
          </div>
        }
      >
        <div />
      </PaginatedCardContent>
    </PaginatedCardRoot>
  ),
};

// Error state
export const ErrorState: Story = {
  render: () => (
    <PaginatedCardRoot>
      <PaginatedCardHeader
        icon={<FileText className="size-5 text-muted-foreground" />}
        title="Documents"
        count={0}
        description="Manage your documents and files"
      />

      <PaginatedCardContent
        isLoading={false}
        error={
          new Error(
            "Failed to load documents. Please check your connection and try again.",
          )
        }
        isEmpty={true}
      >
        <div />
      </PaginatedCardContent>
    </PaginatedCardRoot>
  ),
};

// Different grid layouts
export const SingleColumn: Story = {
  render: () => {
    const items = createMockItems(4);

    return (
      <PaginatedCardRoot>
        <PaginatedCardHeader
          title="Single Column Layout"
          count={items.length}
          description="Items displayed in a single column"
        />

        <PaginatedCardContent isLoading={false} error={null} isEmpty={false}>
          <div className="grid grid-cols-1 gap-4">
            {items.map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </PaginatedCardContent>
      </PaginatedCardRoot>
    );
  },
};

export const FourColumns: Story = {
  render: () => {
    const items = createMockItems(12);

    return (
      <PaginatedCardRoot>
        <PaginatedCardHeader
          title="Four Column Layout"
          count={items.length}
          description="Items displayed in four columns on larger screens"
        />

        <PaginatedCardContent isLoading={false} error={null} isEmpty={false}>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {items.map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </PaginatedCardContent>
      </PaginatedCardRoot>
    );
  },
};

// With pagination
export const WithPagination: Story = {
  render: () => {
    const [pagination, setPagination] = useState({ pageIndex: 1, pageSize: 4 });
    const items = createMockItems(4);
    const total = 27;

    return (
      <PaginatedCardRoot>
        <PaginatedCardHeader
          icon={<FileText className="size-5 text-muted-foreground" />}
          title="Paginated Items"
          count={total}
          description="Example with pagination controls"
        />

        <PaginatedCardContent isLoading={false} error={null} isEmpty={false}>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {items.map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </PaginatedCardContent>

        <PaginatedCardFooter
          pagination={pagination}
          setPagination={setPagination}
          totalItems={total}
          itemNoun={{ singular: "item", plural: "items" }}
          loading={false}
        />
      </PaginatedCardRoot>
    );
  },
};

// Custom content examples
export const CustomContent: Story = {
  render: () => (
    <PaginatedCardRoot>
      <PaginatedCardHeader
        title="Custom Content Example"
        count={3}
        description="Showing different types of content in cards"
        actions={
          <Button variant="outline" size="sm">
            Customize
          </Button>
        }
      />

      <PaginatedCardContent isLoading={false} error={null} isEmpty={false}>
        <div className="space-y-4">
          {/* Custom content examples */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card className="p-6 text-center">
              <div className="text-2xl font-bold text-blue-600">142</div>
              <div className="text-sm text-muted-foreground">Total Users</div>
            </Card>

            <Card className="p-6 text-center">
              <div className="text-2xl font-bold text-green-600">89%</div>
              <div className="text-sm text-muted-foreground">
                Completion Rate
              </div>
            </Card>

            <Card className="p-6 text-center">
              <div className="text-2xl font-bold text-purple-600">$12.4k</div>
              <div className="text-sm text-muted-foreground">Revenue</div>
            </Card>
          </div>

          <Card className="p-6">
            <h4 className="mb-2 font-semibold">Recent Activity</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>New user registration</span>
                <span className="text-muted-foreground">2 min ago</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Payment processed</span>
                <span className="text-muted-foreground">5 min ago</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Report generated</span>
                <span className="text-muted-foreground">10 min ago</span>
              </div>
            </div>
          </Card>
        </div>
      </PaginatedCardContent>
    </PaginatedCardRoot>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Example showing how PaginatedCard can be used with different types of custom content beyond simple lists.",
      },
    },
  },
};
