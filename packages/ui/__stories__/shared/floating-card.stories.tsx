import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { GradientDot, OfficialTenderBackground } from "@/ui/design";
import FloatingCard from "@/ui/shared/floating-card";

const meta: Meta<typeof FloatingCard> = {
  title: "Shared/FloatingCard",
  component: FloatingCard,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A versatile floating card component with 3D hover effects, customizable aspect ratios, sizes, and custom dimensions. Features a stable hover detection area with an internal transforming card to prevent edge flapping. Uses the modular design system for background patterns.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    aspectRatio: {
      control: "select",
      options: [
        "square",
        "portrait",
        "landscape",
        "wide",
        "ultrawide",
        "photo",
        "video",
      ],
      description: "Predefined aspect ratio for the card",
    },
    size: {
      control: "select",
      options: ["sm", "md", "lg", "xl", "2xl"],
      description: "T-shirt size for the card dimensions",
    },
    customWidth: {
      control: { type: "number", min: 100, max: 800, step: 10 },
      description: "Custom width in pixels (overrides size variant)",
    },
    customHeight: {
      control: { type: "number", min: 100, max: 800, step: 10 },
      description: "Custom height in pixels (overrides size variant)",
    },
    maxRotation: {
      control: { type: "number", min: 0, max: 45, step: 1 },
      description: "Maximum rotation angle in degrees",
    },
    perspective: {
      control: { type: "number", min: 500, max: 2000, step: 100 },
      description: "CSS perspective value for 3D effect",
    },
    background: {
      control: false,
      description:
        "Custom background component (defaults to OfficialTenderBackground)",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
} satisfies Meta<typeof FloatingCard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story with background
export const Default: Story = {
  args: {
    aspectRatio: "portrait",
    size: "md",
  },
};

// Empty card without background
export const EmptyCard: Story = {
  args: {
    aspectRatio: "portrait",
    size: "md",
    background: null,
  },
  parameters: {
    docs: {
      description: {
        story: "A card without any background, ready for custom content.",
      },
    },
  },
};

// Different aspect ratios
export const AspectRatios: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-8 lg:grid-cols-4">
      <div className="space-y-2">
        <FloatingCard aspectRatio="square" size="sm" />
        <p className="text-center text-sm text-gray-600">Square</p>
      </div>
      <div className="space-y-2">
        <FloatingCard aspectRatio="portrait" size="sm" />
        <p className="text-center text-sm text-gray-600">Portrait (3:4)</p>
      </div>
      <div className="space-y-2">
        <FloatingCard aspectRatio="landscape" size="sm" />
        <p className="text-center text-sm text-gray-600">Landscape (4:3)</p>
      </div>
      <div className="space-y-2">
        <FloatingCard aspectRatio="wide" size="sm" />
        <p className="text-center text-sm text-gray-600">Wide (16:9)</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different aspect ratio variants showing how the background patterns adapt automatically.",
      },
    },
  },
};

// Different sizes
export const Sizes: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-3 lg:grid-cols-5">
      <div className="space-y-2">
        <FloatingCard size="sm" />
        <p className="text-center text-sm text-gray-600">Small</p>
      </div>
      <div className="space-y-2">
        <FloatingCard size="md" />
        <p className="text-center text-sm text-gray-600">Medium</p>
      </div>
      <div className="space-y-2">
        <FloatingCard size="lg" />
        <p className="text-center text-sm text-gray-600">Large</p>
      </div>
      <div className="space-y-2">
        <FloatingCard size="xl" />
        <p className="text-center text-sm text-gray-600">Extra Large</p>
      </div>
      <div className="space-y-2">
        <FloatingCard size="2xl" />
        <p className="text-center text-sm text-gray-600">2X Large</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "T-shirt sizes from small to 2XL for different use cases.",
      },
    },
  },
};

// Custom dimensions
export const CustomDimensions: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
      <div className="space-y-2">
        <FloatingCard customWidth={200} customHeight={200} />
        <p className="text-center text-sm text-gray-600">200x200px</p>
      </div>
      <div className="space-y-2">
        <FloatingCard customWidth={300} customHeight={150} />
        <p className="text-center text-sm text-gray-600">300x150px</p>
      </div>
      <div className="space-y-2">
        <FloatingCard customWidth={250} customHeight={400} />
        <p className="text-center text-sm text-gray-600">250x400px</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Custom width and height dimensions in pixels, which override the size and aspectRatio variants.",
      },
    },
  },
};

// Custom content examples
export const WithCustomContent: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
      <div className="space-y-2">
        <FloatingCard
          background={null}
          className="flex items-center justify-center"
        >
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-800">Custom Content</h3>
            <p className="text-gray-600">No background, just content</p>
          </div>
        </FloatingCard>
        <p className="text-center text-sm text-gray-600">Custom Content Only</p>
      </div>
      <div className="space-y-2">
        <FloatingCard className="flex items-end justify-center pb-12">
          <div className="rounded-lg bg-white/90 p-4 backdrop-blur-sm">
            <h3 className="text-lg font-semibold text-gray-800">
              Overlay Content
            </h3>
            <p className="text-sm text-gray-600">Content over background</p>
          </div>
        </FloatingCard>
        <p className="text-center text-sm text-gray-600">
          Overlay on Background
        </p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Examples of layering custom content with or without the background.",
      },
    },
  },
};

// Background customization with custom components
export const BackgroundCustomization: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
      <div className="space-y-2">
        <FloatingCard
          size="sm"
          background={
            <OfficialTenderBackground
              className="absolute inset-0"
              opacity={0.3}
            />
          }
        >
          <div className="flex h-full items-center justify-center">
            <span className="text-lg font-semibold">30% Opacity</span>
          </div>
        </FloatingCard>
        <p className="text-center text-sm text-gray-600">Subtle Background</p>
      </div>
      <div className="space-y-2">
        <FloatingCard
          size="sm"
          background={
            <OfficialTenderBackground
              className="absolute inset-0"
              showWavyLines={false}
              opacity={0.8}
            />
          }
        >
          <div className="flex h-full items-center justify-center">
            <span className="text-lg font-semibold">Minimal Pattern</span>
          </div>
        </FloatingCard>
        <p className="text-center text-sm text-gray-600">Reduced Patterns</p>
      </div>
      <div className="space-y-2">
        <FloatingCard
          size="sm"
          background={
            <OfficialTenderBackground
              className="absolute inset-0"
              showTopographic={false}
              showDiagonalLines={false}
            />
          }
        >
          <div className="flex h-full items-center justify-center">
            <span className="text-lg font-semibold">Custom Style</span>
          </div>
        </FloatingCard>
        <p className="text-center text-sm text-gray-600">Custom Patterns</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Custom background configurations using custom background components for granular control over patterns and opacity.",
      },
    },
  },
};

// Different rotation and perspective settings
export const CustomPhysics: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
      <div className="space-y-2">
        <FloatingCard maxRotation={5} size="sm">
          <div className="flex h-full items-center justify-center">
            <span className="text-sm font-medium">Subtle Hover</span>
          </div>
        </FloatingCard>
        <p className="text-center text-sm text-gray-600">Subtle (5°)</p>
      </div>
      <div className="space-y-2">
        <FloatingCard maxRotation={15} size="sm">
          <div className="flex h-full items-center justify-center">
            <span className="text-sm font-medium">Default Hover</span>
          </div>
        </FloatingCard>
        <p className="text-center text-sm text-gray-600">Default (15°)</p>
      </div>
      <div className="space-y-2">
        <FloatingCard maxRotation={30} size="sm">
          <div className="flex h-full items-center justify-center">
            <span className="text-sm font-medium">Dramatic Hover</span>
          </div>
        </FloatingCard>
        <p className="text-center text-sm text-gray-600">Dramatic (30°)</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different rotation intensities for various interaction preferences.",
      },
    },
  },
};

// Complex content example
export const ComplexContent: Story = {
  render: () => (
    <div className="flex justify-center">
      <FloatingCard
        aspectRatio="portrait"
        size="lg"
        background={
          <OfficialTenderBackground
            className="absolute inset-0"
            opacity={0.4}
          />
        }
      >
        <div className="flex h-full flex-col justify-between">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800">Product Card</h2>
            <p className="text-gray-600">Beautiful floating design</p>
          </div>

          <div className="space-y-4">
            <div className="rounded-lg bg-white/80 p-4 backdrop-blur-sm">
              <h3 className="font-semibold">Features</h3>
              <ul className="mt-2 text-sm text-gray-600">
                <li>• 3D hover effects</li>
                <li>• Stable hover detection</li>
                <li>• Modular design system</li>
                <li>• Custom dimensions</li>
              </ul>
            </div>

            <div className="flex gap-2">
              <button className="flex-1 rounded-lg bg-teal-500 px-4 py-2 text-white hover:bg-teal-600">
                Primary
              </button>
              <button className="flex-1 rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50">
                Secondary
              </button>
            </div>
          </div>
        </div>
      </FloatingCard>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "A complex example showing how to layer rich content over the background with reduced opacity.",
      },
    },
  },
};

// Advanced composition examples
export const CompositionExamples: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
      <div className="space-y-2">
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-b from-gray-100 to-gray-200 p-6 shadow-2xl dark:from-gray-800 dark:to-gray-900">
          <OfficialTenderBackground className="absolute inset-0">
            <GradientDot variant="purple" size="xl" />
          </OfficialTenderBackground>
          <div className="relative z-10 text-center">
            <h4 className="font-bold">Custom Color</h4>
            <p className="text-sm text-gray-600">Purple accent</p>
          </div>
        </div>
        <p className="text-center text-sm text-gray-600">Direct Composition</p>
      </div>

      <div className="space-y-2">
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-b from-gray-100 to-gray-200 p-6 shadow-2xl dark:from-gray-800 dark:to-gray-900">
          <OfficialTenderBackground className="absolute inset-0">
            <GradientDot variant="emerald" position="top-right" size="lg" />
            <GradientDot variant="amber" position="bottom-left" size="md" />
          </OfficialTenderBackground>
          <div className="relative z-10 text-center">
            <h4 className="font-bold">Multiple Dots</h4>
            <p className="text-sm text-gray-600">Corner accents</p>
          </div>
        </div>
        <p className="text-center text-sm text-gray-600">Multiple Accents</p>
      </div>

      <div className="space-y-2">
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-b from-gray-100 to-gray-200 p-6 shadow-2xl dark:from-gray-800 dark:to-gray-900">
          <OfficialTenderBackground className="absolute inset-0">
            <GradientDot customSize={64} variant="blue">
              <div className="flex h-full items-center justify-center text-lg font-bold text-white">
                💎
              </div>
            </GradientDot>
          </OfficialTenderBackground>
          <div className="relative z-10 text-center">
            <h4 className="font-bold">Icon Content</h4>
            <p className="text-sm text-gray-600">Custom center</p>
          </div>
        </div>
        <p className="text-center text-sm text-gray-600">Icon in Dot</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Advanced composition examples showing how to use OfficialTenderBackground and GradientDot directly for maximum flexibility.",
      },
    },
  },
};

// Playground
export const Playground: Story = {
  args: {
    aspectRatio: "portrait",
    size: "md",
    maxRotation: 15,
    perspective: 1000,
  },
  render: (args) => (
    <FloatingCard {...args}>
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h3 className="text-xl font-bold text-gray-800">Interactive Demo</h3>
          <p className="text-gray-600">Adjust controls to experiment</p>
        </div>
      </div>
    </FloatingCard>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Interactive playground to test all component props and variants.",
      },
    },
  },
};

export const DarkModeDemo: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="space-y-4">
        <h3 className="text-center text-lg font-semibold text-gray-900 dark:text-gray-100">
          Light & Dark Mode Comparison
        </h3>

        {/* Light mode card */}
        <div className="space-y-2">
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            Light Mode
          </p>
          <div className="rounded-lg bg-white p-8">
            <FloatingCard aspectRatio="landscape" size="md">
              <div className="space-y-2 text-center">
                <h4 className="text-xl font-bold text-gray-900">
                  Light Mode Card
                </h4>
                <p className="text-gray-600">
                  Beautiful gradients and patterns
                </p>
                <div className="mt-4 flex justify-center gap-2">
                  <div className="size-8 rounded-full bg-blue-500"></div>
                  <div className="size-8 rounded-full bg-green-500"></div>
                  <div className="size-8 rounded-full bg-purple-500"></div>
                </div>
              </div>
            </FloatingCard>
          </div>
        </div>

        {/* Dark mode card */}
        <div className="space-y-2">
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            Dark Mode
          </p>
          <div className="rounded-lg bg-gray-900 p-8">
            <div className="dark">
              <FloatingCard aspectRatio="landscape" size="md">
                <div className="space-y-2 text-center">
                  <h4 className="text-xl font-bold text-gray-100">
                    Dark Mode Card
                  </h4>
                  <p className="text-gray-300">
                    Adapts beautifully to dark themes
                  </p>
                  <div className="mt-4 flex justify-center gap-2">
                    <div className="size-8 rounded-full bg-blue-400"></div>
                    <div className="size-8 rounded-full bg-green-400"></div>
                    <div className="size-8 rounded-full bg-purple-400"></div>
                  </div>
                </div>
              </FloatingCard>
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstration of FloatingCard component in both light and dark modes, showing how patterns, gradients, and colors adapt to different themes.",
      },
    },
  },
};
