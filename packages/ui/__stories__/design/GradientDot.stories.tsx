import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { GradientDot } from "@/ui/design/gradient-dot";

const meta: Meta<typeof GradientDot> = {
  title: "Design/GradientDot",
  component: GradientDot,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A flexible gradient dot component with multiple variants, sizes, positions, and shadow options. " +
          "Extracted from the official-tender design system for reusable accent elements.",
      },
    },
  },
  argTypes: {
    variant: {
      control: "select",
      options: ["teal", "blue", "purple", "pink", "amber", "emerald"],
      description: "Color variant of the gradient dot",
    },
    size: {
      control: "select",
      options: ["xs", "sm", "md", "lg", "xl", "2xl", "3xl"],
      description: "Predefined size of the dot",
    },
    position: {
      control: "select",
      options: [
        "center",
        "top-left",
        "top-right",
        "bottom-left",
        "bottom-right",
        "relative",
        "static",
      ],
      description: "Positioning strategy for the dot",
    },
    shadow: {
      control: "select",
      options: ["none", "sm", "md", "lg", "colored"],
      description: "Shadow style - 'colored' matches the variant color",
    },
    customSize: {
      control: "number",
      description: "Custom size in pixels (overrides size prop)",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    variant: "teal",
    size: "lg",
    position: "center",
    shadow: "colored",
  },
  render: (args) => (
    <div className="relative size-40 rounded-lg border border-gray-200">
      <GradientDot {...args} />
    </div>
  ),
};

export const AllVariants: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-8">
      {(["teal", "blue", "purple", "pink", "amber", "emerald"] as const).map(
        (variant) => (
          <div key={variant} className="flex flex-col items-center gap-2">
            <div className="relative size-20 rounded-lg border border-gray-200">
              <GradientDot variant={variant} size="lg" />
            </div>
            <span className="text-sm font-medium capitalize">{variant}</span>
          </div>
        ),
      )}
    </div>
  ),
};

export const AllSizes: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      {(["xs", "sm", "md", "lg", "xl", "2xl", "3xl"] as const).map((size) => (
        <div key={size} className="flex flex-col items-center gap-2">
          <div className="relative size-24 rounded-lg border border-gray-200">
            <GradientDot size={size} />
          </div>
          <span className="text-sm font-medium">{size}</span>
        </div>
      ))}
    </div>
  ),
};

export const AllPositions: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4">
      {(
        [
          "center",
          "top-left",
          "top-right",
          "bottom-left",
          "bottom-right",
        ] as const
      ).map((position) => (
        <div key={position} className="flex flex-col items-center gap-2">
          <div className="relative size-20 rounded-lg border border-gray-200 bg-gray-50">
            <GradientDot position={position} size="md" />
          </div>
          <span className="text-xs font-medium">{position}</span>
        </div>
      ))}
    </div>
  ),
};

export const CustomSizes: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      {[16, 24, 32, 48, 64, 96].map((size) => (
        <div key={size} className="flex flex-col items-center gap-2">
          <div className="relative size-24 rounded-lg border border-gray-200">
            <GradientDot customSize={size} variant="purple" />
          </div>
          <span className="text-sm font-medium">{size}px</span>
        </div>
      ))}
    </div>
  ),
};

export const ShadowVariations: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-8">
      {(["none", "sm", "md", "lg", "colored"] as const).map((shadow) => (
        <div key={shadow} className="flex flex-col items-center gap-2">
          <div className="relative size-20 rounded-lg border border-gray-200 bg-white">
            <GradientDot shadow={shadow} size="lg" variant="blue" />
          </div>
          <span className="text-sm font-medium">{shadow}</span>
        </div>
      ))}
    </div>
  ),
};

export const WithContent: Story = {
  render: () => (
    <div className="flex gap-8">
      <div className="relative size-32 rounded-lg border border-gray-200">
        <GradientDot customSize={80} variant="teal">
          <div className="flex h-full items-center justify-center text-lg font-bold text-white">
            ✓
          </div>
        </GradientDot>
      </div>
      <div className="relative size-32 rounded-lg border border-gray-200">
        <GradientDot customSize={80} variant="purple">
          <div className="flex h-full items-center justify-center font-bold text-white">
            42
          </div>
        </GradientDot>
      </div>
      <div className="relative size-32 rounded-lg border border-gray-200">
        <GradientDot customSize={80} variant="amber">
          <div className="flex h-full items-center justify-center font-bold text-white">
            ⭐
          </div>
        </GradientDot>
      </div>
    </div>
  ),
};

export const InFloatingCard: Story = {
  render: () => (
    <div className="space-y-8">
      <h3 className="text-lg font-semibold">Integration with FloatingCard</h3>
      <div className="relative h-64 w-48 rounded-3xl bg-gradient-to-b from-gray-100 to-gray-200 p-6 shadow-xl dark:from-gray-800 dark:to-gray-900">
        <GradientDot variant="emerald" size="xl" />
        <div className="relative z-10 flex h-full flex-col justify-end">
          <h4 className="text-lg font-bold text-white">Card Title</h4>
          <p className="text-sm text-white/80">
            Accent dot adds visual interest
          </p>
        </div>
      </div>
    </div>
  ),
};
