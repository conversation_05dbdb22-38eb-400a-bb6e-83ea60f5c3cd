import type { VariantProps } from "class-variance-authority";
import type { PropsWithChildren, ReactNode } from "react";

import Link from "next/link";
import { cva } from "class-variance-authority";

import { cn } from "@/ui/lib";
import { Skeleton } from "@/ui/primitives/skeleton";
import FloatingAvatar from "@/ui/shared/FloatingAvatar";

const i18n = {
  en: {
    generics: {
      avatar: "Person Avatar",
    },
  },
  links: {
    person: "/app/people/[id]",
    provider: "/app/providers/[id]",
  },
};

const previewPersonVariants = cva(
  "flex w-full items-center justify-start gap-2 rounded-lg",
  {
    variants: {
      size: {
        sm: "h-6 rounded-md text-xs",
        md: "h-8 rounded-md",
        lg: "h-10",
        xl: "h-16 text-lg",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const previewPersonDescriptionVariants = cva(
  "overflow-hidden text-ellipsis text-muted-foreground",
  {
    variants: {
      size: {
        sm: "text-xs font-light",
        md: "text-sm font-light",
        lg: "text-sm font-normal",
        xl: "text-base font-normal",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const previewPersonCaptionVariants = cva("overflow-hidden text-ellipsis", {
  variants: {
    size: {
      sm: "text-xs font-normal",
      md: "text-sm font-normal",
      lg: "text-sm font-medium",
      xl: "text-sm font-semibold",
    },
  },
  defaultVariants: {
    size: "md",
  },
});

export interface PersonPartial {
  person?: {
    id: string;
    name: string;
    avatar?: string | null;
  } | null;
}

export interface PreviewPersonProps
  extends VariantProps<typeof previewPersonVariants>,
    PersonPartial,
    PropsWithChildren {
  loading?: boolean;
  link?: boolean;
  shadow?: boolean;
  description?: string;
  caption?: ReactNode;
  className?: string;
}

export default function PreviewPerson({
  loading = false,
  link = false,
  size = "md",
  shadow = true,
  className,
  person,
  description,
  caption,
  children,
}: PreviewPersonProps) {
  return (
    <div className={cn(previewPersonVariants({ size, className }))}>
      <FloatingAvatar
        loading={loading}
        shadow={shadow}
        size={size}
        link={link}
        avatar={person?.avatar}
        label={person?.name ? person.name[0] : undefined}
        className={className}
      />

      {loading ? (
        <div className="flex w-full min-w-32 grow flex-col gap-0.5">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-3 w-[70%]" />
          {caption && <Skeleton className="h-3 w-[85%]" />}
        </div>
      ) : link ? (
        <Link
          className="flex grow flex-col items-start justify-start"
          href={i18n.links.person.replace("[id]", person?.id ?? "")}
        >
          <p className="overflow-hidden text-ellipsis font-semibold">
            {person?.name}
          </p>
          {description && (
            <p className={previewPersonDescriptionVariants({ size })}>
              {description}
            </p>
          )}
          {caption && (
            <div className={previewPersonCaptionVariants({ size })}>
              {caption}
            </div>
          )}
        </Link>
      ) : (
        <div className="flex grow flex-col items-start justify-start text-nowrap">
          <p className="overflow-hidden text-ellipsis font-semibold">
            {person?.name}
          </p>
          {description && (
            <p className={previewPersonDescriptionVariants({ size })}>
              {description}
            </p>
          )}
          {caption && (
            <div className={previewPersonCaptionVariants({ size })}>
              {caption}
            </div>
          )}
        </div>
      )}

      {children ? <div>{children}</div> : null}
    </div>
  );
}
