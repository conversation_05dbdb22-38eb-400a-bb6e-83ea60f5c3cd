"use client";

import type { VariantProps } from "class-variance-authority";

import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { cva } from "class-variance-authority";

import type {
  AspectRatio,
  viewDimensionsVariants,
} from "@/ui/hooks/use-view-dimensions";

import { useViewDimensions } from "@/ui/hooks/use-view-dimensions";
import { cn } from "@/ui/lib";

const cardFaceVariants = cva(
  "absolute inset-0 size-full overflow-visible rounded-xl",
  {
    variants: {
      face: {
        front: "",
        back: "",
      },
    },
  },
);

export interface FlipCardProps
  extends VariantProps<typeof viewDimensionsVariants> {
  className?: string;
  frontContent: React.ReactNode;
  backContent: React.ReactNode;
  aspectRatio?: AspectRatio;
  customWidth?: number;
  customHeight?: number;
  baseSize?: number; // Base size for aspect ratio calculations
  perspective?: number;
  style?: React.CSSProperties;
  isFlipped?: boolean;
  onFlip?: (isFlipped: boolean) => void;
  flipOnClick?: boolean;
  flipOnHover?: boolean;
  // Accessibility props
  "aria-label"?: string;
  "aria-labelledby"?: string;
  "aria-describedby"?: string;
  frontLabel?: string; // Screen reader label for front content
  backLabel?: string; // Screen reader label for back content
  announceFlip?: boolean; // Whether to announce flip state changes
  disabled?: boolean; // Whether the card can be flipped
}

const FlipCard = memo(function FlipCard({
  aspectRatio,
  size,
  className,
  frontContent,
  backContent,
  customWidth,
  customHeight,
  baseSize = 400,
  perspective = 1000,
  style,
  isFlipped: controlledFlipped,
  onFlip,
  flipOnClick = true,
  flipOnHover = false,
  "aria-label": ariaLabel,
  "aria-labelledby": ariaLabelledby,
  "aria-describedby": ariaDescribedby,
  frontLabel = "Front side of card",
  backLabel = "Back side of card",
  announceFlip = true,
  disabled = false,
  ...props
}: FlipCardProps) {
  const [internalFlipped, setInternalFlipped] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const announceRef = useRef<HTMLDivElement>(null);

  // Use unified view dimensions
  const { aspectRatioClass, sizeClasses } = useViewDimensions({
    aspectRatio,
    size: baseSize,
    componentSize: size ?? "md",
    customWidth,
    customHeight,
  });

  // Memoize derived values
  const isControlled = useMemo(
    () => controlledFlipped !== undefined,
    [controlledFlipped],
  );
  const isFlipped = useMemo(
    () => (isControlled ? controlledFlipped : internalFlipped),
    [isControlled, controlledFlipped, internalFlipped],
  );

  const isInteractive = useMemo(
    () => (flipOnClick || flipOnHover) && !disabled,
    [flipOnClick, flipOnHover, disabled],
  );

  // Announce flip state changes for screen readers
  useEffect(() => {
    if (announceFlip && announceRef.current) {
      const currentLabel = isFlipped ? backLabel : frontLabel;
      announceRef.current.textContent = `Showing ${currentLabel}`;
    }
  }, [isFlipped, announceFlip, frontLabel, backLabel]);

  // Memoize container perspective style
  const containerStyle = useMemo(
    () => ({
      perspective: `${perspective}px`,
    }),
    [perspective],
  );

  // Memoize card styles
  const cardStyles = useMemo(
    (): React.CSSProperties => ({
      transform: isFlipped ? "rotateY(180deg)" : "rotateY(0deg)",
      transformStyle: "preserve-3d",
      transition: "transform 0.7s cubic-bezier(0.34, 1.56, 0.64, 1)",
      ...(customWidth && {
        width: `${customWidth}px`,
        minWidth: `${customWidth}px`,
      }),
      ...(customHeight && {
        height: `${customHeight}px`,
        minHeight: `${customHeight}px`,
      }),
      ...style,
    }),
    [isFlipped, customWidth, customHeight, style],
  );

  // Memoize computed className with unified aspect ratio
  const cardClassName = useMemo(
    () =>
      cn(
        // Use unified aspect ratio class when not using custom dimensions
        customWidth || customHeight ? "" : aspectRatioClass,
        // Apply size classes when not using custom dimensions
        customWidth || customHeight ? "" : sizeClasses,
        // Add focus styles for interactive cards
        isInteractive &&
          "focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
        // Add disabled styles
        disabled && "cursor-not-allowed opacity-50",
        className,
      ),
    [
      aspectRatioClass,
      sizeClasses,
      customWidth,
      customHeight,
      isInteractive,
      disabled,
      className,
    ],
  );

  // Memoize face styles to prevent recreation
  const frontFaceStyle = useMemo(
    () => ({
      backfaceVisibility: "hidden" as const,
    }),
    [],
  );

  const backFaceStyle = useMemo(
    () => ({
      backfaceVisibility: "hidden" as const,
      transform: "rotateY(180deg)",
    }),
    [],
  );

  // Memoize face class names
  const frontFaceClassName = useMemo(
    () => cn(cardFaceVariants({ face: "front" })),
    [],
  );
  const backFaceClassName = useMemo(
    () => cn(cardFaceVariants({ face: "back" })),
    [],
  );

  // Core flip logic extracted for reuse
  const performFlip = useCallback(() => {
    if (disabled) return;

    const newFlipped = !isFlipped;
    if (!isControlled) {
      setInternalFlipped(newFlipped);
    }
    onFlip?.(newFlipped);
  }, [disabled, isFlipped, isControlled, onFlip]);

  // Stabilize event handlers with useCallback
  const handleClick = useCallback(() => {
    if (!flipOnClick || disabled) return;
    performFlip();
  }, [flipOnClick, disabled, performFlip]);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (disabled || !flipOnClick) return;

      // Support Enter and Space keys for accessibility
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        performFlip();
      }
    },
    [disabled, flipOnClick, performFlip],
  );

  const handleMouseEnter = useCallback(() => {
    if (flipOnHover && !isFlipped && !disabled) {
      const newFlipped = true;
      if (!isControlled) {
        setInternalFlipped(newFlipped);
      }
      onFlip?.(newFlipped);
    }
  }, [flipOnHover, isFlipped, disabled, isControlled, onFlip]);

  const handleMouseLeave = useCallback(() => {
    if (flipOnHover && isFlipped && !disabled) {
      const newFlipped = false;
      if (!isControlled) {
        setInternalFlipped(newFlipped);
      }
      onFlip?.(newFlipped);
    }
  }, [flipOnHover, isFlipped, disabled, isControlled, onFlip]);

  // Memoize ARIA attributes
  const ariaAttributes = useMemo(
    () => ({
      ...(isInteractive && {
        role: "button",
        tabIndex: 0,
        "aria-expanded": isFlipped,
        "aria-label":
          ariaLabel ||
          `Flip card, currently showing ${isFlipped ? backLabel : frontLabel}`,
      }),
      ...(ariaLabelledby && { "aria-labelledby": ariaLabelledby }),
      ...(ariaDescribedby && { "aria-describedby": ariaDescribedby }),
    }),
    [
      isInteractive,
      isFlipped,
      ariaLabel,
      ariaLabelledby,
      ariaDescribedby,
      frontLabel,
      backLabel,
    ],
  );

  return (
    <>
      {/* Screen reader announcements - positioned outside to avoid layout interference */}
      {announceFlip && (
        <div
          ref={announceRef}
          className="absolute left-[-10000px] top-[-10000px] size-px overflow-hidden"
          aria-live="polite"
          aria-atomic="true"
        />
      )}

      <div style={containerStyle}>
        <div
          ref={cardRef}
          className={cardClassName}
          style={cardStyles}
          onClick={handleClick}
          onKeyDown={handleKeyDown}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          {...ariaAttributes}
          {...props}
        >
          {/* Front Face */}
          <div
            className={frontFaceClassName}
            style={frontFaceStyle}
            aria-hidden={isFlipped}
            aria-label={isFlipped ? undefined : frontLabel}
          >
            {frontContent}
          </div>

          {/* Back Face */}
          <div
            className={backFaceClassName}
            style={backFaceStyle}
            aria-hidden={!isFlipped}
            aria-label={!isFlipped ? undefined : backLabel}
          >
            {backContent}
          </div>
        </div>
      </div>
    </>
  );
});

export default FlipCard;
