"use client";

import React, { useCallback } from "react";

import type { useSearchPagination } from "@axa/ui/search";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@axa/ui/primitives/card";
import { Separator } from "@axa/ui/primitives/separator";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { ErrorAlert } from "./ErrorAlert";

// --- Default Components ---

function DefaultEmptyState() {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="mb-4 rounded-full bg-muted p-3">
        <svg
          className="size-6 text-muted-foreground"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0H4"
          />
        </svg>
      </div>
      <h3 className="mb-2 text-lg font-medium text-foreground">
        No items found
      </h3>
      <p className="max-w-sm text-sm text-muted-foreground">
        Try adjusting your search or filter criteria to find what you're looking
        for.
      </p>
    </div>
  );
}

function DefaultErrorComponent({ error }: { error: Error }) {
  return <ErrorAlert error={error} />;
}

function DefaultLoadingContent() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-24 w-full" />
      <Skeleton className="h-24 w-full" />
      <Skeleton className="h-24 w-full" />
    </div>
  );
}

// --- Paginated Count Component ---
interface PaginatedCountProps {
  count: number;
}

function PaginatedCount({ count }: PaginatedCountProps) {
  const displayValue = count >= 99 ? "99+" : count.toString();

  return (
    <span className="ml-2 inline-flex h-5 items-center justify-center rounded-full bg-muted px-2 text-xs font-medium text-muted-foreground">
      {displayValue}
    </span>
  );
}

// --- Root Component ---

interface PaginatedCardRootProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function PaginatedCardRoot({
  children,
  className,
  ...props
}: PaginatedCardRootProps) {
  return (
    <Card className={cn("flex flex-col gap-4", className)} {...props}>
      {children}
    </Card>
  );
}

// --- Header Component ---

interface PaginatedCardHeaderProps {
  icon?: React.ReactNode;
  title: string;
  count?: number;
  description?: string;
  actions?: React.ReactNode;
  filters?: React.ReactNode;
  loading?: boolean;
}

export function PaginatedCardHeader({
  icon,
  title,
  count,
  description,
  actions,
  filters,
  loading,
}: PaginatedCardHeaderProps) {
  return (
    <>
      <CardHeader className="space-y-4">
        {/* Title and Actions Row */}
        <div className="flex items-center justify-between">
          {/* Left side: Icon, Title, and Description grouped together */}
          <div className="flex items-center gap-2">
            {icon &&
              (loading ? (
                <Skeleton className="size-9 rounded-lg" />
              ) : (
                <div className="flex items-center gap-2 rounded-lg border border-border p-2">
                  {icon}
                </div>
              ))}
            <div>
              <div className="flex items-center">
                {loading ? (
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-7 w-48" />
                    {count !== undefined && (
                      <Skeleton className="h-5 w-10 rounded-full" />
                    )}
                  </div>
                ) : (
                  <>
                    <CardTitle>{title}</CardTitle>
                    {count !== undefined && <PaginatedCount count={count} />}
                  </>
                )}
              </div>
              {loading ? (
                <Skeleton className="mt-1 h-4 w-64" />
              ) : (
                description && (
                  <p className="text-sm text-muted-foreground">{description}</p>
                )
              )}
            </div>
          </div>

          {/* Right side: Actions */}
          {loading
            ? actions && <Skeleton className="h-9 w-32" />
            : actions && <div>{actions}</div>}
        </div>

        {/* Filters Row (full width below title/actions) */}
        {loading
          ? filters && <Skeleton className="h-10 w-full" />
          : filters && <div>{filters}</div>}
      </CardHeader>
      <Separator />
    </>
  );
}

// --- Content Component ---

interface PaginatedCardContentProps {
  isLoading?: boolean;
  error?: Error | null;
  isEmpty: boolean;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  children: React.ReactNode;
}

export function PaginatedCardContent({
  isLoading,
  error,
  isEmpty,
  loadingComponent = <DefaultLoadingContent />,
  errorComponent,
  emptyComponent = <DefaultEmptyState />,
  children,
}: PaginatedCardContentProps) {
  return (
    <CardContent className="flex flex-1 flex-col gap-4">
      {isLoading ? (
        loadingComponent
      ) : (
        <>
          {error && (errorComponent ?? <DefaultErrorComponent error={error} />)}
          {isEmpty ? emptyComponent : children}
        </>
      )}
    </CardContent>
  );
}

// --- Footer Component (Pagination) ---

interface PaginatedCardFooterProps {
  pagination: ReturnType<typeof useSearchPagination>["pagination"];
  setPagination: ReturnType<typeof useSearchPagination>["setPagination"];
  totalItems: number;
  itemNoun?: { singular: string; plural: string };
  showEntryCount?: boolean;
  loading?: boolean;
}

const defaultItemNoun = { singular: "entry", plural: "entries" };

export function PaginatedCardFooter({
  pagination,
  setPagination,
  totalItems,
  itemNoun = defaultItemNoun,
  showEntryCount = true,
  loading,
}: PaginatedCardFooterProps) {
  const { pageIndex: page, pageSize } = pagination;
  const totalPages = Math.ceil(totalItems / pageSize);

  const handlePageChange = useCallback(
    (newPage: number) => {
      setPagination((prev: typeof pagination) => ({
        ...prev,
        pageIndex: newPage,
      }));
    },
    [setPagination],
  );

  const startItem = totalItems === 0 ? 0 : page * pageSize + 1;
  const endItem = Math.min((page + 1) * pageSize, totalItems);
  const noun = totalItems === 1 ? itemNoun.singular : itemNoun.plural;

  // Always show footer to prevent layout shifts and maintain filter/navigation access
  return (
    <CardFooter>
      <div className="flex w-full flex-col items-center justify-between gap-4 pt-4 sm:flex-row">
        {loading ? (
          <Skeleton className="h-5 w-40" />
        ) : (
          <div className="text-sm text-muted-foreground">
            {showEntryCount ? (
              totalItems > 0 ? (
                `Showing ${startItem}–${endItem} of ${totalItems} ${noun}`
              ) : (
                `0 ${noun}`
              )
            ) : (
              // Always maintain space for consistent layout
              <span className="opacity-0">0 {noun}</span>
            )}
          </div>
        )}

        {loading ? (
          <div className="flex items-center gap-2">
            <Skeleton className="h-9 w-[70px]" />
            <Skeleton className="h-9 w-[70px]" />
          </div>
        ) : (
          <div className="flex items-center gap-2">
            {/* Always show pagination controls for consistent UI */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.max(0, page - 1))}
              disabled={page === 0}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                handlePageChange(Math.min(totalPages - 1, page + 1))
              }
              disabled={page >= totalPages - 1 || totalPages <= 1}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </CardFooter>
  );
}
