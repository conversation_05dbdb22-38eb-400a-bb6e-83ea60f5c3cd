"use client";

import type { VariantProps } from "class-variance-authority";

import { memo, useMemo } from "react";

import type {
  AspectRatio,
  viewDimensionsVariants,
} from "@/ui/hooks/use-view-dimensions";

import { OfficialTenderBackground } from "@/ui/design/official-tender";
import { useFloatingHover } from "@/ui/hooks/use-floating-hover";
import { useViewDimensions } from "@/ui/hooks/use-view-dimensions";
import { cn } from "@/ui/lib";

export interface FloatingCardProps
  extends VariantProps<typeof viewDimensionsVariants> {
  className?: string;
  children?: React.ReactNode;
  aspectRatio?: AspectRatio;
  customWidth?: number;
  customHeight?: number;
  baseSize?: number; // Base size for aspect ratio calculations
  maxRotation?: number;
  perspective?: number;
  style?: React.CSSProperties;
  background?: React.ReactNode;
}

const FloatingCard = memo(function FloatingCard({
  aspectRatio = "portrait",
  size,
  className,
  children,
  customWidth,
  customHeight,
  baseSize = 400,
  maxRotation = 3,
  perspective = 1000,
  style,
  background,
  ...props
}: FloatingCardProps) {
  // Use unified view dimensions
  const { aspectRatioClass, sizeClasses } = useViewDimensions({
    aspectRatio,
    size: baseSize,
    componentSize: size || "md",
    customWidth,
    customHeight,
  });

  // Memoize default background to prevent recreation
  const defaultBackground = useMemo(
    () => <OfficialTenderBackground className="absolute inset-0" />,
    [],
  );
  const finalBackground = background ?? defaultBackground;

  const { elementRef, isHovering, handlers, getTransform } = useFloatingHover({
    maxRotation,
    perspective,
  });

  // Memoize container styles
  const containerStyles = useMemo(
    (): React.CSSProperties => ({
      ...(customWidth && { width: `${customWidth}px` }),
      ...(customHeight && { height: `${customHeight}px` }),
      perspective: `${perspective}px`,
    }),
    [customWidth, customHeight, perspective],
  );

  // Simplified and optimized card styles
  const cardStyles = useMemo(
    (): React.CSSProperties => ({
      transform: getTransform(),
      transformStyle: "preserve-3d",
      // Smooth, optimized transitions
      transition: isHovering
        ? "transform 0.15s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.2s cubic-bezier(0.23, 1, 0.32, 1)"
        : "transform 0.4s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.3s cubic-bezier(0.23, 1, 0.32, 1)",
      // CSS-based shadow animation using custom properties
      boxShadow: isHovering
        ? "0 25px 35px -5px rgba(0, 0, 0, 0.15), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
        : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      ...style,
    }),
    [getTransform, isHovering, style],
  );

  // Memoize computed className with unified aspect ratio
  const cardClassName = useMemo(
    () =>
      cn(
        "absolute inset-0 cursor-pointer overflow-hidden rounded-3xl bg-gradient-to-b from-gray-100 to-gray-200 shadow-2xl transition-all duration-200 ease-out dark:from-gray-800 dark:to-gray-900",
        "relative transition-all duration-700 ease-out",
        // Use unified aspect ratio class when not using custom dimensions
        customWidth || customHeight ? "" : aspectRatioClass,
        // Apply size classes when not using custom dimensions
        customWidth || customHeight ? "" : sizeClasses,
        className,
        {
          "rounded-md p-2": size === "sm",
          "rounded-lg p-4": size === "md",
          "rounded-lg p-6": size === "lg",
          "rounded-lg p-8": size === "xl",
        },
      ),
    [aspectRatioClass, sizeClasses, customWidth, customHeight, className, size],
  );

  return (
    <div
      ref={elementRef}
      className="relative"
      style={containerStyles}
      onMouseMove={handlers.onMouseMove}
      onMouseEnter={handlers.onMouseEnter}
      onMouseLeave={handlers.onMouseLeave}
      {...props}
    >
      {/* Internal transforming card - has the visual styling */}
      <div className={cardClassName} style={cardStyles}>
        {/* Background layer - fills entire container */}
        {finalBackground}

        {/* Content layer */}
        <div className="relative z-10 size-full">{children}</div>
      </div>
    </div>
  );
});

export default FloatingCard;

export { FloatingCard };
