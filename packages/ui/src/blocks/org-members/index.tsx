import type { GenericMemberWithOriginal } from "./types";

import { MembersList } from "./MembersList";

// Export types
export type {
  GenericMember,
  GenericMemberWithOriginal,
  OrganizationMembersListProps,
  MemberCardProps,
} from "./types";

// Export individual components
export { MemberCard } from "./MemberCard";
export { EmptyMembersState } from "./EmptyState";
export { MembersList } from "./MembersList";

// Re-export the main component as OrganizationMembersList for backwards compatibility
export { MembersList as OrganizationMembersList } from "./MembersList";

// Utility function to create adapters
export function createMemberAdapter<T>(
  item: T,
  config: {
    getId: (item: T) => string;
    getDisplayName: (item: T) => string;
    getAvatar?: (item: T) => string | null | undefined;
    getInitials?: (item: T) => string;
    getRole: (item: T) => string;
    getStatus?: (item: T) => string | undefined;
    getMetadata?: (item: T) => Record<string, unknown>;
  },
): GenericMemberWithOriginal<T> {
  return {
    id: config.getId(item),
    displayName: config.getDisplayName(item),
    avatar: config.getAvatar?.(item) || null,
    initials:
      config.getInitials?.(item) ||
      config.getDisplayName(item).slice(0, 2).toUpperCase(),
    role: config.getRole(item),
    status: config.getStatus?.(item),
    metadata: config.getMetadata?.(item),
    originalData: item,
  };
}

// Export default as the main component
export default MembersList;
