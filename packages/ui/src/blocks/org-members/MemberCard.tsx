"use client";

import React from "react";

import type { GenericMember, MemberCardProps } from "./types";

import { cn } from "../../lib";
import { Avatar, AvatarFallback, AvatarImage } from "../../primitives/avatar";
import { Badge } from "../../primitives/badge";

export function MemberCard<T extends GenericMember = GenericMember>({
  member,
  renderMenu,
  onClick,
  className = "",
}: MemberCardProps<T>) {
  return (
    <div
      className={cn(
        "group relative flex flex-row items-center justify-between gap-4 rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md",
        onClick && "cursor-pointer",
        className,
      )}
      onClick={() => onClick?.(member)}
    >
      <div className="flex items-center gap-3">
        <Avatar className="size-12">
          <AvatarImage
            src={member.avatar || undefined}
            alt={member.displayName}
          />
          <AvatarFallback>
            {member.initials || member.displayName.slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          <h3 className="font-semibold">{member.displayName}</h3>
          <div className="mt-1">
            <Badge
              variant={member.role.includes("admin") ? "default" : "secondary"}
            >
              {member.role}
            </Badge>
            {member.status && (
              <Badge variant="outline" className="ml-2">
                {member.status}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {renderMenu && (
        <div className="opacity-0 transition-opacity group-hover:opacity-100">
          {renderMenu(member)}
        </div>
      )}
    </div>
  );
}
