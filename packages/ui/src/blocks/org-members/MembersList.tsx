"use client";

import React from "react";
import { Users2 } from "lucide-react";

import { cn } from "@/ui/lib";
import {
  PaginatedCardContent,
  PaginatedCardFooter,
  PaginatedCardHeader,
  PaginatedCardRoot,
} from "@/ui/shared/PaginatedCard";

import type { GenericMember, OrganizationMembersListProps } from "./types";

import { EmptyMembersState } from "./EmptyState";
import { MemberCard } from "./MemberCard";

export function MembersList<T extends GenericMember = GenericMember>({
  members,
  total,
  loading = false,
  error,
  title = "Team Members",
  description = "Manage organization members and their roles",
  emptyMessage = "No team members yet",
  itemNoun = { singular: "member", plural: "members" },
  renderMember,
  renderActions,
  renderMemberMenu,
  pagination,
  onPaginationChange,
  onMemberClick,
  className = "",
  gridCols = 2,
}: OrganizationMembersListProps<T>) {
  const defaultRenderMember = (member: T) => (
    <MemberCard
      key={member.id}
      member={member}
      renderMenu={renderMemberMenu}
      onClick={onMemberClick}
    />
  );

  return (
    <PaginatedCardRoot className={className}>
      <PaginatedCardHeader
        icon={<Users2 className="size-5 text-muted-foreground" />}
        title={title}
        count={total}
        description={description}
        loading={loading}
        actions={renderActions?.()}
      />

      <PaginatedCardContent
        isLoading={loading}
        error={error}
        isEmpty={members.length === 0}
        emptyComponent={<EmptyMembersState message={emptyMessage} />}
      >
        <div
          className={cn(
            "grid grid-cols-1 gap-4",
            gridCols === 2 && "sm:grid-cols-2",
            gridCols === 3 && "sm:grid-cols-3",
            gridCols === 4 && "sm:grid-cols-4",
          )}
        >
          {members.map(renderMember || defaultRenderMember)}
        </div>
      </PaginatedCardContent>

      {pagination && onPaginationChange && (
        <PaginatedCardFooter
          pagination={pagination}
          setPagination={(updaterOrValue) => {
            if (typeof updaterOrValue === "function") {
              onPaginationChange(updaterOrValue(pagination));
            } else {
              onPaginationChange(updaterOrValue);
            }
          }}
          totalItems={total}
          itemNoun={itemNoun}
          loading={loading}
        />
      )}
    </PaginatedCardRoot>
  );
}
