import { cn } from "@/ui/lib";
import { Skeleton } from "@/ui/primitives/skeleton";

import type { DocumentType } from "../security-id/types";
import type { ProviderData, ProviderSecurityData } from "./types";

import { SecurityIdFlipCard } from "../security-id";
import { ProviderLicense } from "./provider-license";

export interface ProviderSecurityIdProps {
  provider: ProviderData;
  securityData: ProviderSecurityData;
  authorized?: boolean;
  className?: string;
  loading?: boolean;
}

export function ProviderSecurityId({
  provider,
  securityData,
  authorized = false,
  className,
  loading = false,
}: ProviderSecurityIdProps) {
  // Create custom front content using the ProviderLicense component
  const frontContent = <ProviderLicense securityData={securityData} />;

  return (
    <div className={cn("w-full max-w-sm", className)}>
      {loading ? (
        <Skeleton className="h-32 w-full rounded-lg" />
      ) : (
        <SecurityIdFlipCard
          size="sm"
          type={"government-id" as DocumentType}
          provider={provider.name}
          authorized={authorized}
          frontContent={frontContent}
          securityData={securityData}
          className="w-full"
        />
      )}
    </div>
  );
}
