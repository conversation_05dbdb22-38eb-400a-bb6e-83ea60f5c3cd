import { Skeleton } from "@/ui/primitives/skeleton";

import type { ProviderSecurityData } from "./types";

export interface ProviderLicenseProps {
  securityData: ProviderSecurityData;
  className?: string;
  loading?: boolean;
}

export function ProviderLicense({
  securityData,
  className,
  loading = false,
}: ProviderLicenseProps) {
  return (
    <section
      className={`flex h-full flex-col justify-between ${className || ""}`}
    >
      {loading ? (
        <>
          {/* Header with verification status */}
          <header className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Skeleton className="size-3 rounded-full" />
              <Skeleton className="h-4 w-16" />
            </div>
          </header>

          {/* License information */}
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <Skeleton className="h-3 w-12" />
              <Skeleton className="h-3 w-20" />
            </div>
            <div className="flex justify-between text-xs">
              <Skeleton className="h-3 w-12" />
              <Skeleton className="h-3 w-16" />
            </div>
            <div className="flex justify-between text-xs">
              <Skeleton className="h-3 w-8" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
        </>
      ) : (
        <>
          {/* Header with verification status */}
          <header className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="size-3 rounded-full bg-green-500 dark:bg-green-400" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">
                {securityData.verificationStatus === "verified"
                  ? "Verified"
                  : "Pending"}
              </span>
            </div>
          </header>

          {/* License information */}
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">License</span>
              <span className="font-mono">{securityData.documentId}</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">Expires</span>
              <span className="font-mono">{securityData.expirationDate}</span>
            </div>
            {securityData.npiNumber && (
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">NPI</span>
                <span className="font-mono">{securityData.npiNumber}</span>
              </div>
            )}
          </div>
        </>
      )}
    </section>
  );
}
