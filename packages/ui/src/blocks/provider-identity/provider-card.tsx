import type { PropsWithChildren } from "react";

import { cn } from "@/ui/lib";
import { Card, CardContent, CardHeader } from "@/ui/primitives/card";
import { Separator } from "@/ui/primitives/separator";
import { Skeleton } from "@/ui/primitives/skeleton";

import type { ProviderData, ProviderSecurityData } from "./types";

import { ProviderProfile } from "./provider-profile";
import { ProviderSecurityId } from "./provider-security-id";

export interface ProviderCardProps {
  provider: ProviderData;
  securityData?: ProviderSecurityData;
  onSecurityLoad?: () => Promise<ProviderSecurityData>;
  authorized?: boolean;
  className?: string;
  loading?: boolean;
}

export function ProviderCard({
  provider,
  securityData,
  authorized = false,
  className,
  loading = false,
  children,
}: PropsWithChildren<ProviderCardProps>) {
  return (
    <Card
      className={cn(
        "mx-auto w-full max-w-md shadow-lg transition-shadow duration-300 hover:shadow-xl",
        className,
      )}
    >
      <CardHeader className="pb-4">
        {loading ? (
          <div className="flex items-center gap-3">
            <Skeleton className="size-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>
        ) : (
          <ProviderProfile provider={provider} loading={loading}>
            {children}
          </ProviderProfile>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        <Separator />

        {securityData && (
          <ProviderSecurityId
            provider={provider}
            securityData={securityData}
            authorized={authorized}
            loading={loading}
          />
        )}
      </CardContent>
    </Card>
  );
}

export default ProviderCard;
