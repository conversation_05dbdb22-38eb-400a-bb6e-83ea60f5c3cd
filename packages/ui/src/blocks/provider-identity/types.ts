import type {
  GovernmentIdSecurityData,
  SecurityData,
} from "../security-id/types";

export enum ProviderStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  PENDING = "pending",
}

export interface ProviderData {
  id: string;
  name: string;
  specialty: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  licenseExpiry: string;
  profileImage?: string;
  status: ProviderStatus;
}

// Provider-specific security data that extends government ID data
export interface ProviderSecurityData extends GovernmentIdSecurityData {
  npiNumber?: string;
  deaNumber?: string;
  licenseType?: string;
  specialty?: string;
}
