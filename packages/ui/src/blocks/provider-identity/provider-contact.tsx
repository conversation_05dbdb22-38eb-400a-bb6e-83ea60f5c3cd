import { Calendar, Mail, MapPin, Phone } from "lucide-react";

import { cn } from "@/ui/lib";
import { Skeleton } from "@/ui/primitives/skeleton";

import type { ProviderData } from "./types";

export interface ProviderContactProps {
  provider: ProviderData;
  className?: string;
  loading?: boolean;
}

interface ContactItemProps {
  icon: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  className?: string;
}

function ContactItem({ icon: Icon, children, className }: ContactItemProps) {
  return (
    <div className={cn("flex items-center gap-3 text-sm", className)}>
      <Icon className="size-4 shrink-0 text-gray-400" />
      <span className="text-gray-600">{children}</span>
    </div>
  );
}

export function ProviderContact({
  provider,
  className,
  loading = false,
}: ProviderContactProps) {
  return (
    <div className={cn("space-y-3", className)}>
      {loading ? (
        <>
          <div className="flex items-center gap-3">
            <Skeleton className="size-4 rounded" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex items-center gap-3">
            <Skeleton className="size-4 rounded" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex items-center gap-3">
            <Skeleton className="size-4 rounded" />
            <Skeleton className="h-4 w-28" />
          </div>
          <div className="flex items-center gap-3">
            <Skeleton className="size-4 rounded" />
            <Skeleton className="h-4 w-36" />
          </div>
        </>
      ) : (
        <>
          <ContactItem icon={Mail}>
            <span className="truncate">{provider.email}</span>
          </ContactItem>

          <ContactItem icon={Phone}>{provider.phone}</ContactItem>

          <ContactItem icon={MapPin}>{provider.location}</ContactItem>

          <ContactItem icon={Calendar}>
            License expires: {provider.licenseExpiry}
          </ContactItem>
        </>
      )}
    </div>
  );
}
