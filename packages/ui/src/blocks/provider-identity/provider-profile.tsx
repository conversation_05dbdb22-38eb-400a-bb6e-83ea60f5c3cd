import type { PropsWithChildren } from "react";

import PreviewPerson from "@/ui/common/PreviewPerson";
import { cn } from "@/ui/lib";

import type { ProviderData } from "./types";

export interface ProviderProfileProps {
  provider: ProviderData;
  className?: string;
  loading?: boolean;
}

export function ProviderProfile({
  provider,
  className,
  loading = false,
  children,
}: PropsWithChildren<ProviderProfileProps>) {
  // Transform provider data to work with PreviewPerson
  const personData = {
    id: provider.id,
    name: provider.name,
    avatar: provider.profileImage,
  };

  return (
    <div
      className={cn(
        "flex flex-col items-start justify-between gap-4",
        className,
      )}
    >
      <div className="min-w-0 flex-1">
        <PreviewPerson
          loading={loading}
          person={personData}
          description={provider.title}
          caption={
            <span className="font-medium text-accent-foreground">
              {provider.specialty}
            </span>
          }
          size="xl"
        />
      </div>

      {children}
    </div>
  );
}
