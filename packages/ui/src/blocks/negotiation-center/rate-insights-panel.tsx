import { Minus, TrendingDown, TrendingUp } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";

import { cn } from "../../lib";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "../../primitives/chart";
import { Skeleton } from "../../primitives/skeleton";

// Structured data interfaces
interface RateHistoryData {
  date: string;
  rate: number;
  marketAverage: number;
}

interface DemandMetrics {
  level: "low" | "medium" | "high";
  score: number; // 0-100
  trend: "up" | "down" | "stable";
}

interface MarketComparison {
  percentile: number;
  difference: number;
  trend: "above" | "below" | "at";
}

interface ActionItem {
  id: string;
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  impact: string;
}

interface RateInsightsPanelProps {
  currentRate: number;
  currency?: string;
  unit?: string;
  rateHistory: RateHistoryData[];
  marketComparison: MarketComparison;
  demandMetrics: DemandMetrics;
  actionItems: ActionItem[];
  loading?: boolean;
}

export function RateInsightsPanel({
  currentRate,
  currency = "$",
  unit = "per hour",
  rateHistory,
  marketComparison,
  demandMetrics,
  actionItems,
  loading = false,
}: RateInsightsPanelProps) {
  const getDemandPosition = () => {
    return `${demandMetrics.score}%`;
  };

  const getDemandColor = () => {
    switch (demandMetrics.level) {
      case "low":
        return "bg-red-500";
      case "medium":
        return "bg-yellow-500";
      case "high":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const getComparisonIcon = () => {
    switch (marketComparison.trend) {
      case "above":
        return <TrendingUp className="size-4 text-green-600" />;
      case "below":
        return <TrendingDown className="size-4 text-red-600" />;
      case "at":
        return <Minus className="size-4 text-gray-600" />;
      default:
        return <Minus className="size-4 text-gray-600" />;
    }
  };

  const getComparisonText = () => {
    const absValue = Math.abs(marketComparison.difference);
    const symbol =
      marketComparison.trend === "above"
        ? "+"
        : marketComparison.trend === "below"
          ? "-"
          : "";
    return `${symbol}${currency}${absValue} vs market (${marketComparison.percentile}th percentile)`;
  };

  const chartConfig = {
    rate: {
      label: "Your Rate",
      color: "hsl(var(--chart-1))",
    },
    marketAverage: {
      label: "Market Average",
      color: "hsl(var(--chart-2))",
    },
  };

  const hasRateHistory = rateHistory && rateHistory.length > 0;
  const hasActionItems = actionItems && actionItems.length > 0;

  return (
    <div className="h-fit rounded-lg border border-border bg-card p-4 shadow-sm lg:p-6">
      <div className="mb-4 flex items-center justify-between lg:mb-6">
        {loading ? (
          <Skeleton className="h-6 w-24" />
        ) : (
          <h2 className="text-base font-semibold text-foreground lg:text-lg">
            Rate insights
          </h2>
        )}
        {loading ? (
          <Skeleton className="size-6 rounded" />
        ) : (
          <button className="text-muted-foreground hover:text-foreground">
            <span className="text-lg">⋯</span>
          </button>
        )}
      </div>

      {/* Rate Chart */}
      <div className="mb-4 lg:mb-6">
        {loading ? (
          <div className="mb-4 rounded-lg bg-teal-50 p-3 lg:p-4">
            <div className="flex h-16 items-end gap-1 lg:h-20 lg:gap-2">
              <Skeleton className="h-8 w-6 lg:h-12 lg:w-8" />
              <Skeleton className="h-12 w-6 lg:h-16 lg:w-8" />
              <Skeleton className="h-16 w-6 lg:h-20 lg:w-8" />
              <Skeleton className="h-10 w-6 lg:h-14 lg:w-8" />
            </div>
          </div>
        ) : hasRateHistory ? (
          <div className="mb-4 rounded-lg bg-teal-50 p-3 lg:p-4">
            <ChartContainer config={chartConfig} className="h-[120px]">
              <AreaChart
                accessibilityLayer
                data={rateHistory}
                margin={{
                  left: 8,
                  right: 8,
                  top: 8,
                  bottom: 8,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString("en-US", { month: "short" });
                  }}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => `${currency}${value}`}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator="line" />}
                />
                <Area
                  dataKey="marketAverage"
                  type="natural"
                  fill="var(--color-marketAverage)"
                  fillOpacity={0.2}
                  stroke="var(--color-marketAverage)"
                  strokeWidth={2}
                />
                <Area
                  dataKey="rate"
                  type="natural"
                  fill="var(--color-rate)"
                  fillOpacity={0.4}
                  stroke="var(--color-rate)"
                  strokeWidth={2}
                />
              </AreaChart>
            </ChartContainer>
          </div>
        ) : (
          <div className="mb-4 rounded-lg bg-muted/50 p-3 lg:p-4">
            <div className="flex h-[120px] items-center justify-center">
              <div className="text-center">
                <div className="text-sm text-muted-foreground">
                  No rate history data available
                </div>
                <div className="mt-1 text-xs text-muted-foreground">
                  Rate trends will appear here as data becomes available
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="text-center">
          {loading ? (
            <>
              <Skeleton className="mx-auto h-8 w-32 lg:h-10 lg:w-40" />
              <Skeleton className="mx-auto mt-1 h-4 w-24" />
            </>
          ) : (
            <>
              <div className="text-xl font-bold text-foreground lg:text-2xl">
                {currency}
                {currentRate}{" "}
                <span className="text-sm font-normal text-muted-foreground">
                  {unit}
                </span>
              </div>
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                {getComparisonIcon()}
                <span>{getComparisonText()}</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Demand Gauge */}
      <div className="mb-4 lg:mb-6">
        {loading ? (
          <Skeleton className="mb-3 h-5 w-16" />
        ) : (
          <div className="mb-3 flex items-center gap-2">
            <h3 className="font-semibold text-foreground">Demand</h3>
            <span
              className={cn(
                "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
                demandMetrics.level === "high"
                  ? "bg-green-100 text-green-800"
                  : demandMetrics.level === "medium"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800",
              )}
            >
              {demandMetrics.level}
            </span>
          </div>
        )}
        <div className="relative">
          {loading ? (
            <>
              <Skeleton className="h-2 w-full rounded-full" />
              <div className="mt-1 flex justify-between">
                <Skeleton className="h-3 w-6" />
                <Skeleton className="h-3 w-8" />
              </div>
            </>
          ) : (
            <>
              <div className="h-2 w-full rounded-full bg-muted">
                <div
                  className={cn("h-2 rounded-full", getDemandColor())}
                  style={{
                    width: `${Math.min(Math.max(demandMetrics.score, 0), 100)}%`,
                  }}
                ></div>
              </div>
              <div
                className={cn(
                  "absolute top-0 size-3 -translate-x-1.5 -translate-y-0.5 rounded-full",
                  getDemandColor(),
                )}
                style={{
                  left: `${Math.min(Math.max(demandMetrics.score, 0), 100)}%`,
                }}
              ></div>
              <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                <span>Low</span>
                <span>High</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Action Items */}
      <div>
        {loading ? (
          <Skeleton className="mb-3 h-5 w-12" />
        ) : (
          <h3 className="mb-3 font-semibold text-teal-600">
            Recommended Actions
          </h3>
        )}
        <div className="space-y-3">
          {loading ? (
            <>
              <Skeleton className="h-12 w-full rounded" />
              <Skeleton className="h-12 w-full rounded" />
            </>
          ) : hasActionItems ? (
            actionItems.map((item) => (
              <div
                key={item.id}
                className="rounded-lg border border-border bg-background p-3 transition-colors hover:bg-accent/50"
              >
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-foreground">
                      {item.title}
                    </h4>
                    <p className="mt-1 text-xs text-muted-foreground">
                      {item.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className={cn(
                        "inline-flex items-center rounded px-2 py-1 text-xs font-medium",
                        item.priority === "high"
                          ? "bg-red-100 text-red-800"
                          : item.priority === "medium"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-green-100 text-green-800",
                      )}
                    >
                      {item.priority}
                    </span>
                  </div>
                </div>
                <div className="mt-2 text-xs font-medium text-teal-600">
                  Impact: {item.impact}
                </div>
              </div>
            ))
          ) : (
            <div className="rounded-lg border border-dashed border-muted bg-muted/20 p-4 text-center">
              <div className="text-sm text-muted-foreground">
                No action items available
              </div>
              <div className="mt-1 text-xs text-muted-foreground">
                Recommendations will appear here based on market analysis
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
