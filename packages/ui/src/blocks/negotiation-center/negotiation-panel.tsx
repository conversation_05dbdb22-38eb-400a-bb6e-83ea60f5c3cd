"use client";

import { MessageSquare } from "lucide-react";

import type { Milestone, Registry } from "../negotiation-timeline";

import { NegotiationTimeline } from "../negotiation-timeline";

interface NegotiationPanelProps {
  milestones: Milestone[];
  registry: Registry;
  defaultExpanded?: boolean;
  showMessageInput?: boolean;
  onSendMessage?: (message: string) => void;
  loading?: boolean;
}

export function NegotiationPanel({
  milestones,
  registry,
  defaultExpanded = false,
  showMessageInput = true,
  onSendMessage,
  loading = false,
}: NegotiationPanelProps) {
  const header = (
    <div className="flex items-center gap-2 border-b bg-background/50 px-4 py-3">
      <MessageSquare className="size-5 text-teal-600" />
      <h2 className="text-lg font-semibold text-foreground">Negotiations</h2>
    </div>
  );

  return (
    <NegotiationTimeline
      className="h-full border border-border"
      milestones={milestones}
      registry={registry}
      defaultExpanded={defaultExpanded}
      showMessageInput={showMessageInput}
      onSendMessage={onSendMessage}
      loading={loading}
      header={header}
    />
  );
}
