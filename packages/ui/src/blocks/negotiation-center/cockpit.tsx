"use client";

import type { Miles<PERSON>, Registry } from "../negotiation-timeline";
import type { ProviderSecurityData } from "../provider-identity/types";

import { JobCard } from "./job-card";
import { NegotiationPanel } from "./negotiation-panel";
import { ProviderCard } from "./provider-card";
import { RateInsightsPanel } from "./rate-insights-panel";

// Re-export types from RateInsightsPanel for convenience
interface RateHistoryData {
  date: string;
  rate: number;
  marketAverage: number;
}

interface DemandMetrics {
  level: "low" | "medium" | "high";
  score: number; // 0-100
  trend: "up" | "down" | "stable";
}

interface MarketComparison {
  percentile: number;
  difference: number;
  trend: "above" | "below" | "at";
}

interface ActionItem {
  id: string;
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  impact: string;
}

interface Organization {
  id: string;
  name: string;
  avatar?: string | null;
  description?: string | null;
}

interface Location {
  id: string;
  name: string;
  address: {
    formatted: string;
  };
  description?: string | null;
}

interface CockpitLayoutProps {
  providerProps: {
    name: string;
    avatar: string;
    specialty: string;
    experience: string;
    securityData?: ProviderSecurityData;
    authorized?: boolean;
  };
  jobProps: {
    specialty: string;
    location?: Location;
    schedule: string;
    jobType?: string;
    employmentType?: string;
    salary?: string;
    highlights?: string[];
    organization?: Organization;
  };
  rateInsightsProps: {
    currentRate: number;
    currency?: string;
    unit?: string;
    rateHistory?: RateHistoryData[];
    marketComparison?: MarketComparison;
    demandMetrics?: DemandMetrics;
    actionItems?: ActionItem[];
  };
  negotiationProps: {
    milestones: Milestone[];
    registry: Registry;
    defaultExpanded?: boolean;
    showMessageInput?: boolean;
    onSendMessage?: (message: string) => void;
  };
  // Optional event handlers
  onProviderViewProfile?: () => void;
  onJobViewDetails?: () => void;
  // Loading state
  loading?: boolean;
}

export function CockpitLayout({
  providerProps,
  jobProps,
  rateInsightsProps,
  negotiationProps,
  onProviderViewProfile,
  onJobViewDetails,
  loading = false,
}: CockpitLayoutProps) {
  // Provide default values for rate insights when data is missing
  const rateInsightsWithDefaults = {
    currentRate: rateInsightsProps.currentRate,
    currency: rateInsightsProps.currency || "$",
    unit: rateInsightsProps.unit || "per hour",
    rateHistory: rateInsightsProps.rateHistory || [],
    marketComparison: rateInsightsProps.marketComparison || {
      percentile: 50,
      difference: 0,
      trend: "at" as const,
    },
    demandMetrics: rateInsightsProps.demandMetrics || {
      level: "medium" as const,
      score: 50,
      trend: "stable" as const,
    },
    actionItems: rateInsightsProps.actionItems || [],
    loading,
  };

  return (
    <div className="flex h-fit flex-col p-4 lg:p-6">
      <div className="mx-auto flex h-full max-w-7xl flex-1">
        {/* 
          Responsive CSS Grid Layout:
          - Mobile: Single column, components stacked
          - Tablet: 2 columns for cards on top, then timeline + insights below  
          - Desktop: 3-column layout with left sidebar (cards), center timeline, right insights
        */}
        <div className="grid size-full grid-cols-1 gap-4 md:grid-cols-2 md:grid-rows-[auto_auto_1fr] lg:grid-cols-12 lg:grid-rows-[auto_1fr]">
          {/* Left Sidebar - Provider and Job Cards */}
          <div className="grid h-full grid-cols-1 gap-2 space-y-4 md:col-span-2 md:row-span-1 md:grid-cols-2 lg:col-span-3 lg:row-span-2 lg:grid-cols-1">
            {/* Provider Card */}
            <ProviderCard
              {...providerProps}
              onViewProfile={onProviderViewProfile}
              loading={loading}
            />

            {/* Job Card */}
            <JobCard
              {...jobProps}
              onViewDetails={onJobViewDetails}
              loading={loading}
            />
          </div>

          {/* Center - Negotiation Panel */}
          <div className="h-full min-h-0 md:col-span-1 md:row-span-1 lg:col-span-6 lg:row-span-2">
            <div className="h-full">
              <NegotiationPanel {...negotiationProps} loading={loading} />
            </div>
          </div>

          {/* Right - Rate Insights Panel */}
          <div className="md:col-span-1 md:row-span-1 lg:col-span-3 lg:row-span-2">
            <div className="h-full overflow-y-auto">
              <RateInsightsPanel {...rateInsightsWithDefaults} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
