import type { ReactNode } from "react";

import type {
  ProviderData,
  ProviderSecurityData,
} from "../provider-identity/types";

import { Button } from "../../primitives/button";
import { ProviderCard as ProviderIdentityCard } from "../provider-identity/provider-card";
import { ProviderStatus } from "../provider-identity/types";

interface ProviderCardProps {
  name: string;
  avatar?: string;
  specialty: string;
  experience: string;
  securityData?: ProviderSecurityData;
  authorized?: boolean;
  onViewProfile?: () => void;
  loading?: boolean;
  children?: ReactNode;
}

export function ProviderCard({
  name,
  avatar,
  specialty,
  experience,
  securityData,
  authorized = false,
  onViewProfile,
  loading = false,
  children,
}: ProviderCardProps) {
  // Transform props to provider data format
  const providerData: ProviderData = {
    id: name, // Using name as id since we don't have a separate id
    name,
    specialty,
    title: experience,
    email: "", // Not available in current props
    phone: "", // Not available in current props
    location: "", // Not available in current props
    licenseExpiry: "", // Not available in current props
    profileImage: avatar,
    status: ProviderStatus.ACTIVE,
  };

  return (
    <div className="space-y-4">
      <ProviderIdentityCard
        provider={providerData}
        securityData={securityData}
        authorized={authorized}
        loading={loading}
      >
        {children ??
          (onViewProfile && (
            <Button
              variant="outline"
              size="sm"
              className="flex w-full justify-center"
            >
              <span>View profile</span>
            </Button>
          ))}
      </ProviderIdentityCard>
    </div>
  );
}
