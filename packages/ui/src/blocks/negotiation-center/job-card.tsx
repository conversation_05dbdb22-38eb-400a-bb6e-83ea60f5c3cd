import { Button } from "@/ui/primitives/button";

import PreviewLocation from "../../common/PreviewLocation";
import PreviewOrganization from "../../common/PreviewOrganization";
import { Skeleton } from "../../primitives/skeleton";

interface Organization {
  id: string;
  name: string;
  avatar?: string | null;
  description?: string | null;
}

interface Location {
  id: string;
  name: string;
  address: {
    formatted: string;
  };
  description?: string | null;
}

interface JobCardProps {
  specialty: string;
  location?: Location;
  schedule: string;
  jobType?: string;
  employmentType?: string;
  salary?: string;
  highlights?: string[];
  organization?: Organization;
  onViewDetails?: () => void;
  loading?: boolean;
}

export function JobCard({
  specialty,
  location,
  schedule,
  jobType,
  employmentType,
  salary,
  highlights,
  organization,
  onViewDetails,
  loading = false,
}: JobCardProps) {
  return (
    <div className="rounded-lg border border-border bg-card p-4 shadow-sm">
      <div className="mb-4 space-y-3">
        {loading ? (
          <>
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-28" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-20" />
          </>
        ) : (
          <>
            <h3 className="font-semibold text-foreground">{specialty}</h3>
            {location && (
              <PreviewLocation
                location={location}
                loading={loading}
                size="sm"
                showCopyButton={false}
              />
            )}
            <div className="flex flex-wrap gap-x-4 gap-y-2 text-sm text-muted-foreground">
              <span>{schedule}</span>
              {jobType && <span>• {jobType}</span>}
              {employmentType && <span>• {employmentType}</span>}
            </div>
            {salary && (
              <p className="text-sm font-medium text-foreground">{salary}</p>
            )}
            {highlights && highlights.length > 0 && (
              <div className="space-y-1">
                {highlights.slice(0, 2).map((highlight, index) => (
                  <p key={index} className="text-sm text-muted-foreground">
                    • {highlight}
                  </p>
                ))}
                {highlights.length > 2 && (
                  <p className="text-xs text-muted-foreground">
                    +{highlights.length - 2} more benefits
                  </p>
                )}
              </div>
            )}
          </>
        )}
      </div>

      {/* Organization Display */}
      {organization && (
        <div className="mb-4 border-t border-border pt-4">
          <PreviewOrganization
            organization={organization}
            loading={loading}
            size="sm"
          />
        </div>
      )}

      {loading ? (
        <Skeleton className="h-4 w-20" />
      ) : (
        <Button
          onClick={onViewDetails}
          variant="outline"
          size="sm"
          className="w-full"
        >
          View full details
        </Button>
      )}
    </div>
  );
}
