"use client";

import { useCallback } from "react";

import { cn } from "@/ui/lib";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/ui/primitives/dialog";

import type { SecurityImage } from "./types";

export type SecurityIdImageSize = "xs" | "sm" | "md" | "lg" | "xl";

const i18n = {
  en: {
    clickToExpand: "Click to expand",
    image: "Image",
  },
};

export interface SecurityIdImageProps {
  title: string;
  image?: SecurityImage;
  onImageExpand?: (image: SecurityImage) => void;
  size?: SecurityIdImageSize;
  className?: string;
}

export default function SecurityIdImage({
  title,
  image,
  onImageExpand,
  size = "md",
  className,
}: SecurityIdImageProps) {
  const handleOpenChange = useCallback(
    (open: boolean) => {
      if (open && image && onImageExpand) {
        onImageExpand(image);
      }
    },
    [image, onImageExpand],
  );

  const tileContent = (
    <div
      className={cn(
        "aspect-square w-full rounded-lg border border-dashed border-muted-foreground/20",
        "flex flex-col items-center justify-center bg-transparent backdrop-blur-sm",
        {
          "max-w-10 gap-1 p-0.5 md:max-w-16": size === "xs" || size === "sm",
          "max-w-12 gap-2 p-1 md:max-w-16": size === "md",
          "max-w-16 gap-2 p-1.5 md:max-w-20": size === "lg",
          "max-w-20 gap-3 p-2 md:max-w-24": size === "xl",
        },
        className,
      )}
    >
      {/* Title - Centered */}
      <div className="text-center">
        <div
          className={cn("truncate whitespace-nowrap text-muted-foreground", {
            "text-[10px] font-medium":
              size === "xs" || size === "sm" || size === "md",
            "text-xs font-medium": size === "lg" || size === "xl",
          })}
        >
          {title}
        </div>
      </div>

      {/* Click to expand - Smaller and centered */}
      <div
        className={cn("w-full text-center", {
          "max-w-8": size === "xs",
          "max-w-10": size === "sm",
          "max-w-16": size === "md",
          "max-w-20": size === "lg",
          "max-w-24": size === "xl",
        })}
      >
        <div
          className={cn(
            "mx-auto w-[90%] truncate whitespace-nowrap text-muted-foreground",
            {
              "text-[8px]": size === "xs" || size === "sm" || size === "md",
              "text-[10px]": size === "lg" || size === "xl",
            },
          )}
        >
          {i18n.en.clickToExpand}
        </div>
      </div>
    </div>
  );

  if (!image) {
    return tileContent;
  }

  return (
    <Dialog onOpenChange={handleOpenChange}>
      <DialogTrigger
        onClick={(e) => {
          // Prevent the click from bubbling up to the parent
          // This is to prevent the flip card from flipping when the image is clicked
          e.stopPropagation();
        }}
      >
        {tileContent}
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="sr-only">
            {title} {i18n.en.image}
          </DialogTitle>
          <DialogDescription className="sr-only">
            {image.alt || `${title} document image`}
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center justify-center">
          <img
            src={image.url}
            alt={image.alt}
            className="max-h-[80vh] max-w-full rounded object-contain"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
