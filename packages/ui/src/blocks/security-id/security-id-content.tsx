import { cn } from "@/ui/lib";

import type { SecurityIdPreviewSize } from "./security-id-preview";
import type { SecurityImage } from "./types";

import SecurityIdImage from "./security-id-image";
import SecurityIdPreview from "./security-id-preview";
import { DocumentType } from "./types";

const i18n = {
  en: {
    front: "Front",
    back: "Back",
    types: {
      [DocumentType.PASSPORT]: "Passport",
      [DocumentType.GOVERNMENT_ID]: "Government ID",
      [DocumentType.DRIVER_LICENSE]: "Driver's License",
    },
  },
};

export type SecurityIdContentSize = "sm" | "md" | "lg" | "xl";

export interface SecurityIdContentProps {
  // Preview props
  type: DocumentType;
  name?: string;
  documentId?: string;
  expiration?: string;
  // Image props
  frontImage?: SecurityImage;
  backImage?: SecurityImage;
  onImageExpand?: (image: SecurityImage) => void;
  // Layout props
  size?: SecurityIdContentSize;
  className?: string;
}

export default function SecurityIdContent({
  type,
  name = "<PERSON>",
  documentId = "ID123456789",
  expiration,
  frontImage,
  backImage,
  onImageExpand,
  size = "md",
  className,
}: SecurityIdContentProps) {
  // Map SecurityIdContent size to SecurityIdPreview size
  const previewSize: SecurityIdPreviewSize =
    size === "sm" ? "sm" : size === "md" ? "md" : size === "lg" ? "lg" : "xl";

  return (
    <section
      className={cn("flex size-full flex-col justify-between", className)}
    >
      <header
        className={cn("flex items-center justify-between gap-2", {
          "mb-2": size === "sm",
          "mb-3": size === "md",
          "mb-4": size === "lg",
          "mb-5": size === "xl",
        })}
      >
        <h2
          className={cn("text-nowrap font-bold", {
            "text-xs": size === "sm",
            "text-base": size === "md",
            "text-lg": size === "lg",
            "text-xl": size === "xl",
          })}
        >
          {i18n.en.types[type]}
        </h2>
        <p
          className={cn("text-nowrap text-muted-foreground", {
            "text-xs": size === "sm",
            "text-sm": size === "md",
            "text-base": size === "lg",
            "text-lg": size === "xl",
          })}
        >
          {name}
        </p>
      </header>

      <div
        className={cn("grid h-fit grid-cols-[1fr_auto] grid-rows-2", {
          "gap-1.5": size === "sm",
          "gap-2": size === "md",
          "gap-3": size === "lg",
          "gap-4": size === "xl",
        })}
      >
        <div className="row-span-2 bg-transparent backdrop-blur-sm">
          <SecurityIdPreview
            name={name}
            documentId={documentId}
            expiration={expiration}
            size={previewSize}
            className="h-full"
          />
        </div>

        <SecurityIdImage
          title={i18n.en.front}
          image={frontImage}
          onImageExpand={onImageExpand}
        />

        <SecurityIdImage
          title={i18n.en.back}
          image={backImage}
          onImageExpand={onImageExpand}
        />
      </div>
    </section>
  );
}
