// Document type enum with proper display names
export enum DocumentType {
  PASSPORT = "passport",
  GOVERNMENT_ID = "government-id",
  DRIVER_LICENSE = "driver-license",
}

// Verification status
export type VerificationStatus = "verified" | "pending" | "failed";

// Image data for expandable modals
export interface SecurityImage {
  url: string;
  alt: string;
  type: "front" | "back" | "photo" | "signature";
}

// Base security data interface
export interface BaseSecurityData {
  documentId: string;
  fullName: string;
  expirationDate: string;
  verificationStatus: VerificationStatus;
}

// Passport-specific data
export interface PassportSecurityData extends BaseSecurityData {
  type: "passport";
  nationality: string;
  images: {
    photo?: SecurityImage;
    signature?: SecurityImage;
  };
}

// Government ID-specific data
export interface GovernmentIdSecurityData extends BaseSecurityData {
  type: "government-id";
  issuingState: string;
  images: {
    front: SecurityImage;
    back: SecurityImage;
  };
}

// Driver's License-specific data
export interface DriverLicenseSecurityData extends BaseSecurityData {
  type: "driver-license";
  licenseClass: string;
  issuingState: string;
  restrictions?: string[];
  images: {
    front: SecurityImage;
    back: SecurityImage;
  };
}

// Union type for all security data
export type SecurityData =
  | PassportSecurityData
  | GovernmentIdSecurityData
  | DriverLicenseSecurityData;
