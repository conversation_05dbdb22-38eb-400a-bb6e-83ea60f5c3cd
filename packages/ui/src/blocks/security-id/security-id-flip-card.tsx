"use client";

import { useCallback, useMemo, useState } from "react";

import type { AspectRatio } from "@/ui/hooks/use-view-dimensions";

import { Badge } from "@/ui/primitives/badge";
import { <PERSON><PERSON> } from "@/ui/primitives/button";
import FlipCard from "@/ui/shared/flip-card";
import FloatingCard from "@/ui/shared/floating-card";

import type { DocumentType, SecurityData, SecurityImage } from "./types";

import SecurityIdContent from "./security-id-content";

const i18n = {
  en: {
    verified: "Verified",
    accessDenied: "Access Denied",
    notAuthorized: "You are not authorized to view this security information",
    unknown: "Unknown",
    provider: "Provider",
    documentId: "Document ID",
    expiration: "Expiration",
    viewVerificationDetails: "View Verification Details",
    na: "N/A",
  },
};

// Component props interfaces
export interface SecurityIdFlipCardProps {
  type: DocumentType;
  provider?: string;
  authorized?: boolean;
  frontContent?: React.ReactNode;
  securityData?: SecurityData;
  size?: "sm" | "md" | "lg" | "xl";
  aspectRatio?: AspectRatio;
  onImageExpand?: (image: SecurityImage) => void;
  className?: string;
}

export default function SecurityIdFlipCard({
  type,
  provider,
  authorized = false,
  frontContent,
  securityData,
  size = "md",
  aspectRatio = "card",
  onImageExpand,
  className,
}: SecurityIdFlipCardProps) {
  const [isFlipped, setIsFlipped] = useState(false);

  // Handle flip attempt
  const handleFlipAttempt = useCallback(() => {
    if (!authorized) {
      return;
    }

    setIsFlipped(!isFlipped);
  }, [authorized, isFlipped]);

  // Default front content
  const defaultFrontContent = useMemo(
    () => (
      <div className="flex h-full flex-col justify-between">
        {/* Header with verification status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="size-3 rounded-full bg-green-500 dark:bg-green-400" />
            <span className="text-xs font-medium text-green-700 dark:text-green-300">
              {i18n.en.verified}
            </span>
          </div>
          <Badge variant="outline" className="text-xs">
            {type.toUpperCase()}
          </Badge>
        </div>

        {/* Provider info */}
        <div className="text-center">
          <div className="mb-2 text-base font-bold text-foreground dark:text-foreground">
            {provider || i18n.en.unknown}
          </div>
          <div className="text-xs text-muted-foreground dark:text-muted-foreground">
            {i18n.en.provider}
          </div>
        </div>

        {/* Action button */}
        <div className="space-y-2">
          <Button size="sm" variant="ghost" className="w-full text-xs">
            {i18n.en.viewVerificationDetails}
          </Button>
        </div>
      </div>
    ),
    [type, provider],
  );

  return (
    <FlipCard
      size={size}
      isFlipped={isFlipped}
      onFlip={handleFlipAttempt}
      aspectRatio={aspectRatio}
      className={className}
      frontContent={
        <FloatingCard
          size={size}
          className={className}
          aspectRatio={aspectRatio}
        >
          {frontContent || defaultFrontContent}
        </FloatingCard>
      }
      backContent={
        <FloatingCard
          size={size}
          className={className}
          aspectRatio={aspectRatio}
        >
          {useMemo(() => {
            if (!authorized) {
              return (
                <div className="flex h-full items-center justify-center">
                  <div className="space-y-4 text-center">
                    <div className="mx-auto flex size-12 items-center justify-center rounded-full border-2 border-red-500 dark:border-red-400">
                      <div className="size-6 rounded-full bg-red-500 dark:bg-red-400" />
                    </div>
                    <div className="space-y-2">
                      <div className="text-base font-semibold text-red-600 dark:text-red-400">
                        {i18n.en.accessDenied}
                      </div>
                      <div className="max-w-xs text-xs text-muted-foreground dark:text-muted-foreground">
                        {i18n.en.notAuthorized}
                      </div>
                    </div>
                  </div>
                </div>
              );
            }

            // Helper function to get front image based on document type
            const getFrontImage = () => {
              if (!securityData?.images) return undefined;

              if ("front" in securityData.images) {
                return securityData.images.front;
              }
              if ("photo" in securityData.images) {
                return securityData.images.photo;
              }
              return undefined;
            };

            // Helper function to get back image based on document type
            const getBackImage = () => {
              if (!securityData?.images) return undefined;

              if ("back" in securityData.images) {
                return securityData.images.back;
              }
              if ("signature" in securityData.images) {
                return securityData.images.signature;
              }
              return undefined;
            };

            return (
              <SecurityIdContent
                size={size}
                type={(securityData?.type as DocumentType | undefined) || type}
                name={securityData?.fullName || provider || i18n.en.unknown}
                documentId={securityData?.documentId || i18n.en.na}
                expiration={securityData?.expirationDate}
                frontImage={getFrontImage()}
                backImage={getBackImage()}
                onImageExpand={onImageExpand}
              />
            );
          }, [size, authorized, securityData, type, provider, onImageExpand])}
        </FloatingCard>
      }
    />
  );
}
