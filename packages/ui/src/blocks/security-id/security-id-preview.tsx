"use client";

import { memo } from "react";

import { cn } from "@/ui/lib";

const i18n = {
  en: {
    name: "Name",
    documentId: "Document ID",
    expires: "Expires",
  },
};

export type SecurityIdPreviewSize = "xs" | "sm" | "md" | "lg" | "xl";

export interface SecurityIdPreviewProps {
  name: string;
  documentId: string;
  expiration?: string;
  size?: SecurityIdPreviewSize;
  className?: string;
}

const SecurityIdPreview = memo(function SecurityIdPreview({
  name,
  documentId,
  expiration,
  size = "md",
  className,
}: SecurityIdPreviewProps) {
  return (
    <div
      className={cn(
        "border border-dashed border-muted-foreground/20 bg-transparent backdrop-blur-sm",
        {
          "rounded p-1": size === "xs" || size === "sm",
          "rounded-lg p-2": size === "md",
          "rounded-lg p-3": size === "lg",
          "rounded-xl p-4": size === "xl",
        },
        className,
      )}
    >
      <div
        className={cn("flex h-full flex-col justify-center", {
          "space-y-0": size === "xs" || size === "sm",
          "space-y-1": size === "md",
          "space-y-2": size === "lg",
          "space-y-3": size === "xl",
        })}
      >
        {/* Header */}
        <div className="text-start">
          <p
            className={cn(
              "font-medium text-muted-foreground dark:text-muted-foreground",
              {
                "mb-0.5 text-[10px]": size === "xs" || size === "sm",
                "mb-1 text-xs": size === "md",
                "mb-2 text-sm": size === "lg",
                "mb-2 text-xs": size === "xl",
              },
            )}
          >
            {i18n.en.name}
          </p>
          <p
            className={cn(
              "truncate whitespace-nowrap font-bold text-foreground dark:text-foreground",
              {
                "text-[10px]": size === "xs" || size === "sm" || size === "md",
                "text-xs": size === "lg",
                "text-sm": size === "xl",
              },
            )}
          >
            {name}
          </p>
        </div>

        {/* Document ID */}
        <div className="text-start">
          <p
            className={cn("text-muted-foreground dark:text-muted-foreground", {
              "text-[10px]": size === "xs" || size === "sm" || size === "md",
              "text-xs": size === "lg",
              "text-sm": size === "xl",
            })}
          >
            {i18n.en.documentId}
          </p>
          <p
            className={cn(
              "truncate whitespace-nowrap font-mono font-medium text-foreground dark:text-foreground",
              {
                "text-[10px]": size === "xs" || size === "sm" || size === "md",
                "text-xs": size === "lg",
                "text-sm": size === "xl",
              },
            )}
          >
            {documentId}
          </p>
        </div>

        {/* Expiration */}
        {expiration && (
          <div className="text-start">
            <p
              className={cn(
                "text-muted-foreground dark:text-muted-foreground",
                {
                  "text-[10px]":
                    size === "xs" || size === "sm" || size === "md",
                  "text-xs": size === "lg",
                  "text-sm": size === "xl",
                },
              )}
            >
              {i18n.en.expires}
            </p>
            <p
              className={cn(
                "truncate whitespace-nowrap font-medium text-foreground dark:text-foreground",
                {
                  "text-[10px]":
                    size === "xs" || size === "sm" || size === "md",
                  "text-xs": size === "lg",
                  "text-sm": size === "xl",
                },
              )}
            >
              {expiration}
            </p>
          </div>
        )}
      </div>
    </div>
  );
});

export default SecurityIdPreview;
