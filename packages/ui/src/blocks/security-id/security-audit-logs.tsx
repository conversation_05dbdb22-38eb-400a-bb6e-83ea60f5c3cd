"use client";

import { memo, useMemo } from "react";

import { cn } from "@/ui/lib";
import { Badge } from "@/ui/primitives/badge";
import { <PERSON><PERSON> } from "@/ui/primitives/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/ui/primitives/dialog";
import { ScrollArea } from "@/ui/primitives/scroll-area";
import { Separator } from "@/ui/primitives/separator";

export interface SecurityAuditLog {
  id: string;
  timestamp: Date;
  event: "access_attempt" | "flip_attempt" | "security_violation" | "auto_hide";
  authorized: boolean;
  userId?: string;
  violation?: string;
  metadata?: Record<string, unknown>;
}

export interface SecurityAuditLogsProps {
  logs: SecurityAuditLog[];
  maxLogs?: number;
  className?: string;
  trigger?: React.ReactNode;
  title?: string;
  description?: string;
  onClearLogs?: () => void;
  showClearButton?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

// Utility functions moved outside component for better performance
const getEventBadge = (
  log: SecurityAuditLog,
): "default" | "secondary" | "destructive" | "outline" => {
  switch (log.event) {
    case "access_attempt":
      return log.authorized ? "default" : "destructive";
    case "flip_attempt":
      return log.authorized ? "secondary" : "destructive";
    case "security_violation":
      return "destructive";
    case "auto_hide":
      return "outline";
    default:
      return "secondary";
  }
};

const getEventIcon = (log: SecurityAuditLog): string => {
  switch (log.event) {
    case "access_attempt":
      return log.authorized ? "🔓" : "🔒";
    case "flip_attempt":
      return log.authorized ? "🔄" : "⛔";
    case "security_violation":
      return "🚨";
    case "auto_hide":
      return "⏰";
    default:
      return "📝";
  }
};

const getEventDescription = (log: SecurityAuditLog): string => {
  switch (log.event) {
    case "access_attempt":
      return log.authorized
        ? "Authorized access granted"
        : "Unauthorized access denied";
    case "flip_attempt":
      return log.authorized ? "Card flip authorized" : "Card flip blocked";
    case "security_violation":
      return `Security violation: ${log.violation ?? "Unknown violation"}`;
    case "auto_hide":
      return "Sensitive data auto-hidden due to timeout";
    default:
      return "Security event";
  }
};

// Helper function to group logs by date
const groupLogsByDate = (
  logs: SecurityAuditLog[],
): Record<string, SecurityAuditLog[]> => {
  const groups: Record<string, SecurityAuditLog[]> = {};

  logs.forEach((log) => {
    const dateKey = log.timestamp.toDateString();
    groups[dateKey] ??= [];
    groups[dateKey].push(log);
  });

  return groups;
};

// Helper function to calculate summary stats
const calculateStats = (logs: SecurityAuditLog[]) => {
  const total = logs.length;
  const authorized = logs.filter((log) => log.authorized).length;
  const unauthorized = logs.filter((log) => !log.authorized).length;

  return { total, authorized, unauthorized };
};

const SecurityAuditLogs = memo(function SecurityAuditLogs({
  logs,
  maxLogs = 50,
  className,
  trigger,
  title = "Security Access Logs",
  description = "Detailed audit trail of all security events and access attempts",
  onClearLogs,
  showClearButton = false,
  open,
  onOpenChange,
}: SecurityAuditLogsProps) {
  // Limit and sort logs
  const displayLogs = useMemo(() => {
    return logs
      .slice(0, maxLogs)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }, [logs, maxLogs]);

  // Group logs by date
  const groupedLogs = useMemo(
    () => groupLogsByDate(displayLogs),
    [displayLogs],
  );

  // Calculate summary statistics
  const stats = useMemo(() => calculateStats(logs), [logs]);

  const defaultTrigger = (
    <Button variant="ghost" size="sm" className="text-xs text-muted-foreground">
      📋 View Access Logs
      {logs.length > 0 && (
        <Badge variant="secondary" className="ml-2 size-4 p-0 text-xs">
          {logs.length}
        </Badge>
      )}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{trigger ?? defaultTrigger}</DialogTrigger>

      <DialogContent className={cn("max-w-2xl", className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            🛡️ {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Summary stats */}
          <div className="grid grid-cols-3 gap-4 rounded-lg bg-muted/50 p-3">
            <div className="text-center">
              <div className="text-base font-semibold">{stats.total}</div>
              <div className="text-xs text-muted-foreground">Total Events</div>
            </div>
            <div className="text-center">
              <div className="text-base font-semibold text-green-600">
                {stats.authorized}
              </div>
              <div className="text-xs text-muted-foreground">Authorized</div>
            </div>
            <div className="text-center">
              <div className="text-base font-semibold text-red-600">
                {stats.unauthorized}
              </div>
              <div className="text-xs text-muted-foreground">Unauthorized</div>
            </div>
          </div>

          {/* Clear button */}
          {showClearButton && logs.length > 0 && (
            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={onClearLogs}
                className="text-xs"
              >
                Clear Logs
              </Button>
            </div>
          )}

          {/* Logs list */}
          <ScrollArea className="h-96">
            {logs.length === 0 ? (
              <div className="flex h-32 items-center justify-center text-center">
                <div>
                  <div className="text-xl">📝</div>
                  <p className="text-xs text-muted-foreground">
                    No security events logged
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {Object.entries(groupedLogs).map(([date, dateLogs]) => (
                  <div key={date}>
                    <div className="sticky top-0 bg-background pb-2">
                      <h4 className="text-xs font-medium text-muted-foreground">
                        {date}
                      </h4>
                      <Separator className="mt-1" />
                    </div>

                    <div className="space-y-2">
                      {dateLogs.map((log) => (
                        <div
                          key={log.id}
                          className="flex items-start gap-3 rounded-lg border p-3 text-xs"
                        >
                          <div className="text-base">{getEventIcon(log)}</div>

                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={getEventBadge(log)}
                                className="text-xs"
                              >
                                {log.event.replace("_", " ").toUpperCase()}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {log.timestamp.toLocaleTimeString()}
                              </span>
                            </div>

                            <p className="mt-1">{getEventDescription(log)}</p>

                            {log.userId && (
                              <p className="mt-1 text-xs text-muted-foreground">
                                User ID: {log.userId}
                              </p>
                            )}

                            {log.metadata &&
                              Object.keys(log.metadata).length > 0 && (
                                <details className="mt-2">
                                  <summary className="cursor-pointer text-xs text-muted-foreground">
                                    View metadata
                                  </summary>
                                  <pre className="mt-1 text-xs text-muted-foreground">
                                    {JSON.stringify(log.metadata, null, 2)}
                                  </pre>
                                </details>
                              )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
});

export default SecurityAuditLogs;
