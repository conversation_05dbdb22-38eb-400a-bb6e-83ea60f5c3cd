# Negotiation Timeline

A flexible, composable timeline system for displaying negotiation processes and onboarding workflows. The system is built around a composition architecture where individual template blocks are assembled into milestones within a timeline container.

## Architecture Overview

The negotiation timeline follows a **composition-based architecture** where the system is made up of several key layers:

```
Timeline Container
├── Milestone (collapsible sections)
│   ├── Block 1 (template-rendered content)
│   ├── Block 2 (template-rendered content)
│   └── Block N (template-rendered content)
├── Milestone 2
└── Message Input
```

### Core Components

- **`NegotiationTimeline`** - Main container with scrolling, variants, and milestone management
- **`TimelineMilestone`** - Collapsible sections that group related blocks
- **`TimelineBlock`** - Individual content items rendered via the registry pattern
- **`TimelineInput`** - Message input component for user interactions
- **Template Blocks** - Specialized renderers for different block types

## Design System Conventions

### 80px Left Gutter Pattern

All timeline content follows a **strict 80px left gutter pattern**:

```
[  80px Gutter  |  Flexible Content Area  ]
[  Icon/Avatar  |  Block Content & Actions ]
```

**Key measurements:**

- **Total gutter width**: `80px` (`w-20`)
- **Icon/avatar position**: Centered within gutter
- **Timeline line position**: `40px` from left (center of gutter)
- **Content area**: `flex-1` starting at 80px
- **Right padding**: `16px` (`pr-4`) for content breathing room

### Visual Consistency Rules

1. **Icons**: 48px diameter (`size-12`) with consistent theming
2. **Timeline line**: 2px wide, positioned at center of gutter (`left-[40px]`)
3. **Content cards**: Rounded corners (`rounded-lg`), consistent shadow
4. **Spacing**: 16px between content and gutter edge (`pr-4`)

## Registry Pattern

The system uses a **registry pattern** for rendering different block types:

```typescript
// Registry maps block types to their renderers
export type Registry = Record<string, RegistryItem>;

interface RegistryItem {
  type: string;
  render: (block: Block) => React.ReactNode;
}
```

### Using the Registry

```typescript
const registry: Registry = {
  "rate-offer": {
    type: "rate-offer",
    render: (block) => <RateOfferBlock {...block} />
  },
  "background-check": {
    type: "background-check",
    render: (block) => <BackgroundCheckBlock {...block} />
  }
};

<NegotiationTimeline milestones={milestones} registry={registry} />
```

This pattern allows for:

- **Extensibility**: New block types without core changes
- **Type safety**: TypeScript validation of block data
- **Flexibility**: Different renderers for the same data
- **Testing**: Easy mocking and isolated testing

## Template Block Conventions

### Block Structure

Every template block follows these conventions:

```typescript
interface BlockData {
  // Common fields
  status: "pending" | "in_progress" | "completed" | "failed";
  expectedParty?: "provider" | "organization" | undefined;

  // Block-specific data
  // ...
}

interface BlockProps {
  id: string;
  timestamp: Date;
  data: BlockData;

  // Action callbacks (optional)
  onAction?: () => void;
  onRetry?: () => void;
  // ...
}
```

### Expected Party Pattern

Template blocks implement conditional rendering based on `expectedParty`:

```typescript
// Show actions when current user can act
{!data.expectedParty && onAction && (
  <Button onClick={onAction}>Take Action</Button>
)}

// Show placeholder when waiting for specific party
{data.expectedParty === 'provider' && (
  <div className="text-muted-foreground">
    Waiting for provider to complete this step...
  </div>
)}
```

**Rules:**

- **`undefined/null`**: Current user can take action (show buttons)
- **`"provider"`**: Waiting for provider (show waiting message)
- **`"organization"`**: Waiting for organization (show waiting message)

### Status Conventions

All blocks support consistent status values:

- **`pending`**: Not yet started (gray theme)
- **`in_progress`**: Currently active (blue theme)
- **`completed`**: Successfully finished (green theme)
- **`failed`**: Error occurred (red theme)

Some blocks extend this with additional states:

- **`signed`**: For contracts
- **`approved`**: For verifications
- **`rejected`**: For declined items

### Action Button Patterns

Template blocks follow consistent action patterns:

```typescript
// Primary action (colored)
{status === 'pending' && onStart && (
  <Button onClick={onStart}>Start Process</Button>
)}

// Secondary actions (ghost style)
{status === 'completed' && onView && (
  <Button variant="ghost" onClick={onView}>View Details</Button>
)}

// Error recovery (destructive style)
{status === 'failed' && onRetry && (
  <Button variant="destructive" onClick={onRetry}>Retry</Button>
)}
```

## Available Template Blocks

### Communication Blocks

- **`MessageBlock`** - Simple text messages and announcements
- **`TimelineInput`** - User message input component

### Rate & Contract Blocks

- **`RateOfferBlock`** - Rate proposals with accept/decline actions
- **`RateNegotiatedBlock`** - Final rate agreements with view actions
- **`ContractCreatedBlock`** - Contract generation with sign/review actions
- **`ContractSignedBlock`** - Completed contracts with download actions

### Verification Blocks

- **`BackgroundCheckBlock`** - Background verification with multiple check types
- **`BankSetupBlock`** - Banking information setup with account details
- **`IdentityVerificationBlock`** - Identity verification with multiple verification types

## Data Flow & State Management

### Block Data Flow

```
Timeline Component
├── Milestone Data (from props)
├── Block Data (from milestone.blocks)
├── Registry (template mapping)
└── Action Callbacks (event handlers)
```

### State Management Patterns

1. **Milestone Expansion**: Managed internally by `NegotiationTimeline`
2. **Block State**: Passed down via `data` props
3. **Actions**: Callback functions passed to individual blocks
4. **External State**: Parent component manages data updates

### Typical Update Pattern

```typescript
const handleBlockAction = async (blockId: string, action: string) => {
  // Update external state
  await performAction(blockId, action);

  // State update triggers re-render with new data
  refreshTimelineData();
};
```

## Storybook Documentation

### Story Structure

Each template block has comprehensive Storybook documentation:

```
├── [block-name].stories.tsx      # Individual block stories
├── negotiation-timeline.stories.tsx  # Integration stories
└── templates-overview.stories.tsx    # All blocks overview
```

### Story Categories

1. **States Stories** - All status variations
2. **Actions Stories** - Interactive button behaviors
3. **Waiting Stories** - Expected party placeholders
4. **Integration Stories** - Block combinations in timeline
5. **Error Stories** - Failed states and edge cases

### Story Naming Conventions

- **`Default`** - Basic functional state
- **`[Status]State`** - Specific status examples
- **`WithActions`** - Interactive callback examples
- **`WaitingFor[Party]`** - Expected party examples
- **`[Feature]Flow`** - Multi-block scenarios

## Development Guidelines

### Adding New Template Blocks

1. **Create Block Component** in `templates/[block-name].tsx`
2. **Follow 80px Gutter Pattern** for layout consistency
3. **Implement Expected Party Pattern** for conditional rendering
4. **Export Types** for TypeScript consumers
5. **Add to Registry** in `templates/index.ts`
6. **Create Stories** with comprehensive coverage
7. **Update Documentation** in this README

### Block Component Template

```typescript
import React from "react";
import { Clock, [Icon] } from "lucide-react";
import { Button } from "../../primitives/button";

interface [Block]Data {
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  expectedParty?: 'provider' | 'organization';
  // Block-specific fields...
}

interface [Block]Props {
  id: string;
  timestamp: Date;
  data: [Block]Data;

  // Action callbacks
  onAction?: () => void;
  onRetry?: () => void;
}

export function [Block]({ id, timestamp, data, onAction, onRetry }: [Block]Props) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        <div className={`flex size-12 items-center justify-center rounded-full border-2 ${iconClasses}`}>
          <Icon className="size-5" />
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <div className="rounded-lg border bg-card p-4 shadow-sm">
          {/* Block content */}

          {/* Conditional actions/placeholders */}
          {!data.expectedParty && onAction && (
            <Button onClick={onAction}>Action</Button>
          )}

          {data.expectedParty && (
            <div className="text-muted-foreground">
              Waiting for {data.expectedParty}...
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export type { [Block]Data, [Block]Props };
```

### Testing Conventions

1. **Unit Tests** - Test block rendering with different data
2. **Story Tests** - Visual regression via Storybook
3. **Integration Tests** - Timeline with multiple blocks
4. **Accessibility Tests** - Screen reader compatibility
5. **Responsive Tests** - Mobile and desktop layouts

### Performance Considerations

1. **Lazy Loading** - Consider for large timelines
2. **Virtualization** - For timelines with many milestones
3. **Memoization** - Prevent unnecessary re-renders
4. **Image Optimization** - For blocks with media content

## Common Patterns & Examples

### Basic Timeline Usage

```typescript
import { NegotiationTimeline } from "./negotiation-timeline";
import {
  RateOfferBlock,
  BackgroundCheckBlock,
  // ... other blocks
} from "./templates";

const registry = {
  "rate-offer": {
    type: "rate-offer",
    render: (block) => <RateOfferBlock {...block} />
  },
  "background-check": {
    type: "background-check",
    render: (block) => <BackgroundCheckBlock {...block} />
  }
};

const milestones = [
  {
    id: "negotiation",
    title: "Rate Negotiation",
    status: "completed",
    blocks: [
      {
        id: "offer-1",
        type: "rate-offer",
        timestamp: new Date(),
        data: { /* rate data */ }
      }
    ]
  }
];

<NegotiationTimeline
  milestones={milestones}
  registry={registry}
  onSendMessage={handleMessage}
/>
```

### Dynamic Block Registration

```typescript
// Register blocks dynamically
const createRegistry = (userRole: string) => {
  const baseRegistry = { /* base blocks */ };

  if (userRole === 'admin') {
    baseRegistry['admin-panel'] = {
      type: 'admin-panel',
      render: (block) => <AdminPanelBlock {...block} />
    };
  }

  return baseRegistry;
};
```

### Conditional Block Rendering

```typescript
// Filter blocks based on user permissions
const visibleBlocks = milestone.blocks.filter((block) =>
  canUserViewBlock(user, block.type),
);
```

## Troubleshooting

### Common Issues

1. **Missing Registry Item**: Check console warnings for unregistered block types
2. **Layout Breaks**: Ensure 80px gutter pattern in custom blocks
3. **Action Not Working**: Verify callback props are passed correctly
4. **Styling Conflicts**: Check for CSS specificity issues
5. **Performance Issues**: Consider virtualization for large datasets

### Debug Mode

Set `NODE_ENV=development` to enable additional logging:

- Registry misses logged to console
- Block render performance warnings
- Development-only error boundaries

## Migration Guide

### From Legacy Timeline

If migrating from an older timeline system:

1. **Extract Block Data** - Convert existing items to block format
2. **Create Registry** - Map old types to new template components
3. **Update Styling** - Apply 80px gutter pattern
4. **Add TypeScript** - Implement proper typing
5. **Create Stories** - Document all variations

### Breaking Changes

When updating template blocks, consider:

- **Data structure changes** - May require migration
- **Callback signature changes** - Update parent components
- **Styling changes** - Review visual regression tests

## Contributing

When contributing to the negotiation timeline:

1. **Follow Conventions** - Maintain consistency with existing patterns
2. **Add Tests** - Include Storybook stories and unit tests
3. **Update Documentation** - Keep this README current
4. **Consider Performance** - Optimize for large datasets
5. **Ensure Accessibility** - Support screen readers and keyboard navigation

For questions or suggestions, refer to the component development guide or reach out to the design system team.

## Overview

The Negotiation Timeline is a comprehensive component system for displaying interactive timeline data in a visually appealing and responsive format. It consists of a main timeline container, milestone headers, and various block templates for different types of events or data points.

## Features

- **Expandable/Collapsible Milestones**: Click milestone headers to expand or collapse their content
- **Customizable Block Templates**: Use provided templates or create custom ones
- **Registry System**: Type-safe mapping of block types to their renderers
- **Message Input**: Optional input area for user interactions
- **Loading States**: Comprehensive skeleton loading support for all components
- **Responsive Design**: Optimized for different screen sizes
- **Theme Integration**: Uses semantic color tokens

## Core Components

### `NegotiationTimeline`

Main container component that orchestrates the entire timeline.

**Props:**

- `milestones: Milestone[]` - Array of milestone data
- `registry: Registry` - Map of block type to renderer
- `variant?: "primary" | "ghost" | "outline"` - Visual style variant
- `defaultExpanded?: boolean` - Whether milestones start expanded (default: true)
- `showMessageInput?: boolean` - Show message input area (default: true)
- `onSendMessage?: (message: string) => void` - Message send handler
- `loading?: boolean` - Show skeleton loading states (default: false)

### `TimelineMilestone`

Individual milestone header with expandable content.

**Props:**

- `milestone: Milestone` - Milestone data
- `registry: Registry` - Block renderer registry
- `isExpanded: boolean` - Current expanded state
- `onToggle: (milestoneId: string) => void` - Toggle handler
- `isFirst?: boolean` - First milestone flag
- `isLast?: boolean` - Last milestone flag
- `loading?: boolean` - Show skeleton loading states

### `TimelineBlock`

Renders individual blocks within milestones using the registry system.

**Props:**

- `block: Block` - Block data
- `registry: Registry` - Block renderer registry
- `isLast?: boolean` - Last block flag
- `loading?: boolean` - Show skeleton loading states

### `TimelineInput`

Message input component with send functionality.

**Props:**

- `onSend?: (message: string) => void` - Send handler
- `placeholder?: string` - Input placeholder text
- `disabled?: boolean` - Disable input
- `loading?: boolean` - Show skeleton loading states

## Loading States

All timeline components support comprehensive loading states using inline skeleton components. This approach maintains consistent DOM structure and improves performance.

### Key Benefits

- **Consistent Layout**: Same DOM structure whether loading or not
- **Better UX**: Clear visual indication of what content is loading
- **Performance**: Single render tree instead of separate loading trees
- **Maintainable**: Easy to identify what needs skeleton treatment

### Usage

```tsx
// Loading entire timeline
<NegotiationTimeline
  milestones={milestones}
  registry={registry}
  loading={true}
/>;

// Registry items automatically receive loading prop
const registry: Registry = {
  message: {
    type: "message",
    render: (block, loading) => (
      <MessageBlock data={block.data as MessageBlockData} loading={loading} />
    ),
  },
};
```

### Individual Component Loading

```tsx
// Timeline milestone loading
<TimelineMilestone
  milestone={milestone}
  registry={registry}
  isExpanded={true}
  onToggle={handleToggle}
  loading={true}
/>

// Block template loading
<MessageBlock
  data={messageData}
  loading={true}
/>

// Input loading
<TimelineInput
  onSend={handleSend}
  loading={true}
/>
```

## Block Templates

The timeline includes several pre-built block templates, all supporting loading states:

### Message Block

**Type:** `message`
**Data:** `MessageBlockData`
**Loading Features:**

- Skeleton avatar
- Skeleton author name and timestamp
- Skeleton message content (2 lines)

```tsx
interface MessageBlockData {
  author: string;
  message: string;
  avatar?: string;
  timestamp?: Date;
}
```

### Rate Offer Block

**Type:** `rate-offer`
**Data:** `RateOfferBlockData`
**Loading Features:**

- Skeleton icon
- Skeleton rate amount
- Skeleton action buttons
- Skeleton status badge

```tsx
interface RateOfferBlockData {
  rate: string;
  status: "pending" | "accepted" | "countered";
  expectedParty?: "provider" | "organization";
  onAccept?: () => void;
  onCounter?: () => void;
}
```

### Rate Negotiated Block

**Type:** `rate-negotiated`
**Data:** `RateNegotiatedBlockData`
**Loading Features:**

- Skeleton icon
- Skeleton text content
- Skeleton action buttons
- Skeleton badge

```tsx
interface RateNegotiatedBlockData {
  doctor: string;
  rate: string;
  message?: string;
  onViewAgreement?: () => void;
  onViewNegotiationHistory?: () => void;
}
```

### Contract Created Block

**Type:** `contract-created`
**Data:** `ContractCreatedBlockData`
**Loading Features:**

- Skeleton icon
- Skeleton contract details
- Skeleton action buttons
- Skeleton waiting message

```tsx
interface ContractCreatedBlockData {
  organization: string;
  status: "created" | "pending_signature" | "signed";
  contractId?: string;
  expectedParty?: "provider" | "organization";
  onViewContract?: () => void;
  onDownloadContract?: () => void;
  onSignContract?: () => void;
}
```

### Contract Signed Block

**Type:** `contract-signed`
**Data:** `ContractSignedBlockData`
**Loading Features:**

- Skeleton icon
- Skeleton signed date
- Skeleton action buttons

```tsx
interface ContractSignedBlockData {
  signer: string;
  signedAt?: Date;
  onViewSignedContract?: () => void;
  onDownloadSignedContract?: () => void;
}
```

### Verification Blocks

All verification blocks include comprehensive loading states:

#### Background Check Block

**Type:** `background-check`
**Loading Features:**

- Skeleton icon, title, and status badge
- Skeleton provider information
- Skeleton action buttons

#### Bank Setup Block

**Type:** `bank-setup`
**Loading Features:**

- Skeleton icon, title, and status badge
- Skeleton bank details
- Skeleton action buttons

#### Identity Verification Block

**Type:** `identity-verification`
**Loading Features:**

- Skeleton icon, title, and status badge
- Skeleton provider information
- Skeleton action buttons

## Registry System

The registry maps block types to their renderers, with loading support:

```tsx
import type { Registry } from "@/ui/blocks/negotiation-timeline";

import {
  MessageBlock,
  MessageBlockData,
  RateOfferBlock,
  RateOfferBlockData,
} from "@/ui/blocks/negotiation-timeline/templates";

const registry: Registry = {
  message: {
    type: "message",
    render: (block, loading) => (
      <MessageBlock data={block.data as MessageBlockData} loading={loading} />
    ),
  },
  "rate-offer": {
    type: "rate-offer",
    render: (block, loading) => (
      <RateOfferBlock
        data={block.data as RateOfferBlockData}
        loading={loading}
      />
    ),
  },
  // ... other block types
};
```

## Data Structure

### Milestone

```tsx
interface Milestone {
  id: string;
  title: string;
  status: "completed" | "pending" | "active";
  timestamp: Date;
  blocks: Block[];
}
```

### Block

```tsx
interface Block {
  id: string;
  type: string;
  timestamp: Date;
  data: unknown; // Type-specific data
}
```

### Registry Item

```tsx
interface RegistryItem {
  type: string;
  render: (block: Block, loading?: boolean) => React.ReactNode;
}
```

## Complete Example

```tsx
import React, { useState } from "react";

import type { Milestone, Registry } from "@/ui/blocks/negotiation-timeline";

import { NegotiationTimeline } from "@/ui/blocks/negotiation-timeline";
import {
  MessageBlock,
  MessageBlockData,
  RateOfferBlock,
  RateOfferBlockData,
} from "@/ui/blocks/negotiation-timeline/templates";

const ExampleTimeline = () => {
  const [loading, setLoading] = useState(false);

  const registry: Registry = {
    message: {
      type: "message",
      render: (block, loading) => (
        <MessageBlock data={block.data as MessageBlockData} loading={loading} />
      ),
    },
    "rate-offer": {
      type: "rate-offer",
      render: (block, loading) => (
        <RateOfferBlock
          data={block.data as RateOfferBlockData}
          loading={loading}
        />
      ),
    },
  };

  const milestones: Milestone[] = [
    {
      id: "1",
      title: "Initial Discussion",
      status: "completed",
      timestamp: new Date("2024-01-15T10:00:00Z"),
      blocks: [
        {
          id: "1-1",
          type: "message",
          timestamp: new Date("2024-01-15T10:00:00Z"),
          data: {
            author: "Dr. Smith",
            message: "I'm interested in this position",
            avatar: "/avatars/doctor.jpg",
            timestamp: new Date("2024-01-15T10:00:00Z"),
          },
        },
      ],
    },
  ];

  const handleSendMessage = (message: string) => {
    console.log("Message sent:", message);
  };

  return (
    <div className="mx-auto max-w-2xl p-4">
      <button
        onClick={() => setLoading(!loading)}
        className="mb-4 rounded bg-blue-500 px-4 py-2 text-white"
      >
        Toggle Loading: {loading ? "ON" : "OFF"}
      </button>

      <NegotiationTimeline
        milestones={milestones}
        registry={registry}
        onSendMessage={handleSendMessage}
        loading={loading}
      />
    </div>
  );
};

export default ExampleTimeline;
```

## Styling

The timeline uses semantic color tokens and follows the design system:

- **Timeline Line**: `bg-border` (2px wide)
- **Milestone Headers**: Teal color scheme with hover states
- **Block Templates**: Color-coded by type (green for rates, purple for contracts, etc.)
- **Loading States**: Uses `Skeleton` component with appropriate sizing
- **Responsive**: 80px left gutter system for consistent icon alignment

## Best Practices

1. **Loading States**: Always pass the loading prop through your registry renderers
2. **Error Handling**: Include fallback renderers for unknown block types
3. **Performance**: Use React.memo for block templates if rendering large timelines
4. **Accessibility**: Ensure proper ARIA labels and keyboard navigation
5. **Testing**: Test both loading and loaded states in your components

## Development

When creating new block templates:

1. Add loading prop to your component interface
2. Use conditional rendering with skeleton components
3. Maintain the same DOM structure in both states
4. Export your component types from the templates index
5. Add comprehensive loading coverage for all interactive elements
