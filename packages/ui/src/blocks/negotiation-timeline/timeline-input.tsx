"use client";

import type React from "react";

import { useCallback, useState } from "react";
import { Send } from "lucide-react";

import { Button } from "@/ui/primitives/button";
import { Skeleton } from "@/ui/primitives/skeleton";
import { Textarea } from "@/ui/primitives/textarea";

interface TimelineInputProps {
  onSend?: (message: string) => void;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
}

export function TimelineInput({
  onSend,
  placeholder = "Write a message...",
  disabled = false,
  loading = false,
}: TimelineInputProps) {
  const [message, setMessage] = useState("");

  const handleSend = useCallback(() => {
    if (message.trim() && onSend && !disabled) {
      onSend(message);
      setMessage("");
    }
  }, [message, onSend, disabled]);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      // Send with Ctrl+Enter or Cmd+Enter, allow Enter for new lines
      if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend],
  );

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setMessage(e.target.value);
    },
    [],
  );

  return (
    <div className="w-full p-4">
      {loading ? (
        <div className="relative">
          <Skeleton className="h-16 w-full rounded-md" />
          <Skeleton className="absolute bottom-2 right-2 size-8 rounded-full" />
        </div>
      ) : (
        <div className="relative">
          <Textarea
            value={message}
            onChange={handleChange}
            onKeyDown={handleKeyPress}
            placeholder={`${placeholder} (Ctrl+Enter to send)`}
            disabled={disabled}
            className="max-h-32 min-h-[64px] w-full resize-none bg-background/70 pb-12 pr-12 hover:bg-background focus:bg-background"
            rows={2}
          />
          <Button
            onClick={handleSend}
            disabled={disabled || !message.trim()}
            size="sm"
            className="absolute bottom-2 right-2 size-8 rounded-full bg-teal-600 p-0 hover:bg-teal-700 disabled:opacity-50"
          >
            <Send className="size-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
