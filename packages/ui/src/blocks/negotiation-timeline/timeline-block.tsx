import React from "react";

import { Skeleton } from "@/ui/primitives/skeleton";

// Block represents individual timeline items within milestones
export interface Block {
  id: string;
  type: string;
  timestamp: Date;
  data: unknown; // Block-specific data that will be passed to the renderer
}

// Registry item defines how to render each block type
export interface RegistryItem {
  type: string;
  render: (block: Block, loading?: boolean) => React.ReactNode;
}

// Registry maps block types to their renderers
export type Registry = Record<string, RegistryItem>;

interface TimelineBlockProps {
  block: Block;
  registry: Registry;
  isLast?: boolean;
  loading?: boolean;
}

function DefaultBlockRenderer({
  block,
  loading = false,
}: {
  block: Block;
  loading?: boolean;
}) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full border-2 border-gray-300 bg-gray-100 dark:border-gray-600 dark:bg-gray-800">
            <span className="text-xs text-gray-500 dark:text-gray-400">?</span>
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            {loading ? (
              <>
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-4 w-16" />
              </>
            ) : (
              <>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Unknown Block Type: {block.type}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {block.timestamp.toLocaleTimeString()}
                </span>
              </>
            )}
          </div>
          {!loading && block.data != null && (
            <pre className="mt-2 overflow-x-auto text-xs text-gray-600 dark:text-gray-300">
              {JSON.stringify(block.data, null, 2)}
            </pre>
          )}
          {loading && (
            <div className="mt-2 space-y-1">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-3/4" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export function TimelineBlock({
  block,
  registry,
  isLast = false,
  loading = false,
}: TimelineBlockProps) {
  const registryItem = registry[block.type];

  return (
    <div className="relative">
      {registryItem ? (
        registryItem.render(block, loading)
      ) : (
        <>
          <DefaultBlockRenderer block={block} loading={loading} />
          {process.env.NODE_ENV === "development" &&
            (() => {
              console.warn(
                `No registry item found for block type: ${block.type}`,
              );
              return null;
            })()}
        </>
      )}
    </div>
  );
}
