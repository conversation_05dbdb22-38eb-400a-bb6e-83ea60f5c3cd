import { Shield } from "lucide-react";

import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import { Card } from "@/ui/primitives/card";
import { Skeleton } from "@/ui/primitives/skeleton";

interface BackgroundCheckBlockData {
  status: "pending" | "in_progress" | "completed" | "failed";
  provider?: string;
  expectedParty?: "provider" | "organization";
  onStart?: () => void;
  onRetry?: () => void;
  onViewDetails?: () => void;
}

interface BackgroundCheckBlockProps {
  data: BackgroundCheckBlockData;
  loading?: boolean;
}

export function BackgroundCheckBlock({
  data,
  loading = false,
}: BackgroundCheckBlockProps) {
  const getStatusBadge = () => {
    switch (data.status) {
      case "pending":
        return (
          <Badge
            variant="secondary"
            className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400"
          >
            Pending
          </Badge>
        );
      case "in_progress":
        return (
          <Badge
            variant="secondary"
            className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
          >
            In Progress
          </Badge>
        );
      case "completed":
        return (
          <Badge
            variant="default"
            className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
          >
            Completed
          </Badge>
        );
      case "failed":
        return (
          <Badge
            variant="destructive"
            className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
          >
            Failed
          </Badge>
        );
    }
  };

  const renderActions = () => {
    if (data.status === "pending" && data.onStart) {
      return (
        <div className="mt-4 flex justify-center">
          <Button
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
            onClick={data.onStart}
          >
            Start Background Check
          </Button>
        </div>
      );
    }

    if (data.status === "failed" && data.onRetry) {
      return (
        <div className="mt-4 flex justify-center gap-2">
          <Button variant="outline" size="sm" onClick={data.onViewDetails}>
            View Details
          </Button>
          <Button
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
            onClick={data.onRetry}
          >
            Retry Check
          </Button>
        </div>
      );
    }

    if (data.status === "completed" && data.onViewDetails) {
      return (
        <div className="mt-4 flex justify-center">
          <Button variant="outline" size="sm" onClick={data.onViewDetails}>
            View Report
          </Button>
        </div>
      );
    }

    return null;
  };

  const renderPlaceholder = () => {
    if (!data.expectedParty) return null;

    return (
      <div className="mt-4 rounded-md bg-gray-50 p-3 text-center dark:bg-gray-800">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Waiting for{" "}
          <span className="font-medium">
            {data.expectedParty === "provider" ? "provider" : "organization"}
          </span>{" "}
          to complete background check
        </p>
      </div>
    );
  };

  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full bg-blue-500">
            <Shield className="size-6 text-white" />
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <Card className="border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20">
          <div className="mb-2 flex items-center justify-between">
            {loading ? (
              <>
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </>
            ) : (
              <>
                <p className="font-semibold text-blue-800 dark:text-blue-400">
                  Background Check
                </p>
                {getStatusBadge()}
              </>
            )}
          </div>

          {loading ? (
            <Skeleton className="h-4 w-40" />
          ) : (
            data.provider && (
              <p className="text-gray-600 dark:text-gray-400">
                Provider: {data.provider}
              </p>
            )
          )}

          {loading ? (
            <div className="mt-4 flex justify-center">
              <Skeleton className="h-8 w-32" />
            </div>
          ) : (
            <>
              {renderActions()}
              {renderPlaceholder()}
            </>
          )}
        </Card>
      </div>
    </div>
  );
}

export type { BackgroundCheckBlockData, BackgroundCheckBlockProps };
