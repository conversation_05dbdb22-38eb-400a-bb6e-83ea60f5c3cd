import { CreditCard } from "lucide-react";

import { Badge } from "@/ui/primitives/badge";
import { <PERSON>ton } from "@/ui/primitives/button";
import { Card } from "@/ui/primitives/card";
import { Skeleton } from "@/ui/primitives/skeleton";

interface BankSetupBlockData {
  status: "pending" | "in_progress" | "completed" | "failed";
  bankName?: string;
  accountType?: string;
  expectedParty?: "provider" | "organization";
  onStartSetup?: () => void;
  onRetry?: () => void;
  onViewDetails?: () => void;
}

interface BankSetupBlockProps {
  data: BankSetupBlockData;
  loading?: boolean;
}

export function BankSetupBlock({ data, loading = false }: BankSetupBlockProps) {
  const getStatusBadge = () => {
    switch (data.status) {
      case "pending":
        return (
          <Badge
            variant="secondary"
            className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400"
          >
            Pending
          </Badge>
        );
      case "in_progress":
        return (
          <Badge
            variant="secondary"
            className="bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400"
          >
            In Progress
          </Badge>
        );
      case "completed":
        return (
          <Badge
            variant="default"
            className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
          >
            Completed
          </Badge>
        );
      case "failed":
        return (
          <Badge
            variant="destructive"
            className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
          >
            Failed
          </Badge>
        );
    }
  };

  const renderActions = () => {
    if (data.status === "pending" && data.onStartSetup) {
      return (
        <div className="mt-4 flex justify-center">
          <Button
            size="sm"
            className="bg-orange-600 hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-600"
            onClick={data.onStartSetup}
          >
            Setup Bank Account
          </Button>
        </div>
      );
    }

    if (data.status === "failed" && data.onRetry) {
      return (
        <div className="mt-4 flex justify-center gap-2">
          <Button variant="outline" size="sm" onClick={data.onViewDetails}>
            View Details
          </Button>
          <Button
            size="sm"
            className="bg-orange-600 hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-600"
            onClick={data.onRetry}
          >
            Retry Setup
          </Button>
        </div>
      );
    }

    if (data.status === "completed" && data.onViewDetails) {
      return (
        <div className="mt-4 flex justify-center">
          <Button variant="outline" size="sm" onClick={data.onViewDetails}>
            View Account Details
          </Button>
        </div>
      );
    }

    return null;
  };

  const renderPlaceholder = () => {
    if (!data.expectedParty) return null;

    return (
      <div className="mt-4 rounded-md bg-gray-50 p-3 text-center dark:bg-gray-800">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Waiting for{" "}
          <span className="font-medium">
            {data.expectedParty === "provider" ? "provider" : "organization"}
          </span>{" "}
          to complete bank account setup
        </p>
      </div>
    );
  };

  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full bg-orange-500">
            <CreditCard className="size-6 text-white" />
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <Card className="border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-900/20">
          <div className="mb-2 flex items-center justify-between">
            {loading ? (
              <>
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </>
            ) : (
              <>
                <p className="font-semibold text-orange-800 dark:text-orange-400">
                  Bank Account Setup
                </p>
                {getStatusBadge()}
              </>
            )}
          </div>

          {loading ? (
            <div className="space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-32" />
            </div>
          ) : (
            <div className="space-y-1 text-gray-600 dark:text-gray-400">
              {data.bankName && <p>Bank: {data.bankName}</p>}
              {data.accountType && <p>Account Type: {data.accountType}</p>}
            </div>
          )}

          {loading ? (
            <div className="mt-4 flex justify-center">
              <Skeleton className="h-8 w-32" />
            </div>
          ) : (
            <>
              {renderActions()}
              {renderPlaceholder()}
            </>
          )}
        </Card>
      </div>
    </div>
  );
}

export type { BankSetupBlockData, BankSetupBlockProps };
