import { FileText } from "lucide-react";

import { But<PERSON> } from "@/ui/primitives/button";
import { Card } from "@/ui/primitives/card";
import { Skeleton } from "@/ui/primitives/skeleton";

interface ContractCreatedBlockData {
  organization: string;
  status: "created" | "pending_signature" | "signed";
  contractId?: string;
  expectedParty?: "provider" | "organization";
  onViewContract?: () => void;
  onDownloadContract?: () => void;
  onSignContract?: () => void;
}

interface ContractCreatedBlockProps {
  data: ContractCreatedBlockData;
  loading?: boolean;
}

export function ContractCreatedBlock({
  data,
  loading = false,
}: ContractCreatedBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full bg-purple-500">
            <FileText className="size-6 text-white" />
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <Card className="border-purple-200 bg-purple-50 p-4 dark:border-purple-800 dark:bg-purple-900/20">
          <div className="flex-1">
            {loading ? (
              <>
                <Skeleton className="h-5 w-24" />
                <Skeleton className="mt-1 h-4 w-32" />
                <Skeleton className="mt-1 h-3 w-28" />
              </>
            ) : (
              <>
                <p className="font-semibold text-purple-800 dark:text-purple-400">
                  Contract Created
                </p>
                <p className="text-gray-600 dark:text-gray-400">
                  {data.organization} created
                </p>
                {data.contractId && (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Contract ID: {data.contractId}
                  </p>
                )}
              </>
            )}

            {/* Actions */}
            {loading ? (
              <div className="mt-4 flex justify-center gap-2">
                <Skeleton className="h-8 w-24" />
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-8 w-24" />
              </div>
            ) : (
              !data.expectedParty &&
              (data.onViewContract ||
                data.onDownloadContract ||
                data.onSignContract) && (
                <div className="mt-4 flex justify-center gap-2">
                  {data.onViewContract && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={data.onViewContract}
                    >
                      View Contract
                    </Button>
                  )}
                  {data.onDownloadContract && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={data.onDownloadContract}
                    >
                      Download
                    </Button>
                  )}
                  {data.onSignContract &&
                    data.status === "pending_signature" && (
                      <Button
                        size="sm"
                        className="bg-purple-600 hover:bg-purple-700 dark:bg-purple-700 dark:hover:bg-purple-600"
                        onClick={data.onSignContract}
                      >
                        Sign Contract
                      </Button>
                    )}
                </div>
              )
            )}

            {/* Placeholder */}
            {loading ? (
              <div className="mt-4 rounded-md bg-gray-50 p-3 text-center dark:bg-gray-800">
                <Skeleton className="mx-auto h-4 w-48" />
              </div>
            ) : (
              data.expectedParty && (
                <div className="mt-4 rounded-md bg-gray-50 p-3 text-center dark:bg-gray-800">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Waiting for{" "}
                    <span className="font-medium">
                      {data.expectedParty === "provider"
                        ? "provider"
                        : "organization"}
                    </span>{" "}
                    to{" "}
                    {data.status === "pending_signature"
                      ? "sign contract"
                      : "review contract"}
                  </p>
                </div>
              )
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}

export type { ContractCreatedBlockData, ContractCreatedBlockProps };
