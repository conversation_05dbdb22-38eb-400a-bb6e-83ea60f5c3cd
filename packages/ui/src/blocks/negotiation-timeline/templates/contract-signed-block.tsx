import { CheckCir<PERSON> } from "lucide-react";

import { But<PERSON> } from "@/ui/primitives/button";
import { Card } from "@/ui/primitives/card";
import { Skeleton } from "@/ui/primitives/skeleton";

interface ContractSignedBlockData {
  signer: string;
  signedAt?: Date;
  onViewSignedContract?: () => void;
  onDownloadSignedContract?: () => void;
}

interface ContractSignedBlockProps {
  data: ContractSignedBlockData;
  loading?: boolean;
}

export function ContractSignedBlock({
  data,
  loading = false,
}: ContractSignedBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full bg-gray-400">
            <CheckCircle className="size-6 text-white" />
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <Card className="border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800/50">
          <div className="flex-1">
            {loading ? (
              <>
                <Skeleton className="h-4 w-24" />
                <Skeleton className="mt-1 h-3 w-32" />
              </>
            ) : (
              <>
                <p className="text-gray-700 dark:text-gray-300">
                  Contract signed
                </p>
                {data.signedAt && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Signed {data.signedAt.toLocaleDateString()}
                  </p>
                )}
              </>
            )}

            {/* Actions */}
            {loading ? (
              <div className="mt-4 flex justify-center gap-2">
                <Skeleton className="h-8 w-24" />
                <Skeleton className="h-8 w-20" />
              </div>
            ) : (
              (data.onViewSignedContract || data.onDownloadSignedContract) && (
                <div className="mt-4 flex justify-center gap-2">
                  {data.onViewSignedContract && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={data.onViewSignedContract}
                    >
                      View Contract
                    </Button>
                  )}
                  {data.onDownloadSignedContract && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={data.onDownloadSignedContract}
                    >
                      Download
                    </Button>
                  )}
                </div>
              )
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}

export type { ContractSignedBlockData, ContractSignedBlockProps };
