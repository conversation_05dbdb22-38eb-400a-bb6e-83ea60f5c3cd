import { Avatar, AvatarFallback, AvatarImage } from "@/ui/primitives/avatar";
import { Skeleton } from "@/ui/primitives/skeleton";
import TimeAgo from "@/ui/shared/TimeAgo";

interface MessageBlockData {
  author: string;
  message: string;
  avatar?: string;
  timestamp?: Date;
}

interface MessageBlockProps {
  data: MessageBlockData;
  loading?: boolean;
}

export function MessageBlock({ data, loading = false }: MessageBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered avatar */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <Avatar className="size-12">
            <AvatarImage src={data.avatar} alt={data.author} />
            <AvatarFallback className="text-sm font-medium">
              {data.author.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <div className="flex items-baseline gap-2">
          {loading ? (
            <Skeleton className="h-5 w-24" />
          ) : (
            <p className="truncate font-semibold text-gray-900 dark:text-gray-100">
              {data.author}
            </p>
          )}
          {loading ? (
            <Skeleton className="h-4 w-16" />
          ) : (
            data.timestamp && (
              <TimeAgo
                date={data.timestamp}
                className="shrink-0 text-xs text-gray-500 dark:text-gray-400"
              />
            )
          )}
        </div>
        {loading ? (
          <div className="mt-1 space-y-1">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        ) : (
          <p className="mt-1 leading-relaxed text-gray-700 dark:text-gray-300">
            {data.message}
          </p>
        )}
      </div>
    </div>
  );
}

export type { MessageBlockData, MessageBlockProps };
