"use client";

import React from "react";
import { format } from "date-fns";
import { Clock } from "lucide-react";

import type { AccountCardProps, GenericAccount } from "./types";

import PreviewOrganization from "../../common/PreviewOrganization";
import { cn } from "../../lib";
import { Badge } from "../../primitives/badge";
import { Card, CardContent } from "../../primitives/card";

const defaultStatusLabels = {
  active: "Active",
  pending: "Pending",
  suspended: "Suspended",
  inactive: "Inactive",
};

const defaultTypeLabels = {
  primary: "Primary",
  secondary: "Secondary",
  billing: "Billing",
  development: "Development",
  production: "Production",
};

export function AccountCard<T extends GenericAccount = GenericAccount>({
  account,
  renderMenu,
  onClick,
  className = "",
  statusLabels = defaultStatusLabels,
  typeLabels = defaultTypeLabels,
}: AccountCardProps<T>) {
  const getStatusBadgeProps = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return {
          className:
            "bg-green-100 text-green-800 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800",
          variant: "outline" as const,
        };
      case "pending":
        return {
          className:
            "bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800",
          variant: "outline" as const,
        };
      case "suspended":
        return {
          className:
            "bg-red-100 text-red-800 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800",
          variant: "outline" as const,
        };
      case "inactive":
        return {
          className:
            "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800",
          variant: "outline" as const,
        };
      default:
        return {
          className:
            "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800",
          variant: "outline" as const,
        };
    }
  };

  const getTypeBadgeProps = (type: string) => {
    switch (type.toLowerCase()) {
      case "primary":
        return {
          className:
            "bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800",
          variant: "outline" as const,
        };
      case "billing":
        return {
          className:
            "bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-800",
          variant: "outline" as const,
        };
      default:
        return {
          className:
            "bg-slate-100 text-slate-800 border-slate-200 dark:bg-slate-950 dark:text-slate-300 dark:border-slate-800",
          variant: "outline" as const,
        };
    }
  };

  const formatCreatedAt = (createdAt: string | Date) => {
    try {
      const date =
        typeof createdAt === "string" ? new Date(createdAt) : createdAt;
      return format(date, "M/d/yyyy");
    } catch (error) {
      console.warn("Invalid date format:", createdAt);
      return "Invalid date";
    }
  };

  const statusBadgeProps = getStatusBadgeProps(account.status);
  const typeBadgeProps = getTypeBadgeProps(account.type);

  return (
    <Card
      className={cn(
        "transition-shadow hover:shadow-md",
        onClick && "cursor-pointer",
        className,
      )}
      onClick={() => onClick?.(account)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="min-w-0 flex-1">
            {/* Organization Preview */}
            <div className="mb-3">
              <PreviewOrganization
                organization={account.organization}
                size="lg"
              />
            </div>

            {/* Account Details */}
            <div className="flex flex-wrap items-center gap-2">
              <Badge {...statusBadgeProps}>
                {statusLabels[account.status] || account.status}
              </Badge>
              <Badge {...typeBadgeProps}>
                {typeLabels[account.type] || account.type}
              </Badge>
            </div>

            {/* Created Date */}
            <div className="mt-2 flex items-center gap-1 text-xs text-muted-foreground">
              <Clock className="size-3" />
              <span>Created {formatCreatedAt(account.createdAt)}</span>
            </div>
          </div>

          {/* Menu */}
          {renderMenu && (
            <div className="ml-2 shrink-0">{renderMenu(account)}</div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
