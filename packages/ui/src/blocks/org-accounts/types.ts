import type { FilterGroup } from "../../search/filter";
import type { PaginationState } from "../types";

// Generic interfaces for flexibility
export interface GenericAccount {
  id: string;
  organization: {
    id: string;
    name: string;
    avatar?: string | null;
    description?: string | null;
  };
  status: string;
  type: string;
  createdAt: string | Date;
  metadata?: Record<string, unknown>;
}

export interface GenericAccountWithOriginal<T = unknown>
  extends GenericAccount {
  originalData: T;
}

export interface OrganizationAccountsListProps<
  T extends GenericAccount = GenericAccount,
> {
  // Data
  accounts: T[];
  total: number;
  loading?: boolean;
  error?: Error | null;

  // Configuration
  title?: string;
  description?: string;
  emptyMessage?: string;
  itemNoun?: { singular: string; plural: string };

  // Customization
  renderAccount?: (account: T) => React.ReactNode;
  renderAccountMenu?: (account: T) => React.ReactNode;

  // Actions - should be modal triggers, not forms
  onAddClick?: () => void;
  addButtonLabel?: string;

  // Search and Filtering
  filters?: FilterGroup[];
  searchPlaceholder?: string;
  searchNamespace?: string;

  // Behavior
  pagination?: PaginationState;
  onPaginationChange?: (pagination: PaginationState) => void;
  onAccountClick?: (account: T) => void;

  // Styling
  className?: string;
  gridCols?: 1 | 2 | 3 | 4;
}

// Default account card component
export interface AccountCardProps<T extends GenericAccount = GenericAccount> {
  account: T;
  renderMenu?: (account: T) => React.ReactNode;
  onClick?: (account: T) => void;
  className?: string;
  // Status and type label mappings
  statusLabels?: Record<string, string>;
  typeLabels?: Record<string, string>;
}
