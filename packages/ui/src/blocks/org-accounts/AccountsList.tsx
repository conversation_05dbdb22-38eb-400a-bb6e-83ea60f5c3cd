"use client";

import React from "react";
import { Building2, Plus } from "lucide-react";

import type { GenericAccount, OrganizationAccountsListProps } from "./types";

import { cn } from "../../lib";
import { Button } from "../../primitives/button";
import { SearchFilter, SearchText } from "../../search";
import {
  PaginatedCardContent,
  PaginatedCardFooter,
  PaginatedCardHeader,
  PaginatedCardRoot,
} from "../../shared/PaginatedCard";
import { AccountCard } from "./AccountCard";
import { EmptyAccountsState } from "./EmptyState";

export function AccountsList<T extends GenericAccount = GenericAccount>({
  accounts,
  total,
  loading = false,
  error,
  title = "Organization Accounts",
  description = "Manage organization accounts and access",
  emptyMessage = "No accounts found",
  itemNoun = { singular: "account", plural: "accounts" },
  renderAccount,
  renderAccountMenu,
  onAddClick,
  addButtonLabel = "Add Account",
  filters,
  searchPlaceholder = "Search accounts...",
  searchNamespace = "accounts",
  pagination,
  onPaginationChange,
  onAccountClick,
  className = "",
  gridCols = 2,
}: OrganizationAccountsListProps<T>) {
  const defaultRenderAccount = (account: T) => (
    <AccountCard
      key={account.id}
      account={account}
      renderMenu={renderAccountMenu}
      onClick={onAccountClick}
    />
  );

  const renderActions = () => {
    if (!onAddClick) return null;

    return (
      <Button onClick={onAddClick} className="gap-2">
        <Plus className="size-4" />
        {addButtonLabel}
      </Button>
    );
  };

  const renderFilters = () => {
    if (!filters || filters.length === 0) return null;

    return (
      <div className="flex items-center gap-2">
        <SearchFilter name={searchNamespace} groups={filters} />
        <SearchText
          group={searchNamespace}
          placeholder={searchPlaceholder}
          className="flex-1"
        />
      </div>
    );
  };

  return (
    <PaginatedCardRoot className={className}>
      <PaginatedCardHeader
        icon={<Building2 className="size-5 text-muted-foreground" />}
        title={title}
        count={total}
        description={description}
        loading={loading}
        actions={renderActions()}
        filters={renderFilters()}
      />

      <PaginatedCardContent
        isLoading={loading}
        error={error}
        isEmpty={accounts.length === 0}
        emptyComponent={
          <EmptyAccountsState
            message={`${emptyMessage}. No accounts have been created yet.`}
          />
        }
      >
        <div
          className={cn(
            "grid grid-cols-1 gap-4",
            gridCols === 2 && "sm:grid-cols-2",
            gridCols === 3 && "sm:grid-cols-3",
            gridCols === 4 && "sm:grid-cols-4",
          )}
        >
          {accounts.map(renderAccount || defaultRenderAccount)}
        </div>
      </PaginatedCardContent>

      {pagination && onPaginationChange && (
        <PaginatedCardFooter
          pagination={pagination}
          setPagination={(updaterOrValue) => {
            if (typeof updaterOrValue === "function") {
              onPaginationChange(updaterOrValue(pagination));
            } else {
              onPaginationChange(updaterOrValue);
            }
          }}
          totalItems={total}
          itemNoun={itemNoun}
          loading={loading}
        />
      )}
    </PaginatedCardRoot>
  );
}
