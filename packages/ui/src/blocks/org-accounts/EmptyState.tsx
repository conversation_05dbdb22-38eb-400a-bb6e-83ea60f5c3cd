"use client";

import React from "react";
import { Building2 } from "lucide-react";

export interface EmptyAccountsStateProps {
  message?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export function EmptyAccountsState({
  message = "No accounts found",
  icon: Icon = Building2,
}: EmptyAccountsStateProps) {
  return (
    <div className="flex min-h-[200px] flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
      <Icon className="size-8 text-muted-foreground" />
      <p className="mt-2 text-sm text-muted-foreground">{message}</p>
    </div>
  );
}
