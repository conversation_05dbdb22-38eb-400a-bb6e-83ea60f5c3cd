import type { GenericAccountWithOriginal } from "./types";

import { AccountsList } from "./AccountsList";

// Export types
export type {
  GenericAccount,
  GenericAccountWithOriginal,
  OrganizationAccountsListProps,
  AccountCardProps,
} from "./types";

// Export individual components
export { AccountCard } from "./AccountCard";
export { EmptyAccountsState } from "./EmptyState";
export { AccountsList } from "./AccountsList";

// Re-export the main component as OrganizationAccountsList for backwards compatibility
export { AccountsList as OrganizationAccountsList } from "./AccountsList";

// Utility function to create adapters
export function createAccountAdapter<T>(
  item: T,
  config: {
    getId: (item: T) => string;
    getOrganization: (item: T) => {
      id: string;
      name: string;
      avatar?: string | null;
      description?: string | null;
    };
    getStatus: (item: T) => string;
    getType: (item: T) => string;
    getCreatedAt: (item: T) => string;
    getMetadata?: (item: T) => Record<string, unknown>;
  },
): GenericAccountWithOriginal<T> {
  return {
    id: config.getId(item),
    organization: config.getOrganization(item),
    status: config.getStatus(item),
    type: config.getType(item),
    createdAt: config.getCreatedAt(item),
    metadata: config.getMetadata?.(item),
    originalData: item,
  };
}

// Export default as the main component
export default AccountsList;
