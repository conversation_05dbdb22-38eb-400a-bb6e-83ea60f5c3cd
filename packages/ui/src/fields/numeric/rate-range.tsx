import { forwardRef, useState } from "react";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Label } from "@/ui/primitives/label";
import { Separator } from "@/ui/primitives/separator";
import { Slider } from "@/ui/primitives/slider";

import { Currency } from "./Currency";

const i18n = {
  en: {
    range: {
      label: "Rate Range",
      description: "Set the minimum and maximum rate values",
      minPlaceholder: "Min rate",
      maxPlaceholder: "Max rate",
      validation: {
        invalidRange: "Maximum rate must be greater than minimum rate",
        required: "Both minimum and maximum rates are required",
        type: "Please enter a valid number",
      },
    },
    rate: {
      label: "Rate",
      description: "Select a rate within the specified range",
      placeholder: "Enter rate",
      validation: {
        outOfRange: "Rate must be between {min} and {max}",
        required: "Rate is required",
        type: "Please enter a valid number",
      },
    },
    modes: {
      range: "Set Range",
      rate: "Set Rate",
    },
  },
};

export type RateRangeMode = "range" | "rate";

export interface RateRangeValue {
  min?: number;
  max?: number;
  rate?: number;
}

export interface RateRangeControlProps
  extends Omit<InputProps, "type" | "value" | "onChange"> {
  value?: RateRangeValue;
  onChange?: (value: RateRangeValue) => void;
  minPlaceholder?: string;
  maxPlaceholder?: string;
  ratePlaceholder?: string;
  mode?: RateRangeMode;
  allowModeToggle?: boolean;
  step?: number;
  currency?: string; // For slider display formatting
  min?: number;
  max?: number;
  allowDecimals?: boolean;
  predefinedRange?: { min: number; max: number };
  useSlider?: boolean;
  showInputs?: boolean;
  showTooltips?: boolean;
}

export const RateRangeControl = forwardRef<
  HTMLInputElement,
  RateRangeControlProps
>(function RateRangeControl(
  {
    value = {},
    onChange,
    minPlaceholder = i18n.en.range.minPlaceholder,
    maxPlaceholder = i18n.en.range.maxPlaceholder,
    ratePlaceholder = i18n.en.rate.placeholder,
    mode: initialMode = "range",
    allowModeToggle = true,
    step = 0.01,
    currency = "$",
    min = 0,
    max,
    allowDecimals = true, // Keep for backwards compatibility but Currency handles decimals
    predefinedRange,
    useSlider = true,
    showInputs = true,
    showTooltips = true,
    ...props
  },
  ref,
) {
  const [mode, setMode] = useState<RateRangeMode>(initialMode);

  // If predefined range is provided and mode is rate, use rate mode
  const effectiveMode =
    predefinedRange && initialMode === "rate" ? "rate" : mode;

  const formatCurrency = (value: number | undefined) => {
    if (value === undefined || value === null) return "";
    return `${currency}${value.toFixed(allowDecimals ? 2 : 0)}`;
  };

  // Note: parseNumber removed - Currency component handles parsing

  // Currency component handlers - Currency already parses to number
  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMin = parseFloat(e.target.value) || undefined;
    onChange?.({
      ...value,
      min: newMin,
    });
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMax = parseFloat(e.target.value) || undefined;
    onChange?.({
      ...value,
      max: newMax,
    });
  };

  const handleRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newRate = parseFloat(e.target.value) || undefined;
    onChange?.({
      ...value,
      rate: newRate,
    });
  };

  // Slider handlers
  const handleSliderRangeChange = (sliderValues: number[]) => {
    const [newMin, newMax] = sliderValues;
    onChange?.({
      ...value,
      min: newMin,
      max: newMax,
    });
  };

  const handleSliderRateChange = (sliderValues: number[]) => {
    const [newRate] = sliderValues;
    onChange?.({
      ...value,
      rate: newRate,
    });
  };

  // Get slider values for current mode
  const getSliderValues = () => {
    if (effectiveMode === "range") {
      return [value.min ?? min ?? 0, value.max ?? max ?? 100];
    } else {
      return [value.rate ?? predefinedRange?.min ?? value.min ?? min ?? 0];
    }
  };

  // Get slider bounds
  const getSliderBounds = () => {
    if (effectiveMode === "range") {
      return {
        min: min ?? 0,
        max: max ?? 1000,
      };
    } else {
      return {
        min: predefinedRange?.min ?? value.min ?? min ?? 0,
        max: predefinedRange?.max ?? value.max ?? max ?? 100,
      };
    }
  };

  const sliderBounds = getSliderBounds();
  const sliderValues = getSliderValues();

  return (
    <div className="space-y-3">
      {/* Mode Toggle */}
      {allowModeToggle && !predefinedRange && (
        <div className="flex gap-1">
          <Button
            type="button"
            variant={effectiveMode === "range" ? "primary" : "outline"}
            size="sm"
            onClick={() => setMode("range")}
          >
            {i18n.en.modes.range}
          </Button>
          <Button
            type="button"
            variant={effectiveMode === "rate" ? "primary" : "outline"}
            size="sm"
            onClick={() => setMode("rate")}
          >
            {i18n.en.modes.rate}
          </Button>
        </div>
      )}

      {/* Display predefined or current range info */}
      {(predefinedRange ||
        (value.min !== undefined && value.max !== undefined)) && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Range:</span>
          <Badge variant="outline">
            {formatCurrency(predefinedRange?.min ?? value.min)} -{" "}
            {formatCurrency(predefinedRange?.max ?? value.max)}
          </Badge>
        </div>
      )}

      {/* Slider Controls */}
      {useSlider && (
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <Label className="text-muted-foreground">
                {effectiveMode === "range"
                  ? "Range Selection"
                  : "Rate Selection"}
              </Label>
              <div className="flex gap-2 text-xs text-muted-foreground">
                <span>{formatCurrency(sliderBounds.min)}</span>
                <span>-</span>
                <span>{formatCurrency(sliderBounds.max)}</span>
              </div>
            </div>
            <Slider
              value={sliderValues}
              onValueChange={
                effectiveMode === "range"
                  ? handleSliderRangeChange
                  : handleSliderRateChange
              }
              min={sliderBounds.min}
              max={sliderBounds.max}
              step={step}
              showTooltip={showTooltips}
              tooltipContent={(val) => formatCurrency(val)}
              className="w-full"
            />
            {effectiveMode === "range" && (
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Min: {formatCurrency(sliderValues[0])}</span>
                <span>Max: {formatCurrency(sliderValues[1])}</span>
              </div>
            )}
            {effectiveMode === "rate" && (
              <div className="text-center text-xs text-muted-foreground">
                Selected: {formatCurrency(sliderValues[0])}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Input Controls */}
      {showInputs && (
        <div className="space-y-3">
          {effectiveMode === "range" && (
            <div className="space-y-2">
              <Label className="text-sm text-muted-foreground">
                Precise Values
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex-1">
                  <Currency
                    placeholder={minPlaceholder}
                    value={value.min || 0}
                    onChange={handleMinChange}
                    {...props}
                  />
                </div>
                <Separator orientation="horizontal" className="w-4" />
                <div className="flex-1">
                  <Currency
                    placeholder={maxPlaceholder}
                    value={value.max || 0}
                    onChange={handleMaxChange}
                    {...props}
                  />
                </div>
              </div>
            </div>
          )}

          {effectiveMode === "rate" && (
            <div className="space-y-2">
              <Label className="text-sm text-muted-foreground">
                Precise Value
              </Label>
              <Currency
                ref={ref}
                placeholder={ratePlaceholder}
                value={value.rate || 0}
                onChange={handleRateChange}
                {...props}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
});

export interface RateRangeFieldProps
  extends Omit<RateRangeControlProps, "value" | "onChange"> {
  name?: string;
  label?: string;
  description?: string;
}

export function RateRangeField({
  name = "rateRange",
  label,
  description,
  mode: initialMode = "range",
  predefinedRange,
  ...props
}: RateRangeFieldProps) {
  const form = useFormContext();

  // Determine effective mode and labels
  const effectiveMode =
    predefinedRange && initialMode === "rate" ? "rate" : initialMode;
  const currentLabel =
    label ||
    (effectiveMode === "range" ? i18n.en.range.label : i18n.en.rate.label);
  const currentDescription =
    description ||
    (effectiveMode === "range"
      ? i18n.en.range.description
      : i18n.en.rate.description);

  const formatCurrency = (value: number | undefined) => {
    if (value === undefined || value === null) return "";
    return `${props.currency || "$"}${value.toFixed(props.allowDecimals !== false ? 2 : 0)}`;
  };

  return (
    <FormField
      control={form.control}
      name={name}
      rules={{
        validate: {
          validRange: (value: RateRangeValue) => {
            if (effectiveMode === "range") {
              if (value?.min !== undefined && value?.max !== undefined) {
                return (
                  value.max > value.min || i18n.en.range.validation.invalidRange
                );
              }
            }
            return true;
          },
          validRate: (value: RateRangeValue) => {
            if (effectiveMode === "rate" && value?.rate !== undefined) {
              const rangeMin = predefinedRange?.min ?? value?.min ?? props.min;
              const rangeMax = predefinedRange?.max ?? value?.max ?? props.max;

              if (rangeMin !== undefined && rangeMax !== undefined) {
                return (
                  (value.rate >= rangeMin && value.rate <= rangeMax) ||
                  i18n.en.rate.validation.outOfRange
                    .replace("{min}", formatCurrency(rangeMin))
                    .replace("{max}", formatCurrency(rangeMax))
                );
              }
            }
            return true;
          },
        },
      }}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{currentLabel}</FormLabel>
          <FormDescription>{currentDescription}</FormDescription>
          <FormControl>
            <RateRangeControl
              {...props}
              mode={initialMode}
              predefinedRange={predefinedRange}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Convenience components for specific use cases
export interface RangeInputProps
  extends Omit<RateRangeFieldProps, "mode" | "allowModeToggle"> {}

export function RangeInput(props: RangeInputProps) {
  return <RateRangeField {...props} mode="range" allowModeToggle={false} />;
}

export interface RateInputProps
  extends Omit<RateRangeFieldProps, "mode" | "allowModeToggle"> {
  range: { min: number; max: number };
}

export function RateInput({ range, ...props }: RateInputProps) {
  return (
    <RateRangeField
      {...props}
      mode="rate"
      allowModeToggle={false}
      predefinedRange={range}
    />
  );
}

// Additional convenience components for slider-focused use cases
export interface SliderRangeInputProps
  extends Omit<RateRangeFieldProps, "mode" | "allowModeToggle" | "useSlider"> {}

export function SliderRangeInput(props: SliderRangeInputProps) {
  return (
    <RateRangeField
      {...props}
      mode="range"
      allowModeToggle={false}
      useSlider={true}
      showInputs={false}
    />
  );
}

export interface SliderRateInputProps
  extends Omit<RateRangeFieldProps, "mode" | "allowModeToggle" | "useSlider"> {
  range: { min: number; max: number };
}

export function SliderRateInput({ range, ...props }: SliderRateInputProps) {
  return (
    <RateRangeField
      {...props}
      mode="rate"
      allowModeToggle={false}
      predefinedRange={range}
      useSlider={true}
      showInputs={false}
    />
  );
}
