"use client";

import { useRef, useState } from "react";

export interface UseFloatingHoverOptions {
  maxRotation?: number;
  perspective?: number;
}

export function useFloatingHover({
  maxRotation = 15,
  perspective = 1000,
}: UseFloatingHoverOptions = {}) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // Calculate relative position from center (-1 to 1)
    const relativeX = (mouseX - centerX) / centerX;
    const relativeY = (mouseY - centerY) / centerY;

    setMousePosition({ x: relativeX, y: relativeY });
  };

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
    setMousePosition({ x: 0, y: 0 });
  };

  const getTransform = () => {
    if (!isHovering) {
      return `perspective(${perspective}px) rotateX(0deg) rotateY(0deg)`;
    }

    // Convert relative position to rotation angles
    const rotateY = mousePosition.x * maxRotation;
    const rotateX = -mousePosition.y * maxRotation;

    return `perspective(${perspective}px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
  };

  const getBoxShadow = (hoverShadow?: string, defaultShadow?: string) => {
    return isHovering
      ? hoverShadow ||
          "0 35px 60px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1)"
      : defaultShadow ||
          "0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1)";
  };

  return {
    elementRef,
    isHovering,
    mousePosition,
    handlers: {
      onMouseMove: handleMouseMove,
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
    },
    getTransform,
    getBoxShadow,
  };
}
