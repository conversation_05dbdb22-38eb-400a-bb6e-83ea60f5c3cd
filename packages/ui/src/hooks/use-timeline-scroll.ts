"use client";

import { useEffect, useRef, useState } from "react";

export function useTimelineScroll(milestones: string[]) {
  const [activeMilestone, setActiveMilestone] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(),
  );
  const observerRef = useRef<IntersectionObserver | null>(null);
  const milestoneRefs = useRef<Map<string, HTMLElement>>(new Map());

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const milestoneId = entry.target.getAttribute("data-milestone-id");
          if (!milestoneId) return;

          if (entry.isIntersecting) {
            setActiveMilestone(milestoneId);
            setExpandedSections((prev) => new Set([...prev, milestoneId]));
          } else {
            // Keep recently viewed sections expanded for a smoother experience
            setTimeout(() => {
              setExpandedSections((prev) => {
                const newSet = new Set(prev);
                if (activeMilestone !== milestoneId) {
                  newSet.delete(milestoneId);
                }
                return newSet;
              });
            }, 1000);
          }
        });
      },
      {
        threshold: 0.3,
        rootMargin: "-20% 0px -20% 0px",
      },
    );

    return () => {
      observerRef.current?.disconnect();
    };
  }, [activeMilestone]);

  const registerMilestone = (id: string, element: HTMLElement) => {
    milestoneRefs.current.set(id, element);
    observerRef.current?.observe(element);
  };

  const unregisterMilestone = (id: string) => {
    const element = milestoneRefs.current.get(id);
    if (element) {
      observerRef.current?.unobserve(element);
      milestoneRefs.current.delete(id);
    }
  };

  const scrollToMilestone = (id: string) => {
    const element = milestoneRefs.current.get(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  return {
    activeMilestone,
    expandedSections,
    registerMilestone,
    unregisterMilestone,
    scrollToMilestone,
  };
}
