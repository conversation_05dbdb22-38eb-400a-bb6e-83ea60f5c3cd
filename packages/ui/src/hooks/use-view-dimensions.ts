import { useMemo } from "react";
import { cva } from "class-variance-authority";

export const viewDimensionsVariants = cva(
  "", // Remove base classes to prevent conflicts
  {
    variants: {
      aspectRatio: {
        auto: "", // Neutral - no aspect ratio constraint
        square: "aspect-square",
        portrait: "aspect-[3/4]",
        landscape: "aspect-[4/3]",
        wide: "aspect-[16/9]",
        ultrawide: "aspect-[21/9]",
        card: "aspect-[1.6/1]", // Credit card / ID card ratio
        photo: "aspect-[5/4]",
        video: "aspect-video",
      },
      size: {
        auto: "", // Neutral - no size constraints
        sm: "min-h-32 max-w-xs sm:min-h-48 sm:max-w-sm",
        md: "min-h-40 max-w-sm sm:min-h-64 sm:max-w-md",
        lg: "min-h-48 max-w-md sm:min-h-80 sm:max-w-lg",
        xl: "min-h-56 max-w-lg sm:min-h-96 sm:max-w-xl",
        "2xl": "min-h-64 max-w-xl sm:min-h-[480px] sm:max-w-2xl",
      },
    },
    defaultVariants: {
      aspectRatio: "auto",
      size: "auto",
    },
  },
);

// Unified aspect ratio types for all components
export type AspectRatio =
  | "auto" // Added auto as neutral option
  | "square"
  | "portrait"
  | "landscape"
  | "wide"
  | "ultrawide"
  | "card"
  | "photo"
  | "video";

// Unified size scale for consistent sizing across components
export type ComponentSize = "auto" | "sm" | "md" | "lg" | "xl" | "2xl"; // Added auto as neutral option

export interface ViewDimensions {
  width: number;
  height: number;
  viewBox: string;
  patternDensity: number;
  aspectRatioClass: string;
  sizeClasses: string;
}

export interface UseViewDimensionsOptions {
  aspectRatio?: AspectRatio;
  size?: number; // Base size for calculations (default 400)
  componentSize?: ComponentSize; // Size variant for components
  customWidth?: number;
  customHeight?: number;
}

/**
 * Unified hook for calculating view dimensions based on aspect ratio or custom dimensions
 * Used across FloatingCard, FlipCard, and design components for consistency
 */
export function useViewDimensions({
  aspectRatio = "auto",
  size = 400,
  componentSize = "auto",
  customWidth,
  customHeight,
}: UseViewDimensionsOptions = {}): ViewDimensions {
  // Memoize dimension calculations separately
  const { width: viewWidth, height: viewHeight } = useMemo(() => {
    // Use custom dimensions if provided
    if (customWidth && customHeight) {
      return { width: customWidth, height: customHeight };
    }

    // Calculate dimensions based on aspect ratio and base size
    switch (aspectRatio) {
      case "square":
        return { width: size, height: size };
      case "landscape":
        return { width: size, height: (size * 3) / 4 }; // 4:3
      case "wide":
        return { width: size, height: (size * 9) / 16 }; // 16:9
      case "ultrawide":
        return { width: size, height: (size * 9) / 21 }; // 21:9
      case "card":
        return { width: size, height: size / 1.6 }; // 1.6:1 (credit card ratio)
      case "photo":
        return { width: size, height: (size * 4) / 5 }; // 5:4
      case "video":
        return { width: size, height: (size * 9) / 16 }; // 16:9 (same as wide)
      case "portrait":
        return { width: (size * 3) / 4, height: size }; // 3:4
      case "auto":
      default:
        return { width: size, height: size }; // Default square for auto
    }
  }, [aspectRatio, size, customWidth, customHeight]);

  // Memoize CVA calls separately for better performance
  const aspectRatioClass = useMemo(() => {
    return viewDimensionsVariants({ aspectRatio, size: "auto" });
  }, [aspectRatio]);

  const sizeClasses = useMemo(() => {
    return viewDimensionsVariants({ aspectRatio: "auto", size: componentSize });
  }, [componentSize]);

  // Memoize final calculations that depend on dimensions
  const { viewBox, patternDensity } = useMemo(() => {
    const viewBox = `0 0 ${viewWidth} ${viewHeight}`;
    const patternDensity = Math.min(viewWidth, viewHeight) / size;

    return { viewBox, patternDensity };
  }, [viewWidth, viewHeight, size]);

  // Return the final memoized object
  return useMemo(
    () => ({
      width: viewWidth,
      height: viewHeight,
      viewBox,
      patternDensity,
      aspectRatioClass,
      sizeClasses,
    }),
    [
      viewWidth,
      viewHeight,
      viewBox,
      patternDensity,
      aspectRatioClass,
      sizeClasses,
    ],
  );
}
