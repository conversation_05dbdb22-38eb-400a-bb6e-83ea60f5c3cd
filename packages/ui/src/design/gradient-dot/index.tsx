"use client";

import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@/ui/lib";

const gradientDotVariants = cva(
  "rounded-full bg-gradient-to-br shadow-xl transition-all duration-200",
  {
    variants: {
      variant: {
        teal: "from-teal-400 to-emerald-500 dark:from-teal-500 dark:to-emerald-600",
        blue: "from-blue-400 to-cyan-500 dark:from-blue-500 dark:to-cyan-600",
        purple:
          "from-purple-400 to-violet-500 dark:from-purple-500 dark:to-violet-600",
        pink: "from-pink-400 to-rose-500 dark:from-pink-500 dark:to-rose-600",
        amber:
          "from-amber-400 to-orange-500 dark:from-amber-500 dark:to-orange-600",
        emerald:
          "from-emerald-400 to-green-500 dark:from-emerald-500 dark:to-green-600",
      },
      size: {
        xs: "size-4",
        sm: "size-6",
        md: "size-8",
        lg: "size-12",
        xl: "size-16",
        "2xl": "size-20",
        "3xl": "size-24",
      },
      position: {
        center: "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",
        "top-left": "absolute left-4 top-4",
        "top-right": "absolute right-4 top-4",
        "bottom-left": "absolute bottom-4 left-4",
        "bottom-right": "absolute bottom-4 right-4",
        relative: "relative",
        static: "",
      },
      shadow: {
        none: "shadow-none",
        sm: "shadow-sm",
        md: "shadow-xl",
        lg: "shadow-2xl",
        colored: "", // Special case - will be handled in component
      },
    },
    defaultVariants: {
      variant: "teal",
      size: "lg",
      position: "center",
      shadow: "colored",
    },
  },
);

export interface GradientDotProps
  extends VariantProps<typeof gradientDotVariants> {
  className?: string;
  style?: React.CSSProperties;
  customSize?: number;
  children?: React.ReactNode;
}

export function GradientDot({
  variant = "teal",
  size = "lg",
  position = "center",
  shadow = "colored",
  className,
  style,
  customSize,
  children,
  ...props
}: GradientDotProps) {
  const getColoredShadow = () => {
    if (shadow !== "colored") return {};

    const shadowColors = {
      teal: "rgba(20, 184, 166, 0.4), 0 4px 6px -2px rgba(20, 184, 166, 0.2)",
      blue: "rgba(59, 130, 246, 0.4), 0 4px 6px -2px rgba(59, 130, 246, 0.2)",
      purple: "rgba(139, 92, 246, 0.4), 0 4px 6px -2px rgba(139, 92, 246, 0.2)",
      pink: "rgba(236, 72, 153, 0.4), 0 4px 6px -2px rgba(236, 72, 153, 0.2)",
      amber: "rgba(245, 158, 11, 0.4), 0 4px 6px -2px rgba(245, 158, 11, 0.2)",
      emerald:
        "rgba(16, 185, 129, 0.4), 0 4px 6px -2px rgba(16, 185, 129, 0.2)",
    };

    return {
      boxShadow: `0 10px 25px -5px ${shadowColors[variant || "teal"]}`,
    };
  };

  const customSizeStyles = customSize
    ? {
        width: `${customSize}px`,
        height: `${customSize}px`,
      }
    : {};

  const combinedStyles = {
    ...customSizeStyles,
    ...(shadow === "colored" ? getColoredShadow() : {}),
    ...style,
  };

  return (
    <div
      className={cn(
        gradientDotVariants({
          variant,
          size: customSize ? undefined : size,
          position,
          shadow: shadow === "colored" ? "md" : shadow,
        }),
        className,
      )}
      style={combinedStyles}
      {...props}
    >
      {children}
    </div>
  );
}

export default GradientDot;
