# GradientDot Component

A flexible gradient dot component with multiple variants, sizes, positions, and shadow options. Originally extracted from the official-tender design system for reusable accent elements.

## Features

- **6 Color Variants**: teal, blue, purple, pink, amber, emerald
- **7 Size Options**: xs (16px) to 3xl (96px)
- **6 Position Presets**: center, corners, relative, static
- **5 Shadow Styles**: including color-matched shadows
- **Custom Sizing**: Override with exact pixel dimensions
- **Content Support**: Can contain text, icons, or other elements
- **Dark Mode**: Automatic color adjustments for dark themes

## Usage

### Basic Usage

```tsx
import { GradientDot } from "@/ui/design/gradient-dot";

// Simple centered dot
<GradientDot variant="teal" size="lg" />

// Custom positioned dot
<GradientDot
  variant="purple"
  position="top-right"
  size="md"
  shadow="colored"
/>
```

### Custom Sizing

```tsx
// Custom size overrides the size prop
<GradientDot customSize={64} variant="emerald" position="center" />
```

### With Content

```tsx
<GradientDot customSize={80} variant="blue">
  <div className="flex h-full items-center justify-center font-bold text-white">
    ✓
  </div>
</GradientDot>
```

### Positioning Options

```tsx
// Absolute positioning presets
<GradientDot position="center" />     // Centered
<GradientDot position="top-left" />   // Top-left corner
<GradientDot position="top-right" />  // Top-right corner
<GradientDot position="bottom-left" /> // Bottom-left corner
<GradientDot position="bottom-right" /> // Bottom-right corner

// Flow positioning
<GradientDot position="relative" />   // Relative positioning
<GradientDot position="static" />     // Static positioning
```

## API Reference

### Props

| Prop         | Type                                                                                                 | Default     | Description                                    |
| ------------ | ---------------------------------------------------------------------------------------------------- | ----------- | ---------------------------------------------- |
| `variant`    | `"teal" \| "blue" \| "purple" \| "pink" \| "amber" \| "emerald"`                                     | `"teal"`    | Color variant of the gradient                  |
| `size`       | `"xs" \| "sm" \| "md" \| "lg" \| "xl" \| "2xl" \| "3xl"`                                             | `"lg"`      | Predefined size (ignored if customSize is set) |
| `position`   | `"center" \| "top-left" \| "top-right" \| "bottom-left" \| "bottom-right" \| "relative" \| "static"` | `"center"`  | Positioning strategy                           |
| `shadow`     | `"none" \| "sm" \| "md" \| "lg" \| "colored"`                                                        | `"colored"` | Shadow style - "colored" matches variant       |
| `customSize` | `number`                                                                                             | `undefined` | Custom size in pixels (overrides size prop)    |
| `className`  | `string`                                                                                             | `undefined` | Additional CSS classes                         |
| `style`      | `React.CSSProperties`                                                                                | `undefined` | Inline styles                                  |
| `children`   | `React.ReactNode`                                                                                    | `undefined` | Content to display inside the dot              |

### Size Reference

| Size  | Pixels | Use Case                           |
| ----- | ------ | ---------------------------------- |
| `xs`  | 16px   | Small indicators, badges           |
| `sm`  | 24px   | Navigation dots, status indicators |
| `md`  | 32px   | Button accents, small decorations  |
| `lg`  | 48px   | Card accents, medium decorations   |
| `xl`  | 64px   | Hero accents, large decorations    |
| `2xl` | 80px   | Main focal points                  |
| `3xl` | 96px   | Primary hero elements              |

### Color Variants

Each variant includes light and dark mode gradients:

- **Teal**: `from-teal-400 to-emerald-500` → `dark:from-teal-500 dark:to-emerald-600`
- **Blue**: `from-blue-400 to-cyan-500` → `dark:from-blue-500 dark:to-cyan-600`
- **Purple**: `from-purple-400 to-violet-500` → `dark:from-purple-500 dark:to-violet-600`
- **Pink**: `from-pink-400 to-rose-500` → `dark:from-pink-500 dark:to-rose-600`
- **Amber**: `from-amber-400 to-orange-500` → `dark:from-amber-500 dark:to-orange-600`
- **Emerald**: `from-emerald-400 to-green-500` → `dark:from-emerald-500 dark:to-green-600`

## Examples

### Status Indicators

```tsx
<div className="flex gap-2">
  <GradientDot variant="emerald" size="sm" position="static" />
  <span>Online</span>
</div>
```

### Card Accents

```tsx
<div className="relative rounded-lg bg-white p-6 shadow">
  <GradientDot variant="purple" size="xl" position="top-right" />
  <h3>Card Title</h3>
  <p>Card content...</p>
</div>
```

### Icon Containers

```tsx
<GradientDot customSize={60} variant="blue">
  <span className="text-2xl text-white">🎯</span>
</GradientDot>
```

## Migration from CenterAccent

If migrating from the old `CenterAccent` component:

```tsx
// Old
<CenterAccent size={24} />

// New
<GradientDot
  customSize={24 * 4}
  variant="teal"
  position="center"
  shadow="colored"
/>
```

## Accessibility

- Use appropriate color contrast when adding text content
- Consider adding `aria-label` for screen readers when used as a pure decoration
- Ensure sufficient color contrast in custom variants
