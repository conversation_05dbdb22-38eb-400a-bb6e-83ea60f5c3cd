# OfficialTenderBackground Component

A modular background component with topographic patterns, wavy lines, and flowing elements. Supports composition through children for maximum flexibility.

## Features

- **Modular Pattern System**: Topographic, wavy lines, flowing lines, diagonal lines
- **Aspect Ratio Support**: 7 predefined ratios plus custom dimensions
- **Composition Ready**: Accepts children for flexible content layering
- **Performance Optimized**: Pattern density auto-adjusts based on dimensions
- **Dark Mode**: Automatic color adjustments for dark themes

## Usage

### Basic Background

```tsx
import { OfficialTenderBackground } from "@/ui/design/official-tender";

// Simple background with default patterns
<OfficialTenderBackground className="absolute inset-0" />;
```

### With Gradient Dot Accent

```tsx
import { GradientDot } from "@/ui/design/gradient-dot";
import { OfficialTenderBackground } from "@/ui/design/official-tender";

<OfficialTenderBackground className="absolute inset-0">
  <GradientDot variant="teal" size="lg" />
</OfficialTenderBackground>;
```

### Multiple Accents

```tsx
<OfficialTenderBackground className="absolute inset-0">
  <GradientDot variant="purple" position="top-right" size="md" />
  <GradientDot variant="emerald" position="bottom-left" size="sm" />
</OfficialTenderBackground>
```

### Custom Content

```tsx
<OfficialTenderBackground className="absolute inset-0">
  <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
    <div className="rounded-full bg-white/20 p-4 backdrop-blur-sm">
      <span className="text-2xl">🎯</span>
    </div>
  </div>
</OfficialTenderBackground>
```

## API Reference

### Props

| Prop                | Type              | Default      | Description                                                     |
| ------------------- | ----------------- | ------------ | --------------------------------------------------------------- |
| `aspectRatio`       | `AspectRatio`     | `"portrait"` | Predefined aspect ratio (ignored if custom dimensions provided) |
| `className`         | `string`          | `undefined`  | Additional CSS classes                                          |
| `opacity`           | `number`          | `1`          | Overall opacity of the background                               |
| `customWidth`       | `number`          | `undefined`  | Custom width in pixels                                          |
| `customHeight`      | `number`          | `undefined`  | Custom height in pixels                                         |
| `showTopographic`   | `boolean`         | `true`       | Show topographic background elements                            |
| `showWavyLines`     | `boolean`         | `true`       | Show wavy line patterns                                         |
| `showFlowingLines`  | `boolean`         | `true`       | Show flowing line patterns                                      |
| `showDiagonalLines` | `boolean`         | `true`       | Show diagonal line patterns                                     |
| `children`          | `React.ReactNode` | `undefined`  | Content to render inside the background                         |

### Aspect Ratios

| Ratio       | Dimensions | Use Case                      |
| ----------- | ---------- | ----------------------------- |
| `square`    | 1:1        | Profile cards, icons          |
| `portrait`  | 3:4        | Default cards, mobile layouts |
| `landscape` | 4:3        | Desktop cards, images         |
| `wide`      | 16:9       | Hero sections, banners        |
| `ultrawide` | 21:9       | Ultra-wide displays           |
| `photo`     | 5:4        | Photo frames                  |
| `video`     | 16:9       | Video containers              |

## Pattern Layers

The background consists of multiple pattern layers with different opacities:

1. **Topographic Elements** (20% opacity) - Elliptical background shapes
2. **Wavy Lines** (60% opacity) - Dynamic wavy line patterns
3. **Flowing Lines** (50% opacity) - Smooth curved line patterns
4. **Diagonal Lines** (30% opacity) - Diagonal flowing line patterns

## Pattern Density

Pattern density automatically adjusts based on the component dimensions:

```typescript
const patternDensity = Math.min(viewWidth, viewHeight) / 400;
```

This ensures patterns scale appropriately for different sizes while maintaining visual consistency.

## Custom Dimensions

When using custom dimensions, patterns automatically scale to fit:

```tsx
<OfficialTenderBackground
  customWidth={800}
  customHeight={400}
  className="absolute inset-0"
>
  <GradientDot customSize={80} variant="blue" />
</OfficialTenderBackground>
```

## Performance Considerations

- Pattern counts are calculated based on actual dimensions
- SVG viewBox adapts to container size
- Use `preserveAspectRatio="none"` for fill backgrounds (automatic)
- Pattern generation is optimized for different screen sizes

## Composition Examples

### In FloatingCard

```tsx
import { FloatingCard } from "@/ui/shared/floating-card";

<FloatingCard showCenterAccent={false}>
  {/* FloatingCard uses OfficialTenderBackground internally */}
</FloatingCard>;
```

### Direct Usage

```tsx
<div className="relative h-64 w-48 overflow-hidden rounded-3xl">
  <OfficialTenderBackground className="absolute inset-0">
    <GradientDot variant="amber" customSize={60}>
      <span className="flex h-full items-center justify-center font-bold text-white">
        ⭐
      </span>
    </GradientDot>
  </OfficialTenderBackground>

  <div className="relative z-10 p-6">
    <h3>Card Content</h3>
    <p>Your content here</p>
  </div>
</div>
```

## Migration from Previous Version

The component now accepts `children` instead of built-in center accent props:

```tsx
// Old (no longer supported)
<OfficialTenderBackground showCenterAccent={true} accentSize={24} />

// New (recommended)
<OfficialTenderBackground>
  <GradientDot customSize={96} variant="teal" />
</OfficialTenderBackground>
```

## Design System Integration

This component is part of the modular design system and works seamlessly with:

- [`GradientDot`](../gradient-dot/README.md) - For accent elements
- [`FloatingCard`](../../shared/floating-card.tsx) - For complete card components
- Custom positioning and styling components
