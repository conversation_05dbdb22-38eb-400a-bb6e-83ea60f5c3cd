"use client";

import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import type {
  AspectRatio,
  ViewDimensions,
} from "@/ui/hooks/use-view-dimensions";

import { useViewDimensions as useUnifiedViewDimensions } from "@/ui/hooks/use-view-dimensions";
import { cn } from "@/ui/lib";

// Wrapper function to maintain backward compatibility with the old API
export function useViewDimensions(
  aspectRatio: AspectRatio = "portrait",
  customWidth?: number,
  customHeight?: number,
): ViewDimensions {
  return useUnifiedViewDimensions({
    aspectRatio,
    customWidth,
    customHeight,
  });
}

// Hook for calculating pattern counts based on dimensions
export function usePatternCounts(patternDensity: number) {
  return {
    waveCount: Math.floor(30 * patternDensity),
    flowCount: Math.floor(15 * patternDensity),
    diagonalCount: Math.floor(20 * patternDensity),
  };
}

const backgroundVariants = cva("absolute inset-0", {
  variants: {
    aspectRatio: {
      square: "",
      portrait: "",
      landscape: "",
      wide: "",
      ultrawide: "",
      photo: "",
      video: "",
    },
  },
  defaultVariants: {
    aspectRatio: "portrait",
  },
});

export interface BackgroundGradientsProps {
  viewWidth: number;
  viewHeight: number;
}

export function BackgroundGradients({
  viewWidth,
  viewHeight,
}: BackgroundGradientsProps) {
  return (
    <defs>
      {/* Light mode gradients */}
      <radialGradient id="topo1-light" cx="30%" cy="20%">
        <stop offset="0%" stopColor="rgb(255 255 255)" stopOpacity="0.3" />
        <stop offset="100%" stopColor="rgb(255 255 255)" stopOpacity="0" />
      </radialGradient>
      <radialGradient id="topo2-light" cx="70%" cy="80%">
        <stop offset="0%" stopColor="rgb(0 0 0)" stopOpacity="0.1" />
        <stop offset="100%" stopColor="rgb(0 0 0)" stopOpacity="0" />
      </radialGradient>
      {/* Dark mode gradients */}
      <radialGradient id="topo1-dark" cx="30%" cy="20%">
        <stop offset="0%" stopColor="rgb(229 231 235)" stopOpacity="0.3" />
        <stop offset="100%" stopColor="rgb(229 231 235)" stopOpacity="0" />
      </radialGradient>
      <radialGradient id="topo2-dark" cx="70%" cy="80%">
        <stop offset="0%" stopColor="rgb(249 250 251)" stopOpacity="0.1" />
        <stop offset="100%" stopColor="rgb(249 250 251)" stopOpacity="0" />
      </radialGradient>
    </defs>
  );
}

export interface TopographicElementsProps {
  viewWidth: number;
  viewHeight: number;
}

export function TopographicElements({
  viewWidth,
  viewHeight,
}: TopographicElementsProps) {
  return (
    <>
      <ellipse
        cx={viewWidth * 0.3}
        cy={viewHeight * 0.2}
        rx="60"
        ry="40"
        fill="url(#topo1-light)"
        className="dark:hidden"
      />
      <ellipse
        cx={viewWidth * 0.3}
        cy={viewHeight * 0.2}
        rx="60"
        ry="40"
        fill="url(#topo1-dark)"
        className="hidden dark:block"
      />
      <ellipse
        cx={viewWidth * 0.7}
        cy={viewHeight * 0.8}
        rx="80"
        ry="50"
        fill="url(#topo2-light)"
        className="dark:hidden"
      />
      <ellipse
        cx={viewWidth * 0.7}
        cy={viewHeight * 0.8}
        rx="80"
        ry="50"
        fill="url(#topo2-dark)"
        className="hidden dark:block"
      />
      <ellipse
        cx={viewWidth * 0.5}
        cy={viewHeight * 0.5}
        rx="40"
        ry="30"
        fill="url(#topo1-light)"
        className="dark:hidden"
      />
      <ellipse
        cx={viewWidth * 0.5}
        cy={viewHeight * 0.5}
        rx="40"
        ry="30"
        fill="url(#topo1-dark)"
        className="hidden dark:block"
      />
    </>
  );
}

export interface WavyLinesPatternProps {
  viewWidth: number;
  viewHeight: number;
  waveCount: number;
}

export function WavyLinesPattern({
  viewWidth,
  viewHeight,
  waveCount,
}: WavyLinesPatternProps) {
  return (
    <>
      {Array.from({ length: waveCount }).map((_, i) => {
        const y = (viewHeight / waveCount) * i + 10;
        const amplitude = 6 + (i % 4) * 3;
        const frequency = 0.015 + (i % 3) * 0.008;
        const phase = (i % 5) * 0.5;
        const segments = Math.floor(viewWidth / 5);

        return (
          <path
            key={i}
            d={`M 0 ${y + Math.sin(phase) * amplitude} ${Array.from({
              length: segments + 1,
            })
              .map((_, x) => {
                const xPos = (x * viewWidth) / segments;
                const yPos =
                  y +
                  Math.sin((xPos / viewWidth) * frequency * viewWidth + phase) *
                    amplitude;
                return `L ${xPos} ${yPos}`;
              })
              .join(" ")}`}
            className="stroke-gray-500 dark:stroke-gray-400"
            strokeWidth="0.8"
            fill="none"
            opacity={0.3 + (i % 4) * 0.15}
          />
        );
      })}
    </>
  );
}

export interface FlowingLinesPatternProps {
  viewWidth: number;
  viewHeight: number;
  flowCount: number;
}

export function FlowingLinesPattern({
  viewWidth,
  viewHeight,
  flowCount,
}: FlowingLinesPatternProps) {
  return (
    <>
      {Array.from({ length: flowCount }).map((_, i) => {
        const y = (viewHeight / flowCount) * i + 25;
        const midPoint1 = viewWidth * 0.25;
        const midPoint2 = viewWidth * 0.5;
        const endPoint = viewWidth;

        return (
          <path
            key={`flow-${i}`}
            d={`M 0 ${y} Q ${midPoint1} ${y - 10} ${midPoint2} ${y} T ${endPoint} ${y}`}
            className="stroke-gray-600 dark:stroke-gray-300"
            strokeWidth="1.2"
            fill="none"
            opacity={0.4}
          />
        );
      })}
    </>
  );
}

export interface DiagonalLinesPatternProps {
  viewWidth: number;
  viewHeight: number;
  diagonalCount: number;
}

export function DiagonalLinesPattern({
  viewWidth,
  viewHeight,
  diagonalCount,
}: DiagonalLinesPatternProps) {
  return (
    <>
      {Array.from({ length: diagonalCount }).map((_, i) => {
        const startY = (viewHeight / diagonalCount) * i;
        const amplitude = 8;
        const frequency = 0.02;
        const segments = Math.floor(viewWidth / 10);

        return (
          <path
            key={`diagonal-${i}`}
            d={`M 0 ${startY} ${Array.from({ length: segments + 1 })
              .map((_, x) => {
                const xPos = (x * viewWidth) / segments;
                const yPos =
                  startY +
                  (xPos / viewWidth) * viewHeight * 0.8 +
                  Math.sin((xPos / viewWidth) * frequency * viewWidth) *
                    amplitude;
                return `L ${xPos} ${yPos}`;
              })
              .join(" ")}`}
            className="stroke-gray-400 dark:stroke-gray-500"
            strokeWidth="0.6"
            fill="none"
            opacity={0.25}
          />
        );
      })}
    </>
  );
}

export interface OfficialTenderBackgroundProps
  extends VariantProps<typeof backgroundVariants> {
  className?: string;
  opacity?: number;
  customWidth?: number;
  customHeight?: number;
  showTopographic?: boolean;
  showWavyLines?: boolean;
  showFlowingLines?: boolean;
  showDiagonalLines?: boolean;
  children?: React.ReactNode;
}

export function OfficialTenderBackground({
  aspectRatio = "portrait",
  className,
  opacity = 1,
  customWidth,
  customHeight,
  showTopographic = true,
  showWavyLines = true,
  showFlowingLines = true,
  showDiagonalLines = true,
  children,
}: OfficialTenderBackgroundProps) {
  const {
    width: viewWidth,
    height: viewHeight,
    viewBox,
    patternDensity,
  } = useViewDimensions(aspectRatio || "portrait", customWidth, customHeight);
  const { waveCount, flowCount, diagonalCount } =
    usePatternCounts(patternDensity);

  // Check if this is being used as a fill background (absolute positioned)
  const isFillBackground = className?.includes("absolute");

  return (
    <div
      className={cn(
        isFillBackground
          ? "absolute inset-0"
          : backgroundVariants({ aspectRatio }),
        className,
      )}
      style={{ opacity }}
    >
      {/* Topographic background effect */}
      {showTopographic && (
        <div className="absolute inset-0 opacity-20">
          <svg
            className="size-full"
            viewBox={viewBox}
            preserveAspectRatio={isFillBackground ? "none" : "xMidYMid slice"}
          >
            <BackgroundGradients
              viewWidth={viewWidth}
              viewHeight={viewHeight}
            />
            <TopographicElements
              viewWidth={viewWidth}
              viewHeight={viewHeight}
            />
          </svg>
        </div>
      )}

      {/* Enhanced wavy lines pattern */}
      {showWavyLines && (
        <div className="absolute inset-0">
          <svg
            className="size-full opacity-60"
            viewBox={viewBox}
            preserveAspectRatio={isFillBackground ? "none" : "xMidYMid slice"}
          >
            <WavyLinesPattern
              viewWidth={viewWidth}
              viewHeight={viewHeight}
              waveCount={waveCount}
            />
          </svg>
        </div>
      )}

      {/* Additional flowing wavy lines */}
      {showFlowingLines && (
        <div className="absolute inset-0">
          <svg
            className="size-full opacity-50"
            viewBox={viewBox}
            preserveAspectRatio={isFillBackground ? "none" : "xMidYMid slice"}
          >
            <FlowingLinesPattern
              viewWidth={viewWidth}
              viewHeight={viewHeight}
              flowCount={flowCount}
            />
          </svg>
        </div>
      )}

      {/* Diagonal flowing lines */}
      {showDiagonalLines && (
        <div className="absolute inset-0">
          <svg
            className="size-full opacity-30"
            viewBox={viewBox}
            preserveAspectRatio={isFillBackground ? "none" : "xMidYMid slice"}
          >
            <DiagonalLinesPattern
              viewWidth={viewWidth}
              viewHeight={viewHeight}
              diagonalCount={diagonalCount}
            />
          </svg>
        </div>
      )}

      {/* Render children */}
      {children}
    </div>
  );
}

export default OfficialTenderBackground;
