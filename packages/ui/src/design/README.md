# Design System

This directory contains modular design components and hooks extracted from the FloatingCard component to create a reusable design system.

## Overview

The design system has been refactored to be more modular and reusable, with the following key benefits:

- **Reusable hooks** for calculating view dimensions and pattern counts
- **Modular pattern components** that can be used independently
- **Configurable backgrounds** with granular control over each design element
- **Type-safe aspect ratio handling**

## Key Components

### Hooks

#### `useViewDimensions(aspectRatio: AspectRatio, customWidth?: number, customHeight?: number)`

Calculates view dimensions, viewBox, and pattern density based on aspect ratio or custom dimensions.

**Parameters:**

- `aspectRatio`: Standard aspect ratio (used when custom dimensions not provided)
- `customWidth`: Optional custom width in pixels
- `customHeight`: Optional custom height in pixels

**Returns:**

- `width`: Calculated or custom width
- `height`: Calculated or custom height
- `viewBox`: SVG viewBox string
- `patternDensity`: Density multiplier for patterns (auto-calculated based on dimensions)

#### `usePatternCounts(patternDensity: number)`

Calculates the number of pattern elements based on density.

**Returns:**

- `waveCount`: Number of wavy lines to render
- `flowCount`: Number of flowing lines to render
- `diagonalCount`: Number of diagonal lines to render

### Pattern Components

#### `BackgroundGradients`

Defines SVG gradients for light and dark modes.

#### `TopographicElements`

Renders elliptical topographic background elements.

#### `WavyLinesPattern`

Generates dynamic wavy line patterns with configurable amplitude and frequency.

#### `FlowingLinesPattern`

Creates smooth flowing curved lines across the background.

#### `DiagonalLinesPattern`

Renders diagonal flowing lines with sine wave variations.

#### `CenterAccent`

A configurable center accent circle with teal gradient and shadow.

### Main Components

#### `OfficialTenderBackground`

The main background component that combines all pattern elements with granular control.

**Props:**

- `aspectRatio`: Controls the overall dimensions and ratios
- `opacity`: Overall background opacity
- `customWidth`: Optional custom width in pixels (overrides aspect ratio)
- `customHeight`: Optional custom height in pixels (overrides aspect ratio)
- `showTopographic`: Toggle topographic elements
- `showWavyLines`: Toggle wavy line patterns
- `showFlowingLines`: Toggle flowing line patterns
- `showDiagonalLines`: Toggle diagonal line patterns
- `showCenterAccent`: Toggle center accent circle
- `accentSize`: Size of the center accent

## Usage

### Basic Usage

```tsx
import { OfficialTenderBackground } from "@/ui/design/official-tender";

// Basic background with all default patterns
<OfficialTenderBackground aspectRatio="portrait" />

// Custom configuration
<OfficialTenderBackground
  aspectRatio="landscape"
  opacity={0.8}
  showWavyLines={false}
  accentSize={32}
/>

// Custom dimensions (overrides aspect ratio)
<OfficialTenderBackground
  customWidth={500}
  customHeight={300}
  showCenterAccent={false}
/>

// Responsive custom dimensions with reduced patterns
<OfficialTenderBackground
  customWidth={800}
  customHeight={200}
  showTopographic={false}
  showDiagonalLines={false}
  accentSize={16}
/>
```

### Using Individual Hooks

```tsx
import {
  usePatternCounts,
  useViewDimensions,
} from "@/ui/design/official-tender";

function CustomBackground() {
  // Standard aspect ratio usage
  const { width, height, viewBox, patternDensity } =
    useViewDimensions("square");

  // Custom dimensions usage
  const customDimensions = useViewDimensions("portrait", 600, 400);

  const { waveCount, flowCount } = usePatternCounts(patternDensity);

  return <svg viewBox={viewBox}>{/* Custom pattern implementation */}</svg>;
}
```

### Using Individual Pattern Components

```tsx
import {
  CenterAccent,
  useViewDimensions,
  WavyLinesPattern,
} from "@/ui/design/official-tender";

function MinimalBackground() {
  const { width, height } = useViewDimensions("portrait", 500, 300);

  return (
    <div className="relative">
      <svg className="absolute inset-0">
        <WavyLinesPattern
          viewWidth={width}
          viewHeight={height}
          waveCount={20}
        />
      </svg>
      <CenterAccent size={16} />
    </div>
  );
}
```

### Integration with FloatingCard

The FloatingCard component now uses this modular system:

```tsx
import FloatingCard from "@/ui/shared/floating-card";

// Enhanced control over background patterns
<FloatingCard
  aspectRatio="landscape"
  backgroundOptions={{
    showWavyLines: false,
    showDiagonalLines: false,
    accentSize: 32,
  }}
>
  <YourContent />
</FloatingCard>

// Custom dimensions with FloatingCard
<FloatingCard
  customWidth={600}
  customHeight={400}
  backgroundOptions={{
    customWidth: 600,
    customHeight: 400,
    showTopographic: false,
  }}
>
  <YourContent />
</FloatingCard>
```

## Aspect Ratio Types

The system supports the following aspect ratios:

- `square`: 1:1 ratio
- `portrait`: 3:4 ratio
- `landscape`: 4:3 ratio
- `wide`: 16:9 ratio
- `ultrawide`: 21:9 ratio
- `photo`: 5:4 ratio
- `video`: Standard video aspect ratio

> 💡 **Tip:** When using custom dimensions, aspect ratios are ignored and the exact pixel dimensions are used.

## Design Patterns

The background patterns are organized in layers:

1. **Topographic Elements** (opacity: 20%) - Base layer with elliptical gradients
2. **Wavy Lines** (opacity: 60%) - Primary pattern layer with dynamic waves
3. **Flowing Lines** (opacity: 50%) - Secondary curved patterns
4. **Diagonal Lines** (opacity: 30%) - Subtle diagonal elements
5. **Center Accent** - Focal point with teal gradient

Each layer can be toggled independently for different design variations.

## Custom Sizing Behavior

When custom dimensions are provided:

- Pattern density is automatically calculated based on the smallest dimension
- All patterns scale proportionally to maintain visual consistency
- Aspect ratio settings are ignored in favor of exact pixel dimensions
- Performance is optimized based on the actual rendering size

> ℹ️ **Note:** Custom dimensions are particularly useful for responsive designs, banners, or when you need precise control over the background size.

## Migration from FloatingCard

The FloatingCard component has been updated to use this modular system while maintaining backward compatibility. All existing props continue to work, with new `backgroundOptions` prop for advanced configuration.

## Future Enhancements

- Additional pattern types (dots, grids, etc.)
- Color theme variants beyond light/dark
- Animation hooks for dynamic patterns
- Export utilities for static pattern generation
- Responsive dimension presets
- Performance optimization for large custom dimensions

```

```
