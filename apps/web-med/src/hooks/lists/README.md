# Lists Directory

This directory contains reusable hooks and utilities for managing list data, search parameters, and table state across the medical platform. It extracts common patterns from the table components to provide a centralized, consistent approach to list management.

## Overview

The medical platform has 19 table components that follow similar patterns for data fetching, search parameter management, and state handling. This directory consolidates these patterns into reusable hooks and utilities.

## Current Table Components Analysis

### Table Components Location

All table components are located in `apps/web-med/src/components/tables/`:

- `ListApplications.tsx` - Job applications management
- `ListBillingShifts.tsx` - Billing shift records
- `ListContracts.tsx` - Contract management
- `ListDocuments.tsx` - Document management
- `ListFacilities.tsx` - Healthcare facilities
- `ListIncidents.tsx` - Incident reporting
- `ListInvoices.tsx` - Invoice management
- `ListJobs.tsx` - Job postings
- `ListOffers.tsx` - Job offers
- `ListOrganizations.tsx` - Organization management
- `ListPayoutShifts.tsx` - Payout shift records
- `ListPayouts.tsx` - Payout management
- `ListPeople.tsx` - People directory
- `ListProviders.tsx` - Healthcare providers
- `ListReviews.tsx` - Review management
- `ListShifts.tsx` - Shift scheduling
- `ListSpecialties.tsx` - Medical specialties
- `ListUsers.tsx` - User management
- `ListValues.tsx` - Value management

### Common Usage Patterns

#### 1. Data Flow Pattern

```tsx
// Data Layer (index.tsx)
export function EntityView() {
  const query = useSearchTextValue("entity");
  const pagination = useSearchPaginationValue("entity");
  const status = useSearchFilterValue("status", "entity");

  const entities = api.entities.getMany.useQuery({
    query,
    pageNumber: pagination.pageIndex,
    pageSize: pagination.pageSize,
    status,
  });

  return <EntityList entities={entities.data} loading={entities.isLoading} />;
}
```

#### 2. Component Props Interface

All table components follow this interface pattern:

```tsx
interface ListEntityProps extends PropsWithChildren {
  loading?: boolean;
  entities?: RouterOutputs["entities"]["getMany"];
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}
```

#### 3. Data Transformation Pattern

```tsx
const data = useMemo(() => {
  if (!entities) return undefined;
  return {
    items: entities.items,
    total: entities.total,
  };
}, [entities]);
```

#### 4. Search Header Pattern

```tsx
header={
  <>
    <SearchText
      group={group}
      loading={loading}
      placeholder={i18n.en.actions.search}
    />
    <SearchOrganization
      group={group}
      loading={loading}
      size="sm"
      variant="outline"
      className="w-fit min-w-48"
      clearable
    />
    {filters}
  </>
}
```

## Components Using Tables

### Pages and Components

- `apps/web-med/src/www/organizations/applications/` - Uses `ListApplications`
- `apps/web-med/src/www/organizations/jobs/` - Uses `ListJobs`
- `apps/web-med/src/www/organizations/shifts/` - Uses `ListShifts`, `ListIncidents`
- `apps/web-med/src/www/organizations/providers/` - Uses `ListProviders`
- `apps/web-med/src/www/organizations/organizations/` - Uses `ListOrganizations`

### Storybook Stories

- `apps/web-med/__stories__/components/tables/` - Contains stories for table components

## Search Parameter Management

### Current Search Hooks

From `@axa/ui/search`:

- `useSearchTextValue(group, name)` - Text search state
- `useSearchPaginationValue(group, name)` - Pagination state
- `useSearchFilterValue(groupId, name)` - Filter state

### URL State Management

- Uses `SearchParams` context provider
- State persists in URL search parameters
- Enables shareable URLs and browser history
- Automatic debouncing for text search (500ms)

## Planned Extraction Strategy

### 1. Generic List Hook

Create `useListData<T>()` hook that encapsulates:

- tRPC query management
- Search parameter integration
- Loading state handling
- Error handling
- Data transformation

### 2. Search Parameter Helpers

Create specialized hooks for common search patterns:

- `useListSearch(group)` - Combined text, filter, and pagination
- `useListFilters(group, filterGroups)` - Filter management
- `useListPagination(group)` - Pagination with defaults

### 3. Table Configuration Factory

Create `createTableConfig()` utility for:

- Consistent table configuration
- i18n integration
- Action definitions
- Column definitions

### 4. Data Transformation Utilities

Create helpers for:

- Router output to table data transformation
- Column definition generation
- Filter group creation

## Implementation Plan

### Phase 1: Core Hooks

1. `useListData.ts` - Generic data fetching hook
2. `useListSearch.ts` - Combined search parameter management
3. `types.ts` - Common type definitions

### Phase 2: Configuration Utilities

1. `createTableConfig.ts` - Table configuration factory
2. `createColumns.ts` - Column definition helpers
3. `createFilters.ts` - Filter group utilities

### Phase 3: Migration Strategy

1. Start with one table component (e.g., `ListJobs`)
2. Extract patterns into hooks
3. Gradually migrate other components
4. Remove duplicated code

### Phase 4: Advanced Features

1. Optimistic updates
2. Bulk operations
3. Export functionality
4. Advanced filtering

## Benefits of Extraction

1. **Consistency** - Standardized patterns across all tables
2. **Maintainability** - Centralized logic for easier updates
3. **Reusability** - Hooks can be used in new table components
4. **Performance** - Optimized data fetching and caching
5. **Developer Experience** - Simplified component creation
6. **Testing** - Easier to test isolated hooks

## Detailed Component Analysis

### Search Parameter Patterns

#### Text Search Implementation

```tsx
// Current pattern in components
<SearchText
  group={group}
  loading={loading}
  placeholder={i18n.en.actions.search}
/>;

// Hook usage in data layer
const query = useSearchTextValue("entity");
```

#### Organization Search Pattern

Many components include organization filtering:

```tsx
<SearchOrganization
  group={group}
  loading={loading}
  size="sm"
  variant="outline"
  className="w-fit min-w-48"
  clearable
/>
```

#### Date Range Filtering

Used in shift and time-based components:

```tsx
<SearchDateRange group="shift" placeholder={i18n.en.placeholders.date} />
```

### Filter Group Patterns

#### Status Filters (Jobs Example)

```tsx
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.all },
      { value: "DRAFT", label: i18n.en.filters.options[JobPostStatus.DRAFT] },
      {
        value: "PUBLISHED",
        label: i18n.en.filters.options[JobPostStatus.PUBLISHED],
      },
      // ... more options
    ],
  },
];
```

#### Type Filters

```tsx
{
  id: "type",
  label: i18n.en.filters.type,
  options: [
    { value: null, label: i18n.en.filters.options.all },
    { value: "PERMANENT", label: i18n.en.filters.options[JobPostType.PERMANENT] },
    { value: "TEMPORARY", label: i18n.en.filters.options[JobPostType.TEMPORARY] },
  ],
}
```

### tRPC Query Patterns

#### Standard Query Structure

```tsx
const entities = api.entities.getMany.useQuery({
  query: searchText,
  pageNumber: pagination.pageIndex,
  pageSize: pagination.pageSize,
  status: statusFilter,
  // Entity-specific filters
  types: typeFilter ? [typeFilter] : undefined,
  classes: classFilter ? [classFilter] : undefined,
  include: {
    relatedEntity: true,
  },
});
```

#### Common Query Parameters

- `query` - Text search
- `pageNumber` - 0-based pagination
- `pageSize` - Items per page (default: 10)
- `status` - Status filtering
- `types` - Type filtering (array)
- `classes` - Classification filtering
- `include` - Related data inclusion

### Column Definition Patterns

#### Standard Column Structure

```tsx
{
  accessorKey: "fieldName",
  header: ({ column }) => (
    <DataTableColumnHeader
      column={column}
      title={i18n.en.headers.fieldName}
    />
  ),
  cell: ({ row }) => (
    <ComponentRenderer data={row.original.fieldName} />
  ),
  filterFn: (row, id, value) => {
    return value.includes(row.original.fieldName);
  },
}
```

#### Link Column Pattern

```tsx
cell: ({ row }) => (
  <Link
    href={i18n.links.entities.replace("[id]", row.original.id)}
    className="transition-colors hover:text-primary"
  >
    {row.original.name}
  </Link>
),
```

### Action Patterns

#### Export Actions

```tsx
createTypedExportCSVAction<EntityType>(
  ["id", "name", "status", "organization"],
  {
    filename: "entities_export.csv",
    label: "Export Selected Entities",
    resolvers: {
      organization: (org) => org?.name || "",
    },
  },
);
```

#### Row Actions

```tsx
{
  type: "row",
  label: "Entity Actions",
  render: (context: ActionContext<EntityType>) => {
    if (context.type === "row") {
      return <EntityMenu entity={context.row} variant="ghost" />;
    }
    return null;
  },
}
```

## Proposed Hook Interfaces

### useListData Hook

```tsx
interface UseListDataOptions<T> {
  group: string;
  queryFn: (params: SearchParams) => UseQueryResult<PaginatedResponse<T>>;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

function useListData<T>(options: UseListDataOptions<T>) {
  const searchText = useSearchTextValue(options.group);
  const pagination = useSearchPaginationValue(options.group);
  const filters = useListFilters(options.group);

  const query = options.queryFn({
    query: searchText,
    pageNumber: pagination.pageIndex,
    pageSize: pagination.pageSize,
    ...filters,
  });

  return {
    data: query.data,
    loading: query.isLoading,
    error: query.error,
    pagination,
    searchText,
    filters,
  };
}
```

### useListFilters Hook

```tsx
interface UseListFiltersOptions {
  group: string;
  filterGroups: FilterGroup[];
}

function useListFilters(options: UseListFiltersOptions) {
  const filters = {};

  for (const filterGroup of options.filterGroups) {
    const value = useSearchFilterValue(filterGroup.id, options.group);
    if (value) {
      filters[filterGroup.id] = value;
    }
  }

  return filters;
}
```

### createTableConfig Utility

```tsx
interface CreateTableConfigOptions {
  groupName: string;
  enableSelection?: boolean;
  i18n: TableI18n;
  filterGroups?: FilterGroup[];
  actions?: TableAction[];
}

function createTableConfig(options: CreateTableConfigOptions): TableConfig {
  return {
    groupName: options.groupName,
    enableSelection: options.enableSelection ?? true,
    i18n: options.i18n,
  };
}
```

## Migration Strategy

### Step 1: Extract Core Types

```tsx
// types.ts
export interface ListComponentProps<T> extends PropsWithChildren {
  loading?: boolean;
  data?: RouterOutputs[string]["getMany"];
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export interface SearchParams {
  query?: string;
  pageNumber: number;
  pageSize: number;
  [key: string]: any;
}
```

### Step 2: Create Base Hook

Start with `ListJobs` component as the first migration target due to its comprehensive filter implementation.

### Step 3: Gradual Migration

1. `ListJobs` → `useJobsData`
2. `ListApplications` → `useApplicationsData`
3. `ListShifts` → `useShiftsData`
4. Continue with remaining components

## Next Steps

1. Create the core hook structure in this directory
2. Implement `useListData` with `ListJobs` component
3. Add search parameter management utilities
4. Create configuration utilities
5. Document migration guide for existing components
6. Implement remaining hooks for all table components
