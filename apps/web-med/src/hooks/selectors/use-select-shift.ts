import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs, ShiftStatus } from "@/api";

import { api } from "@/api/client";

export type Shift = NonNullable<
  RouterOutputs["shifts"]["getMany"]["items"]
>[number];

export interface UseSelectShiftProps {
  organizationId?: string;
  facilityId?: string;
  status?: ShiftStatus[];
  includeLocation?: boolean;
  includeProvider?: boolean;
  includePerson?: boolean;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Shift;
  defaultOptions?: Shift[];
  defaultDebounce?: number;
  onSelect?: (shift: Shift) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectShift({
  organizationId,
  facilityId,
  status,
  includeLocation = true,
  includeProvider = true,
  includePerson = true,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectShiftProps = {}) {
  const [selection, setSelection] = useState<Shift | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const shifts = api.shifts.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      organizationId,
      status,
      include: {
        location: includeLocation,
        provider: includeProvider,
        person: includePerson,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = shifts.isLoading;

  const data = useMemo(() => {
    const apiData = shifts.data?.items;
    const combined = [...defaultOptions, ...(apiData ?? [])];
    // Remove duplicates by ID
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [shifts.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (shift: Shift) => {
      setSelection(shift);
      setOpen(false);
      setQuery("");
      await onSelect?.(shift);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: shifts.refetch,
      hasNextPage: shifts.data
        ? (pageNumber + 1) * pageSize < shifts.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: shifts.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      shifts.data,
      shifts.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
