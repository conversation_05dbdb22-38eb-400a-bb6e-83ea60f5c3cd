import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { FacilityType, RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Facility = NonNullable<
  RouterOutputs["locations"]["getMany"]["items"]
>[number];

export type FacilityStructure = Facility;

export interface UseSelectFacilityProps {
  enabled?: boolean;
  organizationId?: string;
  type?: FacilityType;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Facility;
  defaultDebounce?: number;
  onSelect?: (facility: Facility) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectFacility({
  enabled,
  organizationId,
  type,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectFacilityProps = {}) {
  const [selection, setSelection] = useState<Facility | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const facilities = api.locations.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      organizationId,
      type,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = facilities.isLoading;
  const data = facilities.data?.items;

  const handleSelect = useCallback(
    async (facility: Facility) => {
      setSelection(facility);
      setOpen(false);
      setQuery("");
      await onSelect?.(facility);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data: data ?? [],
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: facilities.refetch,
      hasNextPage: facilities.data
        ? (pageNumber + 1) * pageSize < facilities.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: facilities.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      facilities.data,
      facilities.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
