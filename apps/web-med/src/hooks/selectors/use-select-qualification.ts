import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type {
  QualificationStatus,
  QualificationType,
  RouterOutputs,
} from "@/api";

import { api } from "@/api/client";

export type Qualification = NonNullable<
  RouterOutputs["qualifications"]["getMany"]["items"]
>[number];

export type QualificationStructure = Qualification;

export interface UseSelectQualificationProps {
  type?: QualificationType;
  status?: QualificationStatus;
  providerId?: string;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Qualification;
  defaultDebounce?: number;
  onSelect?: (qualification: Qualification) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectQualification({
  type,
  status,
  providerId,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectQualificationProps = {}) {
  const [selection, setSelection] = useState<Qualification | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const qualifications = api.qualifications.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      type,
      status,
      providerId,
      include: {
        provider: true,
        person: true,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = qualifications.isLoading;
  const data = qualifications.data?.items;

  const handleSelect = useCallback(
    async (qualification: Qualification) => {
      setSelection(qualification);
      setOpen(false);
      setQuery("");
      await onSelect?.(qualification);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: qualifications.refetch,
      hasNextPage: qualifications.data
        ? (pageNumber + 1) * pageSize < qualifications.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: qualifications.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      qualifications.data,
      qualifications.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
