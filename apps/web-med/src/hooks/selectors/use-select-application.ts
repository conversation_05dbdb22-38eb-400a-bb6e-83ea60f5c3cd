import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { ApplicationStatus, RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Application = NonNullable<
  RouterOutputs["applications"]["getMany"]["items"]
>[number];

export interface UseSelectApplicationProps {
  providerId?: string;
  jobId?: string;
  organizationId?: string;
  status?: ApplicationStatus;
  includeProvider?: boolean;
  includeJob?: boolean;
  includeOrganization?: boolean;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Application;
  defaultOptions?: Application[];
  defaultDebounce?: number;
  onSelect?: (application: Application) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectApplication({
  providerId,
  jobId,
  organizationId,
  status,
  includeProvider = false,
  includeJob = false,
  includeOrganization = false,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectApplicationProps = {}) {
  const [selection, setSelection] = useState<Application | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const applications = api.applications.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      providerId,
      jobId,
      status,
      include: {
        provider: includeProvider,
        job: includeJob,
        organization: includeOrganization,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = applications.isLoading;

  const data = useMemo(() => {
    const apiData = applications.data?.items ?? [];
    const combined = [...defaultOptions, ...apiData];
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [applications.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (application: Application) => {
      setSelection(application);
      setOpen(false);
      setQuery("");
      await onSelect?.(application);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: applications.refetch,
      hasNextPage: applications.data
        ? (pageNumber + 1) * pageSize < applications.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: applications.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      applications.data,
      applications.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
