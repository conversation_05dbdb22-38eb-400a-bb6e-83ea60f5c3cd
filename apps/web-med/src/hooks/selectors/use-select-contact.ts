import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Contact = NonNullable<
  RouterOutputs["contacts"]["getMany"]["items"]
>[number];

export interface UseSelectContactProps {
  organizationId?: string;
  includePerson?: boolean;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Contact;
  defaultDebounce?: number;
  onSelect?: (contact: Contact) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectContact({
  organizationId,
  includePerson = true,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectContactProps = {}) {
  const [selection, setSelection] = useState<Contact | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const contacts = api.contacts.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      organizationId,
      include: {
        person: includePerson,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = contacts.isLoading;
  const data = contacts.data?.items;

  const handleSelect = useCallback(
    async (contact: Contact) => {
      setSelection(contact);
      setOpen(false);
      setQuery("");
      await onSelect?.(contact);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data: data ?? [],
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
    ],
  );
}
