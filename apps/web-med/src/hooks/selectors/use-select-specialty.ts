import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Specialty = NonNullable<
  RouterOutputs["specialties"]["getMany"]["items"]
>[number];

export type SpecialtyStructure = Specialty;

export interface UseSelectSpecialtyProps {
  enabled?: boolean;
  organizationId?: string;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Specialty;
  defaultDebounce?: number;
  onSelect?: (specialty: Specialty) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectSpecialty({
  enabled,
  organizationId,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectSpecialtyProps = {}) {
  const [selection, setSelection] = useState<Specialty | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const specialties = api.specialties.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      // organizationId,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = specialties.isLoading;
  const data = specialties.data?.items;

  const handleSelect = useCallback(
    async (specialty: Specialty) => {
      setSelection(specialty);
      setOpen(false);
      setQuery("");
      await onSelect?.(specialty);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: specialties.refetch,
      hasNextPage: specialties.data
        ? (pageNumber + 1) * pageSize < specialties.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: specialties.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      specialties.data,
      specialties.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
