import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type JobPost = NonNullable<
  RouterOutputs["jobs"]["getMany"]["items"]
>[number];

export type JobStructure = JobPost;

export interface UseSelectJobPostProps {
  organizationId?: string;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: JobPost;
  defaultDebounce?: number;
  onSelect?: (job: JobPost) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectJobPost({
  organizationId,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectJobPostProps = {}) {
  const [selection, setSelection] = useState<JobPost | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const jobs = api.jobs.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      organizationId,
      include: {
        organization: true,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = jobs.isLoading;
  const data = jobs.data?.items;

  const handleSelect = useCallback(
    async (job: JobPost) => {
      setSelection(job);
      setOpen(false);
      setQuery("");
      await onSelect?.(job);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data: data ?? [],
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: jobs.refetch,
      hasNextPage: jobs.data
        ? (pageNumber + 1) * pageSize < jobs.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: jobs.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      jobs.data,
      jobs.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
