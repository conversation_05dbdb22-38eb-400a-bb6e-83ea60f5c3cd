import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { ProviderStatus } from "@/api";
import { api } from "@/api/client";

export type Provider = NonNullable<
  RouterOutputs["providers"]["getMany"]["items"]
>[number];

export type ProviderStructure = Provider;

export interface UseSelectProviderProps {
  organizationId?: string;
  status?: ProviderStatus;
  specialties?: string[];
  locationId?: string;
  facilityId?: string;
  minRating?: number;
  minExperience?: number;
  availabilityStatus?: "AVAILABLE" | "BUSY" | "OFF_DUTY";
  includePerson?: boolean;
  includeQualifications?: boolean;
  includeVerification?: boolean;
  includeReviews?: boolean;
  includeExperiences?: boolean;
  includeSpecialties?: boolean;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Provider;
  defaultOptions?: Provider[];
  defaultDebounce?: number;
  onSelect?: (provider: Provider) => void | Promise<void>;
  /** Callback when query value changes */
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectProvider({
  organizationId,
  status = ProviderStatus.ACTIVE,
  specialties,
  facilityId,
  minRating,
  minExperience,
  availabilityStatus,
  includePerson = true,
  includeQualifications = false,
  includeVerification = false,
  includeReviews = false,
  includeExperiences = false,
  includeSpecialties = false,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectProviderProps = {}) {
  const [selection, setSelection] = useState<Provider | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const providers = api.providers.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      organizationId,
      status,
      // Note: Provider-specific filters will be added when API supports them
      // specialties, locationId, facilityId, minRating, minExperience, availabilityStatus
      include: {
        person: includePerson,
        qualifications: includeQualifications,
        verification: includeVerification,
        reviews: includeReviews,
        experiences: includeExperiences,
        specialties: includeSpecialties,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = providers.isLoading;

  const data = useMemo(() => {
    const apiData = providers.data?.items;
    const combined = [...defaultOptions, ...(apiData ?? [])];
    // Remove duplicates by ID
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [providers.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (provider: Provider) => {
      setSelection(provider);
      setOpen(false);
      setQuery("");
      await onSelect?.(provider);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: providers.refetch,
      hasNextPage: providers.data
        ? (pageNumber + 1) * pageSize < providers.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: providers.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      providers.data,
      pageNumber,
      pageSize,
      providers.refetch,
    ],
  );
}
