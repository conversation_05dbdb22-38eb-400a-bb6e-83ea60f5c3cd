import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { JobPositionStatus, JobPostType, RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Position = NonNullable<
  RouterOutputs["jobs"]["positions"]["getMany"]["items"]
>[number];

export interface UseSelectPositionProps {
  organizationId?: string;
  facilityId?: string;
  departmentId?: string;
  providerId?: string;
  status?: JobPositionStatus[];
  type?: JobPostType[];
  includeProvider?: boolean;
  includeLocation?: boolean;
  includeFacility?: boolean;
  includeDepartment?: boolean;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Position;
  defaultOptions?: Position[];
  defaultDebounce?: number;
  onSelect?: (position: Position) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectPosition({
  organizationId,
  facilityId,
  departmentId,
  providerId,
  status,
  type,
  includeProvider = true,
  includeFacility = true,
  includeDepartment = true,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectPositionProps = {}) {
  const [selection, setSelection] = useState<Position | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const positions = api.jobs.positions.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      organizationId,
      providerId,
      locationId: facilityId ?? undefined,
      departmentId: departmentId ?? undefined,
      status: status?.[0],
      type: type?.[0],
      include: {
        provider: includeProvider,
        location: includeFacility,
        department: includeDepartment,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = positions.isLoading;

  const data = useMemo(() => {
    const apiData = positions.data?.items;
    const combined = [...defaultOptions, ...(apiData ?? [])];
    // Remove duplicates by ID
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [positions.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (position: Position) => {
      setSelection(position);
      setOpen(false);
      setQuery("");
      await onSelect?.(position);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: positions.refetch,
      hasNextPage: positions.data
        ? (pageNumber + 1) * pageSize < positions.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: positions.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      positions.data,
      pageNumber,
      pageSize,
      positions.refetch,
    ],
  );
}
