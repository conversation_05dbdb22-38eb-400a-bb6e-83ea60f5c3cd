import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Address = NonNullable<
  RouterOutputs["addresses"]["getMany"]["items"]
>[number];

export interface AddressAsLocation {
  id: string;
  name: string;
  address: {
    formatted: string | null;
    street?: string | null;
    city?: string | null;
    state?: string | null;
    postal?: string | null;
    country?: string | null;
  };
  [key: string]: unknown;
}

export interface UseSelectAddressProps {
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: AddressAsLocation;
  defaultOptions?: AddressAsLocation[];
  defaultDebounce?: number;
  onSelect?: (address: AddressAsLocation) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

function transformAddressToLocation(address: Address): AddressAsLocation {
  return {
    id: address.id,
    name: address.location?.name || address.formatted || "Unknown Location",
    address: {
      formatted: address.formatted,
      street: address.street,
      city: address.city,
      state: address.state,
      postal: address.postal,
      country: address.country,
    },
  };
}

export function useSelectAddress({
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectAddressProps = {}) {
  const [selection, setSelection] = useState<AddressAsLocation | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const addresses = api.addresses.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = addresses.isLoading;

  const data = useMemo(() => {
    const apiData = (addresses.data?.items ?? []).map(
      transformAddressToLocation,
    );
    const combined = [...defaultOptions, ...apiData];
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [addresses.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (address: AddressAsLocation) => {
      setSelection(address);
      setOpen(false);
      setQuery("");
      await onSelect?.(address);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: addresses.refetch,
      hasNextPage: addresses.data
        ? (pageNumber + 1) * pageSize < addresses.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: addresses.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      addresses.data,
      addresses.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
