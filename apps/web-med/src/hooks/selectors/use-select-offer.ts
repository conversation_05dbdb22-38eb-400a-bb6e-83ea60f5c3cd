import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Offer = NonNullable<
  RouterOutputs["offers"]["getMany"]["items"]
>[number];

export interface UseSelectOfferProps {
  providerId?: string;
  includeProvider?: boolean;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Offer;
  defaultDebounce?: number;
  onSelect?: (offer: Offer) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectOffer({
  providerId,
  includeProvider = true,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectOfferProps = {}) {
  const [selection, setSelection] = useState<Offer | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const offers = api.offers.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      providerId,
      include: {
        provider: includeProvider,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = offers.isLoading;
  const data = offers.data?.items;

  const handleSelect = useCallback(
    async (offer: Offer) => {
      setSelection(offer);
      setOpen(false);
      setQuery("");
      await onSelect?.(offer);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data: data ?? [],
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: offers.refetch,
      hasNextPage: offers.data
        ? (pageNumber + 1) * pageSize < offers.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: offers.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      offers.data,
      offers.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
