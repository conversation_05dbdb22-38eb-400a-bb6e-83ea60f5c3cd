import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { IncidentSeverity, IncidentType, RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Incident = NonNullable<
  RouterOutputs["incidents"]["getMany"]["items"]
>[number];

export type IncidentStructure = Incident;

export interface UseSelectIncidentProps {
  type?: IncidentType;
  severity?: IncidentSeverity;
  organizationId?: string;
  providerId?: string;
  shiftId?: string;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Incident;
  defaultDebounce?: number;
  onSelect?: (incident: Incident) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectIncident({
  type,
  severity,
  organizationId,
  providerId,
  shiftId,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectIncidentProps = {}) {
  const [selection, setSelection] = useState<Incident | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const incidents = api.incidents.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      type,
      severity,
      organizationId,
      providerId,
      shiftId,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = incidents.isLoading;
  const data = incidents.data?.items;

  const handleSelect = useCallback(
    async (incident: Incident) => {
      setSelection(incident);
      setOpen(false);
      setQuery("");
      await onSelect?.(incident);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data: data ?? [],
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: incidents.refetch,
      hasNextPage: incidents.data
        ? (pageNumber + 1) * pageSize < incidents.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: incidents.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      incidents.data,
      incidents.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
