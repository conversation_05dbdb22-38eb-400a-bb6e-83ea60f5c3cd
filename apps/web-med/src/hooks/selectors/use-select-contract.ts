import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { ContractStatus, ContractType, RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Contract = NonNullable<
  RouterOutputs["contracts"]["getMany"]["items"]
>[number];

export interface UseSelectContractProps {
  organizationId?: string;
  providerId?: string;
  status?: ContractStatus;
  type?: ContractType;
  includeProvider?: boolean;
  includeOrganization?: boolean;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Contract;
  defaultOptions?: Contract[];
  defaultDebounce?: number;
  contractId?: string;
  onSelect?: (contract: Contract) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectContract({
  organizationId,
  providerId,
  status,
  type,
  includeProvider = true,
  includeOrganization = true,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  contractId,
  onSelect,
  onValueChange,
}: UseSelectContractProps = {}) {
  const [selection, setSelection] = useState<Contract | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const contracts = api.contracts.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      organizationId,
      providerId,
      status,
      types: type ? [type] : undefined,
      include: {
        provider: includeProvider,
        organization: includeOrganization,
      },
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = contracts.isLoading;

  const data = useMemo(() => {
    const apiData = contracts.data?.items ?? [];
    const combined = [...defaultOptions, ...apiData];
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [contracts.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (contract: Contract) => {
      setSelection(contract);
      setOpen(false);
      setQuery("");
      await onSelect?.(contract);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: contracts.refetch,
      hasNextPage: contracts.data
        ? (pageNumber + 1) * pageSize < contracts.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: contracts.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      contracts.data,
      contracts.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
