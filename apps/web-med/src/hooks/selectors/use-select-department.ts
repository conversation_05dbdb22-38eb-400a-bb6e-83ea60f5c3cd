import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Department = NonNullable<
  RouterOutputs["departments"]["getMany"]["items"]
>[number];

export type DepartmentStructure = Department;

export interface UseSelectDepartmentProps {
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Department;
  defaultOptions?: Department[];
  defaultDebounce?: number;
  facilityId?: string;
  onSelect?: (department: Department) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectDepartment({
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  facilityId,
  onSelect,
  onValueChange,
}: UseSelectDepartmentProps = {}) {
  const [selection, setSelection] = useState<Department | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const departments = api.departments.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      facilityId,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = departments.isLoading;

  const data = useMemo(() => {
    const apiData = departments.data?.items ?? [];
    const combined = [...defaultOptions, ...apiData];
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [departments.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (department: Department) => {
      setSelection(department);
      setOpen(false);
      setQuery("");
      await onSelect?.(department);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      hasNextPage: departments.data
        ? (pageNumber + 1) * pageSize < departments.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: departments.data?.total,
      refetch: departments.refetch,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      departments.data,
      departments.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
