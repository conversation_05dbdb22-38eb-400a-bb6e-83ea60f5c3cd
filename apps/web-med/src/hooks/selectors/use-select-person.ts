import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { PersonRole, RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Person = NonNullable<
  RouterOutputs["people"]["getMany"]["items"]
>[number];

export type PersonStructure = Person;

export interface UseSelectPersonProps {
  roles?: PersonRole[];
  enabled?: boolean;
  organizationId?: string;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Person;
  defaultOptions?: Person[];
  defaultDebounce?: number;
  onSelect?: (person: Person) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectPerson({
  roles,
  enabled,
  organizationId,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectPersonProps = {}) {
  const [selection, setSelection] = useState<Person | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const people = api.people.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      roles,
      organizationId,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = people.isLoading;

  const data = useMemo(() => {
    const apiData = people.data?.items;
    const combined = [...defaultOptions, ...(apiData ?? [])];
    // Remove duplicates by ID
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [people.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (person: Person) => {
      setSelection(person);
      setOpen(false);
      setQuery("");
      await onSelect?.(person);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: people.refetch,
      hasNextPage: people.data
        ? (pageNumber + 1) * pageSize < people.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: people.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      people.data,
      people.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
