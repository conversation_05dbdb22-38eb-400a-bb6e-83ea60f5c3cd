import { useCallback, useMemo, useState } from "react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type Document = NonNullable<
  RouterOutputs["documents"]["getMany"]["items"]
>[number];

export interface UseSelectDocumentProps {
  type?: string;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Document;
  defaultOptions?: Document[];
  defaultDebounce?: number;
  onSelect?: (document: Document) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectDocument({
  type,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectDocumentProps = {}) {
  const [selection, setSelection] = useState<Document | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const documents = api.documents.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      type,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = documents.isLoading;

  const data = useMemo(() => {
    const apiData = documents.data?.items ?? [];
    const combined = [...defaultOptions, ...apiData];
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [documents.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (document: Document) => {
      setSelection(document);
      setOpen(false);
      setQuery("");
      await onSelect?.(document);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      hasNextPage: documents.data
        ? (pageNumber + 1) * pageSize < documents.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: documents.data?.total,
      refetch: documents.refetch,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      documents.data,
      documents.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
