import { useCallback, useMemo, useState } from "react";

import type { PartialValue } from "@axa/ui/selectors/SelectValue";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import type { RouterOutputs } from "@/api";

import { ValueType } from "@/api";
import { api } from "@/api/client";

export type MedicalRole = NonNullable<
  RouterOutputs["values"]["getMany"]["items"]
>[number];

export interface MedicalRoleValue extends PartialValue {
  type: "MEDICAL_ROLE";
  value: string;
}

export interface UseSelectMedicalRoleProps {
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: MedicalRoleValue;
  defaultOptions?: MedicalRoleValue[];
  defaultDebounce?: number;
  organizationId?: string;
  onSelect?: (role: MedicalRoleValue) => void | Promise<void>;
  onValueChange?: (value: string) => void | Promise<void>;
}

export function useSelectMedicalRole({
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultOpen = false,
  defaultDebounce = 500,
  organizationId,
  onSelect,
  onValueChange,
}: UseSelectMedicalRoleProps = {}) {
  const [selection, setSelection] = useState<MedicalRoleValue | null>(
    defaultSelection ?? null,
  );
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);

  const values = api.values.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      type: ValueType.MEDICAL_ROLE,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = values.isLoading;

  const data = useMemo(() => {
    const apiData = (values.data?.items ?? []).map(
      (item): MedicalRoleValue =>
        ({
          ...item,
          type: ValueType.MEDICAL_ROLE,
        }) as MedicalRoleValue,
    );
    const combined = [...defaultOptions, ...apiData];
    // Remove duplicates by ID
    const uniqueData = combined.filter(
      (item, index, self) => index === self.findIndex((t) => t.id === item.id),
    );
    return uniqueData;
  }, [values.data?.items, defaultOptions]);

  const handleSelect = useCallback(
    async (role: MedicalRoleValue) => {
      setSelection(role);
      setOpen(false);
      setQuery("");
      await onSelect?.(role);
      await onValueChange?.("");
    },
    [setOpen, setQuery, onSelect, onValueChange],
  );

  const handleValueChange = useCallback(
    async (value: string) => {
      setQuery(value);
      await onValueChange?.(value);
    },
    [setQuery, onValueChange],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);
    },
    [setOpen],
  );

  return useMemo(
    () => ({
      data,
      loading,
      selection,
      open,
      query,
      setSelection: handleSelect,
      setQuery: handleValueChange,
      setOpen: handleOpenChange,
      refetch: values.refetch,
      hasNextPage: values.data
        ? (pageNumber + 1) * pageSize < values.data.total
        : false,
      hasPreviousPage: pageNumber > 0,
      totalCount: values.data?.total,
    }),
    [
      data,
      loading,
      selection,
      open,
      query,
      handleSelect,
      handleValueChange,
      handleOpenChange,
      values.data,
      values.refetch,
      pageNumber,
      pageSize,
    ],
  );
}
