"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import { SearchText } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { PayoutStatus } from "@/api";
import { PayoutMenu } from "@/components/actions/payout";
import PayoutStatusBadge from "@/components/common/PayoutStatus";
import PreviewProvider from "@/components/shared/PreviewProvider";

const i18n = {
  en: {
    noData: "There are no payouts yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search payouts...",
    },
    headers: {
      id: "ID",
      provider: "Provider",
      amount: "Base Amount",
      date: "Date Paid",
      overtimeAmount: "Overtime",
      holidayAmount: "Holiday",
      nightAmount: "Night",
      status: "Status",
    },
    filters: {
      status: "Status",
      options: {
        ALL: "All",
        [PayoutStatus.PROCESSING]: "Processing",
        [PayoutStatus.PENDING]: "Pending",
        [PayoutStatus.COMPLETED]: "Completed",
        [PayoutStatus.CANCELLED]: "Cancelled",
        [PayoutStatus.FAILED]: "Failed",
      },
    },
  },
  links: {
    payments: "/providers/app/earnings/[id]",
    payouts: "/app/billing/payouts/[id]",
    providers: "/app/providers/[id]",
    organizations: "/providers/app/organizations/[id]",
  },
};

export const groupName = "payouts";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      {
        value: PayoutStatus.PENDING,
        label: i18n.en.filters.options[PayoutStatus.PENDING],
      },
      {
        value: PayoutStatus.PROCESSING,
        label: i18n.en.filters.options[PayoutStatus.PROCESSING],
      },
      {
        value: PayoutStatus.COMPLETED,
        label: i18n.en.filters.options[PayoutStatus.COMPLETED],
      },
      {
        value: PayoutStatus.FAILED,
        label: i18n.en.filters.options[PayoutStatus.FAILED],
      },
      {
        value: PayoutStatus.CANCELLED,
        label: i18n.en.filters.options[PayoutStatus.CANCELLED],
      },
    ] satisfies { value: PayoutStatus | null; label: string }[],
  },
];

export type PayoutsQueryResult = RouterOutputs["billing"]["payouts"]["getMany"];
export type PayoutsType = PayoutsQueryResult["items"];
export type PayoutStructure = PayoutsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListPayoutsProps extends PropsWithChildren {
  loading?: boolean;
  payouts?: PayoutsQueryResult;
  filters?: React.ReactNode | React.ReactNode[];
  defaultPageSize?: number;
  defaultPageIndex?: number;
  mode?: "organization" | "provider";
}

export default function ListPayouts({
  loading = false,
  payouts,
  children,
  filters,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  mode = "organization",
}: ListPayoutsProps) {
  const data = useMemo(() => {
    if (!payouts) return undefined;
    return {
      items: payouts.items,
      total: payouts.total,
    };
  }, [payouts]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<PayoutStructure>(
            ["id", "status", "amount", "provider"],
            {
              filename: "payouts_export.csv",
              label: "Export Selected Payouts",
              resolvers: {
                provider: (provider) => {
                  if (
                    provider &&
                    typeof provider === "object" &&
                    "firstName" in provider &&
                    "lastName" in provider
                  ) {
                    return `${provider.firstName as string} ${provider.lastName as string}`;
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Payout Actions",
            render: (context: ActionContext<PayoutStructure>) => {
              if (context.type === "row") {
                return <PayoutMenu payout={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <PayoutStatusBadge status={row.original.status} />
                </div>
              ),
            },
            ...(mode === "organization"
              ? [
                  {
                    id: "provider",
                    accessorKey: "provider.id",
                    header: ({ column }) => (
                      <DataTableColumnHeader
                        column={column}
                        title={i18n.en.headers.provider}
                      />
                    ),
                    cell: ({ row }) =>
                      row.original.provider ? (
                        <div className="w-fit">
                          <Link
                            href={(mode === "organization"
                              ? i18n.links.payouts
                              : i18n.links.payments
                            ).replace("[id]", row.original.id)}
                          >
                            <PreviewProvider provider={row.original.provider} />
                          </Link>
                        </div>
                      ) : null,
                    enableHiding: false,
                  } as ColumnDef<PayoutStructure, PayoutStructure[]>,
                ]
              : [
                  {
                    id: "id",
                    accessorKey: "id",
                    header: ({ column }) => (
                      <DataTableColumnHeader
                        column={column}
                        title={i18n.en.headers.id}
                      />
                    ),
                    cell: ({ row }) => (
                      <Link
                        href={i18n.links.payments.replace(
                          "[id]",
                          row.original.id,
                        )}
                        className="font-semibold hover:text-primary"
                      >
                        {row.original.id}
                      </Link>
                    ),
                    enableHiding: false,
                  } as ColumnDef<PayoutStructure, PayoutStructure[]>,
                ]),
            {
              id: "amount",
              accessorKey: "amount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.amount}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  <span className="font-medium">
                    <Currency amount={row.original.amount} />
                  </span>
                </div>
              ),
            },
            {
              id: "overtimeAmount",
              accessorKey: "overtimeAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.overtimeAmount}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  {row.original.overtimeAmount ? (
                    <span className="font-medium">
                      <Currency amount={row.original.overtimeAmount} />
                    </span>
                  ) : (
                    <span className="text-sm text-muted-foreground">-</span>
                  )}
                </div>
              ),
            },
            {
              id: "holidayAmount",
              accessorKey: "holidayAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.holidayAmount}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  {row.original.holidayAmount ? (
                    <span className="font-medium">
                      <Currency amount={row.original.holidayAmount} />
                    </span>
                  ) : (
                    <span className="text-sm text-muted-foreground">-</span>
                  )}
                </div>
              ),
            },
            {
              id: "nightAmount",
              accessorKey: "nightAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.nightAmount}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  {row.original.nightAmount ? (
                    <span className="font-medium">
                      <Currency amount={row.original.nightAmount} />
                    </span>
                  ) : (
                    <span className="text-sm text-muted-foreground">-</span>
                  )}
                </div>
              ),
            },
            {
              id: "paidAt",
              accessorKey: "paidAt",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.date}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  <span className="font-medium">
                    {row.original.paidAt
                      ? new Date(row.original.paidAt).toLocaleDateString()
                      : "-"}
                  </span>
                </div>
              ),
            },
          ] as ColumnDef<PayoutStructure, PayoutStructure[]>[],
        [mode],
      )}
    >
      {children}
    </Table>
  );
}
