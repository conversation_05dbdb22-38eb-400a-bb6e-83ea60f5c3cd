"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";
import { format } from "date-fns";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { InvoiceStatus } from "@/api";
import { InvoiceMenu } from "@/components/actions/invoice";
import InvoiceStatusBadge from "@/components/common/InvoiceStatus";

const i18n = {
  en: {
    noData: "There are no invoices yet",
    title: "Invoices",
    description: "Invoices are a collection of time sheets for work orders.",
    invoiceTitle: "Invoice",
    noInvoices: "There are no invoices yet",
    selection: "Selection",
    actions: {
      search: "Search invoices...",
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      export: "Export Selected Invoices",
    },
    headers: {
      name: "Name",
      pending: "Pending",
      balance: "Balance",
      period: "Period",
      due: "Due Date",
      status: "Status",
      organization: "Organization",
    },
    filters: {
      status: "Status",
      options: {
        ALL: "All",
        [InvoiceStatus.DRAFT]: "Draft",
        [InvoiceStatus.OPEN]: "Open",
        [InvoiceStatus.PAID]: "Paid",
        [InvoiceStatus.DUE]: "Due",
        [InvoiceStatus.VOID]: "Void",
      },
    },
  },
  links: {
    invoices: "/app/billing/invoices/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "invoice";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      {
        value: InvoiceStatus.DRAFT,
        label: i18n.en.filters.options[InvoiceStatus.DRAFT],
      },
      {
        value: InvoiceStatus.OPEN,
        label: i18n.en.filters.options[InvoiceStatus.OPEN],
      },
      {
        value: InvoiceStatus.PAID,
        label: i18n.en.filters.options[InvoiceStatus.PAID],
      },
      {
        value: InvoiceStatus.DUE,
        label: i18n.en.filters.options[InvoiceStatus.DUE],
      },
      {
        value: InvoiceStatus.VOID,
        label: i18n.en.filters.options[InvoiceStatus.VOID],
      },
    ] satisfies { value: InvoiceStatus | null; label: string }[],
  },
];

export type InvoicesQueryResult =
  RouterOutputs["billing"]["invoices"]["getMany"];
export type InvoicesType = InvoicesQueryResult["items"];
export type InvoiceStructure = InvoicesType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export interface ListInvoicesProps extends PropsWithChildren {
  loading?: boolean;
  invoices?: InvoicesQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListInvoices({
  loading = false,
  invoices,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: ListInvoicesProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!invoices) return undefined;
    return {
      items: invoices.items,
      total: invoices.total,
    };
  }, [invoices]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<InvoiceStructure>(
            ["id", "number", "status", "total", "organization"],
            {
              filename: "invoices_export.csv",
              label: "Export Selected Invoices",
              resolvers: {
                number: (number, invoice) => invoice.number || `#${invoice.id}`,
                organization: (organization) => {
                  if (
                    organization &&
                    typeof organization === "object" &&
                    "name" in organization
                  ) {
                    return organization.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Invoice Actions",
            render: (context: ActionContext<InvoiceStructure>) => {
              if (context.type === "row") {
                return <InvoiceMenu invoice={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <InvoiceStatusBadge type={row.getValue("status")} />
              ),
            },
            {
              id: "id",
              accessorKey: "number",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => {
                return (
                  <div className="flex w-fit flex-col">
                    <Link
                      href={i18n.links.invoices.replace(
                        "[id]",
                        row.original.id,
                      )}
                      className="w-full font-semibold hover:text-primary"
                    >
                      #{row.getValue("id") ?? "000"}
                    </Link>
                  </div>
                );
              },
              enableHiding: false,
            },
            {
              id: "balance",
              accessorKey: "total",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.balance}
                />
              ),
              cell: ({ row }) => <Currency amount={row.getValue("balance")} />,
            },
            {
              id: "pending",
              accessorKey: "total",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.pending}
                />
              ),
              cell: ({ row }) => <Currency amount={row.getValue("pending")} />,
            },
            {
              id: "period",
              accessorKey: "date",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.period}
                />
              ),
              cell: ({ row }) => {
                const due = row.original.due;
                const date = new Date(
                  due.getTime() + due.getTimezoneOffset() * 60000,
                );

                return (
                  <p className="text-nowrap">{format(date, "LLLL yyyy")}</p>
                );
              },
            },
            {
              id: "due",
              accessorKey: "due",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.due}
                />
              ),
              cell: ({ row }) => {
                const due = row.original.due;
                const date = new Date(
                  due.getTime() + due.getTimezoneOffset() * 60000,
                );

                return <p className="text-nowrap">{format(date, "PPP")}</p>;
              },
            },
            {
              id: "organization",
              accessorKey: "organization.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.organizations.replace(
                    "[id]",
                    row.original.organization?.id ?? "",
                  )}
                  className="transition-colors hover:text-primary"
                >
                  <PreviewOrganization
                    size="sm"
                    organization={row.original.organization}
                  />
                </Link>
              ),
            },
          ] as ColumnDef<InvoiceStructure, InvoiceStructure[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
