"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { TableConfig } from "@axa/ui/tables/table";
import { SearchText } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import ShiftStatusBadge from "@/components/common/ShiftStatus";

const i18n = {
  en: {
    noData: "There are no shifts associated with this payout",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search shifts...",
    },
    headers: {
      id: "ID",
      summary: "Summary",
      scope: "Scope",
      startDate: "Start Date",
      endDate: "End Date",
      hours: "Hours",
      paymentRate: "Rate",
      paymentAmount: "Base Amount",
      overtimeAmount: "Overtime",
      holidayAmount: "Holiday",
      nightAmount: "Night",
      paymentTotal: "Total",
      status: "Status",
    },
  },
  links: {
    shifts: "/providers/app/shifts/[id]" as const,
  },
};

export const groupName = "payoutShifts";

export type ShiftStructure = NonNullable<
  NonNullable<RouterOutputs["billing"]["payouts"]["get"]>["shifts"]
>[number];
export type ShiftsStructure = ShiftStructure[];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListPayoutShiftsProps extends PropsWithChildren {
  loading?: boolean;
  shifts?: ShiftsStructure;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters: React.ReactNode | React.ReactNode[];
}

export default function ListPayoutShifts({
  loading = false,
  shifts,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: ListPayoutShiftsProps) {
  const data = useMemo(() => {
    if (!shifts) return undefined;
    return {
      items: shifts,
      total: shifts.length,
    };
  }, [shifts]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <div className="flex h-fit flex-1 gap-2 overflow-scroll p-1 px-2">
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </div>
      }
      actions={useMemo(
        () => [
          createTypedExportCSVAction<ShiftStructure>(
            ["id", "summary", "status", "hours", "paymentRate", "paymentTotal"],
            {
              filename: "payout_shifts_export.csv",
              label: "Export Selected Shifts",
            },
          ),
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <ShiftStatusBadge status={row.original.status} />
                </div>
              ),
            },
            {
              id: "summary",
              accessorKey: "summary",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.summary}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.shifts.replace("[id]", row.original.id)}
                  className="font-medium hover:underline"
                >
                  {row.original.summary}
                </Link>
              ),
              enableHiding: false,
            },
            {
              id: "scope",
              accessorKey: "scope",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.scope}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  <span>{row.original.scope}</span>
                </div>
              ),
            },
            {
              id: "dates",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.startDate}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  <span className="font-medium">
                    {new Date(row.original.startDate).toLocaleDateString()}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(row.original.endDate).toLocaleDateString()}
                  </span>
                </div>
              ),
            },
            {
              id: "hours",
              accessorKey: "hours",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.hours}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  <span className="font-medium">{row.original.hours} hrs</span>
                </div>
              ),
            },
            {
              id: "paymentRate",
              accessorKey: "paymentRate",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.paymentRate}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  <span className="font-medium">
                    <Currency amount={row.original.paymentRate} />
                  </span>
                </div>
              ),
            },
            {
              id: "paymentAmount",
              accessorKey: "paymentAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.paymentAmount}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  <span className="font-medium">
                    <Currency amount={row.original.paymentAmount} />
                  </span>
                </div>
              ),
            },
            {
              id: "overtimeAmount",
              accessorKey: "overtimeAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.overtimeAmount}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  {row.original.overtimeAmount ? (
                    <span className="font-medium">
                      <Currency amount={row.original.overtimeAmount} />
                    </span>
                  ) : (
                    <span className="text-sm text-muted-foreground">-</span>
                  )}
                </div>
              ),
            },
            {
              id: "holidayAmount",
              accessorKey: "holidayAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.holidayAmount}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  {row.original.holidayAmount ? (
                    <span className="font-medium">
                      <Currency amount={row.original.holidayAmount} />
                    </span>
                  ) : (
                    <span className="text-sm text-muted-foreground">-</span>
                  )}
                </div>
              ),
            },
            {
              id: "nightAmount",
              accessorKey: "nightAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.nightAmount}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  {row.original.nightAmount ? (
                    <span className="font-medium">
                      <Currency amount={row.original.nightAmount} />
                    </span>
                  ) : (
                    <span className="text-sm text-muted-foreground">-</span>
                  )}
                </div>
              ),
            },
            {
              id: "paymentTotal",
              accessorKey: "paymentTotal",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.paymentTotal}
                />
              ),
              cell: ({ row }) => (
                <div className="flex flex-col">
                  <span className="font-medium">
                    <Currency amount={row.original.paymentTotal} />
                  </span>
                </div>
              ),
            },
          ] as ColumnDef<ShiftStructure, ShiftStructure[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
