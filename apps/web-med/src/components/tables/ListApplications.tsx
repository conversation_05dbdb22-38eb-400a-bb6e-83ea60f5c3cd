"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { ApplicationStatus } from "@/api";
import { OrganizationApplicationMenu } from "@/components/actions/jobs/application";
import ApplicationStatusBadge from "@/components/common/ApplicationStatus";
import PreviewProvider from "@/components/shared/PreviewProvider";

import { SearchOrganization } from "../selectors/SelectOrganization";

const i18n = {
  en: {
    noData: "There are no applications yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search applications...",
    },
    headers: {
      provider: "Provider",
      job: "Job",
      status: "Status",
      createdAt: "Submitted",
      organization: "Organization",
      notes: "Notes",
    },
    filters: {
      status: "Status",
      options: {
        ALL: "All",
        [ApplicationStatus.PENDING]: "Pending",
        [ApplicationStatus.ACCEPTED]: "Accepted",
        [ApplicationStatus.REJECTED]: "Rejected",
        [ApplicationStatus.WITHDRAWN]: "Withdrawn",
      },
    },
  },
  links: {
    applications: "/app/applications/[id]",
    providers: "/app/providers/[id]",
    jobs: "/app/jobs/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "application";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      {
        value: ApplicationStatus.PENDING,
        label: i18n.en.filters.options[ApplicationStatus.PENDING],
      },
      {
        value: ApplicationStatus.ACCEPTED,
        label: i18n.en.filters.options[ApplicationStatus.ACCEPTED],
      },
      {
        value: ApplicationStatus.REJECTED,
        label: i18n.en.filters.options[ApplicationStatus.REJECTED],
      },
      {
        value: ApplicationStatus.WITHDRAWN,
        label: i18n.en.filters.options[ApplicationStatus.WITHDRAWN],
      },
    ] satisfies { value: ApplicationStatus | null; label: string }[],
  },
];

export type ApplicationsQueryResult = RouterOutputs["applications"]["getMany"];
export type ApplicationsType = ApplicationsQueryResult["items"];
export type ApplicationStructure = ApplicationsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListApplicationsProps extends PropsWithChildren {
  loading?: boolean;
  applications?: ApplicationsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListApplications({
  group = groupName,
  loading = false,
  applications,
  filters,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListApplicationsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!applications) return undefined;
    return {
      items: applications.items,
      total: applications.total,
    };
  }, [applications]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganization
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ApplicationStructure>(
            [
              "id",
              "status",
              "notes",
              "createdAt",
              "job",
              "provider",
              "organization",
            ],
            {
              filename: "applications_export.csv",
              label: "Export Selected Applications",
              resolvers: {
                job: (job) => {
                  if (job && typeof job === "object" && "summary" in job) {
                    return job.summary || "";
                  }
                  return "";
                },
                provider: (provider) => {
                  if (
                    provider &&
                    typeof provider === "object" &&
                    "firstName" in provider &&
                    "lastName" in provider
                  ) {
                    return `${provider.firstName as string} ${provider.lastName as string}`;
                  }
                  return "";
                },
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Application Actions",
            render: (context: ActionContext<ApplicationStructure>) => {
              if (context.type === "row") {
                return (
                  <OrganizationApplicationMenu
                    application={context.row}
                    variant="ghost"
                  />
                );
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <ApplicationStatusBadge status={row.original.status} />
                </div>
              ),
            },
            {
              id: "job",
              accessorKey: "job.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.job}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <Link
                    href={i18n.links.applications.replace(
                      "[id]",
                      row.original.id,
                    )}
                  >
                    <span className="text-sm text-muted-foreground">
                      {row.original.job?.summary}
                    </span>
                  </Link>
                </div>
              ),
            },
            {
              id: "notes",
              accessorKey: "notes",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.notes}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <span className="text-sm text-muted-foreground">
                    {row.original.notes}
                  </span>
                </div>
              ),
            },
            {
              id: "createdAt",
              accessorKey: "createdAt",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.createdAt}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <span className="text-sm text-muted-foreground">
                    {new Date(row.original.createdAt).toLocaleDateString()}
                  </span>
                </div>
              ),
            },
            {
              id: "provider",
              accessorKey: "provider.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => {
                const provider = row.original.provider;
                return provider ? (
                  <div className="w-fit">
                    <Link
                      href={i18n.links.providers.replace("[id]", provider.id)}
                    >
                      <PreviewProvider provider={provider} />
                    </Link>
                  </div>
                ) : null;
              },
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.organizations.replace(
                    "[id]",
                    row.original.organization?.id ?? "",
                  )}
                  className="transition-colors hover:text-primary"
                >
                  <PreviewOrganization
                    organization={row.original.organization}
                  />
                </Link>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<ApplicationStructure, ApplicationStructure[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
