"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Image from "next/image";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import ContactEmail from "@axa/ui/common/ContactEmail";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { PersonRole } from "@/api";
import { UserMenu } from "@/components/actions/user";

const i18n = {
  en: {
    noData: "There are no users yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search users...",
    },
    headers: {
      role: "Role",
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      organization: "Organization",
    },
    filters: {
      role: "Role",
      options: {
        ALL: "All",
        [PersonRole.ADMIN]: "Admin",
        [PersonRole.BILLING]: "Billing",
        [PersonRole.INTERNAL]: "Internal",
        [PersonRole.PROVIDER]: "Provider",
        [PersonRole.CLIENT]: "Client",
        [PersonRole.USER]: "User",
        [PersonRole.NONE]: "None",
      },
    },
  },
  links: {
    users: "/app/admin/users/[id]",
  },
};

export const groupName = "user";
const filterGroups = [
  {
    id: "role",
    label: i18n.en.filters.role,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.ALL,
      },
      {
        value: PersonRole.ADMIN,
        label: i18n.en.filters.options[PersonRole.ADMIN],
      },
      {
        value: PersonRole.BILLING,
        label: i18n.en.filters.options[PersonRole.BILLING],
      },
      {
        value: PersonRole.INTERNAL,
        label: i18n.en.filters.options[PersonRole.INTERNAL],
      },
      {
        value: PersonRole.PROVIDER,
        label: i18n.en.filters.options[PersonRole.PROVIDER],
      },
      {
        value: PersonRole.CLIENT,
        label: i18n.en.filters.options[PersonRole.CLIENT],
      },
      {
        value: PersonRole.USER,
        label: i18n.en.filters.options[PersonRole.USER],
      },
      {
        value: PersonRole.NONE,
        label: i18n.en.filters.options[PersonRole.NONE],
      },
    ] satisfies { value: PersonRole | null; label: string }[],
  },
];

export type UserQueryResult = RouterOutputs["user"]["getMany"];
export type UserType = UserQueryResult["items"];
export type PersonType = UserType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListUsersProps extends PropsWithChildren {
  loading?: boolean;
  users?: UserQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListUsers({
  group = groupName,
  loading = false,
  users,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: ListUsersProps) {
  const data = useMemo(() => {
    if (!users) return undefined;
    return {
      items: users.items,
      total: users.total,
    };
  }, [users]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<PersonType>(
            [
              "id",
              "firstName",
              "lastName",
              "email",
              "phone",
              "role",
              "organization",
            ],
            {
              filename: "users_export.csv",
              label: "Export Selected Users",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "User Actions",
            render: (context: ActionContext<PersonType>) => {
              if (context.type === "row") {
                return <UserMenu user={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "avatar",
              accessorKey: "avatar",
              header: () => null,
              cell: ({ row }) => (
                <Avatar className="size-10">
                  <AvatarImage
                    asChild
                    src={row.original.avatar ?? undefined}
                    alt={`${row.original.firstName} ${row.original.lastName}`}
                  >
                    <Image
                      src={row.original.avatar ?? ""}
                      alt={`${row.original.firstName} ${row.original.lastName}`}
                      width={40}
                      height={40}
                      layout="fixed"
                    />
                  </AvatarImage>
                  <AvatarFallback>
                    {row.original.firstName.charAt(0)}
                    {row.original.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              ),
            },
            {
              id: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-full items-center gap-2">
                  <ContactName
                    className="font-semibold"
                    link={i18n.links.users.replace("[id]", row.original.id)}
                    name={`${row.original.firstName} ${row.original.lastName}`}
                  />
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "email",
              accessorKey: "email",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.email}
                />
              ),
              cell: ({ row }) => <ContactEmail email={row.getValue("email")} />,
            },
            {
              id: "phone",
              accessorKey: "phone",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.phone}
                />
              ),
              cell: ({ row }) => <ContactPhone phone={row.getValue("phone")} />,
            },
            {
              id: "role",
              accessorKey: "role",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.role}
                />
              ),
              cell: ({ row }) => (
                <Badge variant="outline" className="w-fit text-nowrap text-xs">
                  {row.original.role}
                </Badge>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.role);
              },
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) =>
                row.original.organization ? (
                  <PreviewOrganization
                    organization={row.original.organization}
                  />
                ) : null,
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<PersonType, UserType>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
