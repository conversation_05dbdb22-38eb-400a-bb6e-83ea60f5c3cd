"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import { SearchText } from "@axa/ui/search";
import TimeAgo from "@axa/ui/shared/TimeAgo";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";
import type {
  DeleteSpecialtyProps,
  UpdateSpecialtyProps,
} from "@/components/actions/specialty";

import { SpecialtyMenu } from "@/components/actions/specialty";

const i18n = {
  en: {
    noData: "There are no specialties yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search specialties...",
    },
    headers: {
      name: "Name",
      description: "Description",
      providers: "Providers",
      jobs: "Jobs",
      shifts: "Shifts",
      experiences: "Experiences",
      createdAt: "Created At",
      updatedAt: "Updated At",
    },
  },
  links: {
    providers: "/app/providers",
    jobs: "/app/jobs",
    shifts: "/app/shifts",
    experiences: "/app/experiences",
  },
};

export const groupName = "specialties";

export type SpecialtiesQueryResult = RouterOutputs["specialties"]["getMany"];
export type SpecialtiesType = SpecialtiesQueryResult["items"];
export type SpecialtyType = SpecialtiesType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListSpecialtiesProps extends PropsWithChildren {
  loading?: boolean;
  specialties?: SpecialtiesQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  onUpdate?: UpdateSpecialtyProps["onUpdate"];
  onDelete?: DeleteSpecialtyProps["onDelete"];
}

export default function ListSpecialties({
  loading = false,
  specialties,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
  onUpdate,
  onDelete,
}: ListSpecialtiesProps) {
  const data = useMemo(() => {
    if (!specialties) return undefined;
    return {
      items: specialties.items,
      total: specialties.total,
    };
  }, [specialties]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <div className="flex h-fit flex-1 gap-2 overflow-scroll p-1 px-2">
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
        </div>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<SpecialtyType>(
            [
              "id",
              "name",
              "description",
              "providers",
              "jobs",
              "shifts",
              "experiences",
            ],
            {
              filename: "specialties_export.csv",
              label: "Export Selected Specialties",
              resolvers: {
                providers: (providers) => {
                  if (Array.isArray(providers)) {
                    return providers.length.toString();
                  }
                  return "0";
                },
                jobs: (jobs) => {
                  if (Array.isArray(jobs)) {
                    return jobs.length.toString();
                  }
                  return "0";
                },
                shifts: (shifts) => {
                  if (Array.isArray(shifts)) {
                    return shifts.length.toString();
                  }
                  return "0";
                },
                experiences: (experiences) => {
                  if (Array.isArray(experiences)) {
                    return experiences.length.toString();
                  }
                  return "0";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Specialty Actions",
            render: (context: ActionContext<SpecialtyType>) => {
              if (context.type === "row") {
                return (
                  <SpecialtyMenu
                    specialty={context.row}
                    onUpdate={onUpdate}
                    onDelete={onDelete}
                    variant="ghost"
                  />
                );
              }
              return null;
            },
          },
        ],
        [onUpdate, onDelete],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "name",
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit font-semibold">{row.original.name}</div>
              ),
              enableHiding: false,
            },
            {
              id: "description",
              accessorKey: "description",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.description}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit max-w-[300px] truncate">
                  {row.original.description}
                </div>
              ),
            },
            {
              id: "providers",
              accessorKey: "providers",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.providers}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  {row.original.providers?.length ?? 0} providers
                </div>
              ),
            },
            {
              id: "jobs",
              accessorKey: "jobs",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.jobs}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  {row.original.jobs?.length ?? 0} jobs
                </div>
              ),
            },
            {
              id: "shifts",
              accessorKey: "shifts",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.shifts}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  {row.original.shifts?.length ?? 0} shifts
                </div>
              ),
            },
            {
              id: "experiences",
              accessorKey: "experiences",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.experiences}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  {row.original.experiences?.length ?? 0} experiences
                </div>
              ),
            },
            {
              id: "createdAt",
              accessorKey: "createdAt",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.createdAt}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <TimeAgo date={row.original.createdAt} />
                </div>
              ),
            },
            {
              id: "updatedAt",
              accessorKey: "updatedAt",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.updatedAt}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <TimeAgo date={row.original.updatedAt} />
                </div>
              ),
            },
          ] as ColumnDef<SpecialtyType, SpecialtyType[]>[],
        [onUpdate, onDelete],
      )}
    >
      {children}
    </Table>
  );
}
