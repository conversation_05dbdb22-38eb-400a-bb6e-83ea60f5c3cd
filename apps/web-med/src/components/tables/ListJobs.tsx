"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewLocation from "@axa/ui/common/PreviewLocation";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { JobPostPriority, JobPostStatus, JobPostType } from "@/api";
import { JobPostMenu } from "@/components/actions/jobs/job";
import JobStatusBadge from "@/components/common/JobStatus";
import JobTypeBadge from "@/components/common/JobType";

import ProviderRole from "../common/ProviderRole";

const i18n = {
  en: {
    noData: "There are no jobs yet",
    selection: "Selection",
    noFacility: "No facility assigned",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search jobs...",
      add: "Add Job",
    },
    headers: {
      summary: "Summary",
      type: "Type",
      organization: "Organization",
      status: "Status",
      role: "Role",
      facility: "Facility",
      department: "Department",
      specialties: "Specialties",
    },
    filters: {
      status: "Status",
      type: "Type",
      priority: "Priority",
      options: {
        all: "All",
        [JobPostType.PERMANENT]: "Permanent",
        [JobPostType.TEMPORARY]: "Temporary",
        [JobPostType.PER_DIEM]: "Per Diem",
        [JobPostStatus.DRAFT]: "Draft",
        [JobPostStatus.PUBLISHED]: "Published",
        [JobPostStatus.FILLED]: "Filled",
        [JobPostStatus.COMPLETED]: "Completed",
        [JobPostStatus.CANCELLED]: "Cancelled",
        [JobPostStatus.EXPIRED]: "Expired",
        [JobPostPriority.LOW]: "Low",
        [JobPostPriority.MEDIUM]: "Medium",
        [JobPostPriority.HIGH]: "High",
      },
    },
  },
  links: {
    jobs: "/app/jobs/[id]",
    facilities: "/app/facilities/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "job";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "DRAFT",
        label: i18n.en.filters.options[JobPostStatus.DRAFT],
      },
      {
        value: "PUBLISHED",
        label: i18n.en.filters.options[JobPostStatus.PUBLISHED],
      },
      {
        value: "FILLED",
        label: i18n.en.filters.options[JobPostStatus.FILLED],
      },
      {
        value: "COMPLETED",
        label: i18n.en.filters.options[JobPostStatus.COMPLETED],
      },
      {
        value: "CANCELLED",
        label: i18n.en.filters.options[JobPostStatus.CANCELLED],
      },
      {
        value: "EXPIRED",
        label: i18n.en.filters.options[JobPostStatus.EXPIRED],
      },
    ] satisfies { value: JobType["status"] | null; label: string }[],
  },
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "PERMANENT",
        label: i18n.en.filters.options[JobPostType.PERMANENT],
      },
      {
        value: "TEMPORARY",
        label: i18n.en.filters.options[JobPostType.TEMPORARY],
      },
      {
        value: "PER_DIEM",
        label: i18n.en.filters.options[JobPostType.PER_DIEM],
      },
    ],
  },
  {
    id: "priority",
    label: i18n.en.filters.priority,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "LOW",
        label: i18n.en.filters.options[JobPostPriority.LOW],
      },
      {
        value: "MEDIUM",
        label: i18n.en.filters.options[JobPostPriority.MEDIUM],
      },
      {
        value: "HIGH",
        label: i18n.en.filters.options[JobPostPriority.HIGH],
      },
    ],
  },
];

export type JobsQueryResult = RouterOutputs["jobs"]["getMany"];
export type JobsType = JobsQueryResult["items"];
export type JobType = JobsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListJobsProps extends PropsWithChildren {
  loading?: boolean;
  jobs?: JobsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListJobs({
  group = groupName,
  loading = false,
  jobs,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: ListJobsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!jobs) return undefined;
    return {
      items: jobs.items,
      total: jobs.total,
    };
  }, [jobs]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<JobType>(
            [
              "id",
              "summary",
              "scope",
              "role",
              "type",
              "status",
              "organization",
            ],
            {
              filename: "jobs_export.csv",
              label: "Export Selected Jobs",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Job Actions",
            render: (context: ActionContext<JobType>) => {
              if (context.type === "row") {
                return <JobPostMenu job={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <JobStatusBadge status={row.original.status} />
              ),
            },
            {
              accessorKey: "summary",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.summary}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit max-w-[200px] flex-col truncate">
                  <Link
                    href={i18n.links.jobs.replace("[id]", row.original.id)}
                    className="w-full truncate text-nowrap font-semibold hover:text-primary"
                  >
                    {row.original.summary}
                  </Link>
                  <span className="line-clamp-2 text-ellipsis text-sm text-muted-foreground">
                    {row.original.scope}
                  </span>
                </div>
              ),
              enableHiding: false,
            },
            {
              accessorKey: "role",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.role}
                />
              ),
              cell: ({ row }) => <ProviderRole roleName={row.original.role} />,
            },
            {
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => <JobTypeBadge type={row.original.type} />,
            },
            {
              id: "facility",
              accessorKey: "facility.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.facility}
                />
              ),
              cell: ({ row }) => {
                const location = row.original.location as unknown as {
                  id: string;
                  name: string;
                  address: {
                    formatted: string;
                  };
                } | null;

                return location ? (
                  <span className="text-ellipsis text-sm text-muted-foreground">
                    <PreviewLocation
                      loading={loading}
                      location={location}
                      link={true}
                    />
                  </span>
                ) : (
                  <span className="text-ellipsis text-sm text-muted-foreground">
                    {i18n.en.noFacility}
                  </span>
                );
              },
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.organizations.replace(
                    "[id]",
                    row.original.organization?.id ?? "",
                  )}
                  className="transition-colors hover:text-primary"
                >
                  <PreviewOrganization
                    loading={loading}
                    organization={row.original.organization}
                  />
                </Link>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<JobType, JobType[]>[],
        [loading],
      )}
    >
      {children}
    </Table>
  );
}
