"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { OrganizationClass, OrganizationStatus, OrganizationType } from "@/api";
import { OrganizationMenu } from "@/components/actions/organization";
import OrganizationClassBadge from "@/components/common/OrganizationClass";
import OrganizationStatusBadge from "@/components/common/OrganizationStatus";
import OrganizationTypeBadge from "@/components/common/OrganizationType";

const i18n = {
  en: {
    noData: "There are no organizations yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search organizations...",
      add: "Add Organization",
    },
    headers: {
      name: "Name",
      type: "Type",
      class: "Class",
      status: "Status",
    },
    filters: {
      class: "Class",
      status: "Status",
      type: "Type",
      options: {
        all: "All",
        [OrganizationStatus.ACTIVE]: "Active",
        [OrganizationStatus.INACTIVE]: "Inactive",
        [OrganizationStatus.PENDING]: "Pending",
        [OrganizationStatus.REJECTED]: "Rejected",
        [OrganizationStatus.SUSPENDED]: "Suspended",
        [OrganizationClass.PRIVATE]: "Private",
        [OrganizationClass.GOVERNMENT]: "Government",
        [OrganizationClass.NONPROFIT]: "Non-Profit",
        [OrganizationType.INTERNAL]: "Internal",
        [OrganizationType.CLIENT]: "Client",
        [OrganizationType.ACCOUNT]: "Account",
      },
    },
  },
  links: {
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "organization";
const filterGroups = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "INTERNAL",
        label: i18n.en.filters.options[OrganizationType.INTERNAL],
      },
      {
        value: "CLIENT",
        label: i18n.en.filters.options[OrganizationType.CLIENT],
      },
      {
        value: "ACCOUNT",
        label: i18n.en.filters.options[OrganizationType.ACCOUNT],
      },
    ],
  },
  {
    id: "class",
    label: i18n.en.filters.class,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "PRIVATE",
        label: i18n.en.filters.options[OrganizationClass.PRIVATE],
      },
      {
        value: "GOVERNMENT",
        label: i18n.en.filters.options[OrganizationClass.GOVERNMENT],
      },
      {
        value: "NONPROFIT",
        label: i18n.en.filters.options[OrganizationClass.NONPROFIT],
      },
    ],
  },
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "ACTIVE",
        label: i18n.en.filters.options[OrganizationStatus.ACTIVE],
      },
      {
        value: "INACTIVE",
        label: i18n.en.filters.options[OrganizationStatus.INACTIVE],
      },
      {
        value: "PENDING",
        label: i18n.en.filters.options[OrganizationStatus.PENDING],
      },
      {
        value: "REJECTED",
        label: i18n.en.filters.options[OrganizationStatus.REJECTED],
      },
      {
        value: "SUSPENDED",
        label: i18n.en.filters.options[OrganizationStatus.SUSPENDED],
      },
    ],
  },
];

export type OrganizationsQueryResult =
  RouterOutputs["organizations"]["getMany"];
export type OrganizationsType = OrganizationsQueryResult["items"];
export type OrganizationStructure = OrganizationsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListOrganizationsProps extends PropsWithChildren {
  loading?: boolean;
  organizations?: OrganizationsQueryResult;
  group?: string;
  defaultPageIndex?: number;
  defaultPageSize?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListOrganizations({
  group = groupName,
  loading = false,
  organizations,
  defaultPageIndex = 0,
  defaultPageSize = 10,
  children,
  filters,
}: ListOrganizationsProps) {
  const data = useMemo(() => {
    if (!organizations) return undefined;
    return {
      items: organizations.items,
      total: organizations.total,
    };
  }, [organizations]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<OrganizationStructure>(
            ["id", "name", "status", "type", "class"],
            {
              filename: "organizations_export.csv",
              label: "Export Selected Organizations",
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Organization Actions",
            render: (context: ActionContext<OrganizationStructure>) => {
              if (context.type === "row") {
                return (
                  <OrganizationMenu
                    organization={context.row}
                    variant="ghost"
                  />
                );
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <Link
                    href={i18n.links.organizations.replace(
                      "[id]",
                      row.original.id,
                    )}
                    className="w-full text-nowrap font-semibold hover:text-primary"
                  >
                    <PreviewOrganization organization={row.original} />
                  </Link>
                </div>
              ),
              enableHiding: false,
            },
            {
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <OrganizationStatusBadge status={row.original.status} />
                </div>
              ),
            },
            {
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <OrganizationTypeBadge type={row.original.type} />
                </div>
              ),
            },
            {
              accessorKey: "class",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.class}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <OrganizationClassBadge class={row.original.class} />
                </div>
              ),
            },
          ] as ColumnDef<OrganizationStructure, OrganizationsType>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
