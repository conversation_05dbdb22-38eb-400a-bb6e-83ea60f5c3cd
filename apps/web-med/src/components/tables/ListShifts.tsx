"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewLocation from "@axa/ui/common/PreviewLocation";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { ShiftStatus } from "@/api";
import { ShiftMenu } from "@/components/actions/shift";
import { SearchOrganization } from "@/components/selectors/SelectOrganization";

import ShiftStatusBadge from "../common/ShiftStatus";
import PreviewProvider from "../shared/PreviewProvider";

const i18n = {
  en: {
    noData: "There are no shifts yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      searchOrgs: "Search by organization",
      search: "Search shifts",
      add: "Add Shift",
    },
    headers: {
      summary: "Summary",
      status: "Status",
      provider: "Provider",
      location: "Location",
      start: "Start",
      end: "End",
      organization: "Organization",
    },
    filters: {
      status: "Status",
      options: {
        all: "All",
        [ShiftStatus.PENDING]: "Pending",
        [ShiftStatus.CONFIRMED]: "Confirmed",
        [ShiftStatus.ACTIVE]: "Active",
        [ShiftStatus.COMPLETED]: "Completed",
        [ShiftStatus.CANCELLED]: "Cancelled",
      },
    },
  },
  links: {
    shifts: "/app/shifts/[id]",
  },
};

export const groupName = "shift";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "PENDING",
        label: i18n.en.filters.options[ShiftStatus.PENDING],
      },
      {
        value: "CONFIRMED",
        label: i18n.en.filters.options[ShiftStatus.CONFIRMED],
      },
      {
        value: "ACTIVE",
        label: i18n.en.filters.options[ShiftStatus.ACTIVE],
      },
      {
        value: "COMPLETED",
        label: i18n.en.filters.options[ShiftStatus.COMPLETED],
      },
      {
        value: "CANCELLED",
        label: i18n.en.filters.options[ShiftStatus.CANCELLED],
      },
    ],
  },
];

export type ShiftsQueryResult = RouterOutputs["shifts"]["getMany"];
export type ShiftsType = ShiftsQueryResult["items"];
export type ShiftType = ShiftsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListShiftsProps extends PropsWithChildren {
  loading?: boolean;
  shifts?: ShiftsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListShifts({
  group = groupName,
  loading = false,
  shifts,
  children,
  filters,
  defaultPageSize = 10,
  defaultPageIndex = 0,
}: ListShiftsProps) {
  const data = useMemo(() => {
    if (!shifts) return undefined;
    return {
      items: shifts.items,
      total: shifts.total,
    };
  }, [shifts]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganization
              size="sm"
              group={group}
              name="organization"
              loading={loading}
              placeholder={i18n.en.actions.searchOrgs}
            />
          </div>
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ShiftType>(
            [
              "id",
              "status",
              "summary",
              "scope",
              "startDate",
              "endDate",
              "provider",
              "location",
              "organization",
            ],
            {
              filename: "shifts_export.csv",
              label: "Export Selected Shifts",
              resolvers: {
                provider: (provider) => {
                  if (
                    provider &&
                    typeof provider === "object" &&
                    "firstName" in provider &&
                    "lastName" in provider
                  ) {
                    return `${provider.firstName as string} ${provider.lastName as string}`;
                  }
                  return "";
                },
                location: (location) => {
                  if (
                    location &&
                    typeof location === "object" &&
                    "name" in location
                  ) {
                    return location.name || "";
                  }
                  return "";
                },
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
                startDate: (date) => {
                  if (
                    date &&
                    (typeof date === "string" ||
                      typeof date === "number" ||
                      date instanceof Date)
                  ) {
                    return new Date(date).toLocaleDateString();
                  }
                  return "";
                },
                endDate: (date) => {
                  if (
                    date &&
                    (typeof date === "string" ||
                      typeof date === "number" ||
                      date instanceof Date)
                  ) {
                    return new Date(date).toLocaleDateString();
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Shift Actions",
            render: (context: ActionContext<ShiftType>) => {
              if (context.type === "row") {
                return <ShiftMenu shift={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <ShiftStatusBadge status={row.original.status} />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.status);
              },
            },
            {
              accessorKey: "summary",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.summary}
                />
              ),
              cell: ({ row }) => (
                <div className="flex max-w-[200px] flex-col">
                  <Link
                    href={i18n.links.shifts.replace("[id]", row.original.id)}
                    className="w-full truncate font-semibold hover:text-primary"
                  >
                    {row.original.summary}
                  </Link>
                  <span className="w-full truncate text-sm text-muted-foreground">
                    {row.original.scope}
                  </span>
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "provider",
              accessorKey: "provider.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => {
                const provider = row.original.provider;
                return <PreviewProvider provider={provider} />;
              },
            },
            {
              id: "location",
              accessorKey: "location.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.location}
                />
              ),
              cell: ({ row }) => (
                <PreviewLocation
                  location={
                    row.original.location as unknown as {
                      id: string;
                      name: string;
                      address: {
                        formatted: string;
                      };
                    }
                  }
                />
              ),
            },
            {
              id: "start",
              accessorKey: "start",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.start}
                />
              ),
              cell: ({ row }) => (
                <span>{row.original.startDate.toLocaleDateString()}</span>
              ),
            },
            {
              id: "end",
              accessorKey: "end",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.end}
                />
              ),
              cell: ({ row }) => (
                <span>{row.original.endDate.toLocaleDateString()}</span>
              ),
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <PreviewOrganization organization={row.original.organization} />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<ShiftType, ShiftType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
