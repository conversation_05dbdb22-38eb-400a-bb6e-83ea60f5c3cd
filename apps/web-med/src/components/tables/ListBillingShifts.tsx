"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import { Badge } from "@axa/ui/primitives/badge";
import { SearchText } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { ShiftStatus } from "@/api";
import { ShiftMenu } from "@/components/actions/shift";
import PreviewProvider from "@/components/shared/PreviewProvider";

const i18n = {
  en: {
    noData: "There are no shifts yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search shifts...",
    },
    headers: {
      summary: "Summary",
      status: "Status",
      provider: "Provider",
      start: "Start",
      end: "End",
      hours: "Hours",
      rate: "Rate",
      amount: "Amount",
      overtime: "Overtime",
      holiday: "Holiday",
      night: "Night",
      total: "Total",
    },
    filters: {
      status: "Status",
      options: {
        ALL: "All",
        [ShiftStatus.PENDING]: "Pending",
        [ShiftStatus.CONFIRMED]: "Confirmed",
        [ShiftStatus.ACTIVE]: "Active",
        [ShiftStatus.COMPLETED]: "Completed",
        CANCELLED: "Cancelled",
      },
    },
  },
  links: {
    shifts: "/app/shifts/[id]",
  },
};

export const groupName = "billing-shift";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.ALL,
      },
      {
        value: ShiftStatus.PENDING,
        label: i18n.en.filters.options[ShiftStatus.PENDING],
      },
      {
        value: ShiftStatus.CONFIRMED,
        label: i18n.en.filters.options[ShiftStatus.CONFIRMED],
      },
      {
        value: ShiftStatus.ACTIVE,
        label: i18n.en.filters.options[ShiftStatus.ACTIVE],
      },
      {
        value: ShiftStatus.COMPLETED,
        label: i18n.en.filters.options[ShiftStatus.COMPLETED],
      },
      {
        value: ShiftStatus.CANCELLED,
        label: i18n.en.filters.options[ShiftStatus.CANCELLED],
      },
    ],
  },
];

export type BillingShiftsQueryResult =
  RouterOutputs["billing"]["payouts"]["get"]["shifts"];
export type BillingShiftsType = NonNullable<BillingShiftsQueryResult>;
export type BillingShiftType = BillingShiftsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
  manualPagination: false,
};

interface ListBillingShiftsProps extends PropsWithChildren {
  loading?: boolean;
  shifts?: BillingShiftsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListBillingShifts({
  loading = false,
  shifts,
  children,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
}: ListBillingShiftsProps) {
  const data = useMemo(() => {
    if (!shifts) return undefined;
    return {
      items: shifts,
      total: shifts.length,
    };
  }, [shifts]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<BillingShiftType>(
            ["id", "summary", "status", "hours", "paymentRate", "paymentTotal"],
            {
              filename: "billing_shifts_export.csv",
              label: "Export Selected Shifts",
              resolvers: {
                provider: (provider) => {
                  if (
                    provider &&
                    typeof provider === "object" &&
                    "person" in provider
                  ) {
                    return `${provider.person.firstName} ${provider.person.lastName}`;
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Shift Actions",
            render: (context: ActionContext<BillingShiftType>) => {
              if (context.type === "row") {
                return <ShiftMenu shift={context.row as any} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              accessorKey: "summary",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.summary}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <Link
                    href={i18n.links.shifts.replace("[id]", row.original.id)}
                    className="w-full text-nowrap font-semibold hover:text-primary"
                  >
                    {row.original.summary}
                  </Link>
                  <span className="text-sm text-muted-foreground">
                    {row.original.scope}
                  </span>
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <Badge variant="outline">{row.original.status}</Badge>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.status);
              },
            },
            {
              id: "provider",
              accessorKey: "provider.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => (
                <PreviewProvider provider={row.original.provider} />
              ),
            },
            {
              id: "start",
              accessorKey: "startDate",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.start}
                />
              ),
              cell: ({ row }) => (
                <span>{row.original.startDate.toLocaleDateString()}</span>
              ),
            },
            {
              id: "end",
              accessorKey: "endDate",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.end}
                />
              ),
              cell: ({ row }) => (
                <span>{row.original.endDate.toLocaleDateString()}</span>
              ),
            },
            {
              id: "hours",
              accessorKey: "hours",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.hours}
                />
              ),
              cell: ({ row }) => <span>{row.original.hours.toFixed(1)}</span>,
            },
            {
              id: "rate",
              accessorKey: "paymentRate",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.rate}
                />
              ),
              cell: ({ row }) => <Currency amount={row.original.paymentRate} />,
            },
            {
              id: "amount",
              accessorKey: "paymentAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.amount}
                />
              ),
              cell: ({ row }) => (
                <Currency amount={row.original.paymentAmount} />
              ),
            },
            {
              id: "overtimeAmount",
              accessorKey: "overtimeAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.overtime}
                />
              ),
              cell: ({ row }) => (
                <Currency amount={row.original.overtimeAmount} />
              ),
            },
            {
              id: "holidayAmount",
              accessorKey: "holidayAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.holiday}
                />
              ),
              cell: ({ row }) => (
                <Currency amount={row.original.holidayAmount} />
              ),
            },
            {
              id: "nightAmount",
              accessorKey: "nightAmount",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.night}
                />
              ),
              cell: ({ row }) => <Currency amount={row.original.nightAmount} />,
            },
            {
              id: "paymentTotal",
              accessorKey: "paymentTotal",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.total}
                />
              ),
              cell: ({ row }) => (
                <Currency amount={row.original.paymentTotal} />
              ),
            },
          ] as ColumnDef<BillingShiftType, BillingShiftType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
