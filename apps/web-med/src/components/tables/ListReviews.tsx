"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";
import { Star } from "lucide-react";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewContact from "@axa/ui/common/PreviewContact";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { ReviewMenu } from "@/components/actions/review";

import PreviewProvider from "../shared/PreviewProvider";

const i18n = {
  en: {
    noData: "There are no reviews yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search reviews...",
    },
    headers: {
      provider: "Provider",
      rating: "Rating",
      comment: "Comment",
      reviewer: "Reviewer",
      shift: "Shift",
    },
    filters: {
      rating: "Rating",
      organizations: "Organizations",
      options: {
        all: "All",
        one: "⭐",
        two: "⭐⭐",
        three: "⭐⭐⭐",
        four: "⭐⭐⭐⭐",
        five: "⭐⭐⭐⭐⭐",
      },
    },
  },
  links: {
    providers: "/app/providers/[id]",
    people: "/app/people/[id]",
    shifts: "/app/shifts/[id]",
  },
};

export const groupName = "review";
const filterGroups = [
  {
    id: "rating",
    label: i18n.en.filters.rating,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "1",
        label: i18n.en.filters.options.one,
      },
      {
        value: "2",
        label: i18n.en.filters.options.two,
      },
      {
        value: "3",
        label: i18n.en.filters.options.three,
      },
      {
        value: "4",
        label: i18n.en.filters.options.four,
      },
      {
        value: "5",
        label: i18n.en.filters.options.five,
      },
    ],
  },
];

export type ReviewsQueryResult = RouterOutputs["reviews"]["getMany"];
export type ReviewsType = ReviewsQueryResult["items"];
export type ReviewType = ReviewsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListReviewsProps extends PropsWithChildren {
  loading?: boolean;
  reviews?: ReviewsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListReviews({
  group = groupName,
  loading = false,
  reviews,
  children,
  filters,
  defaultPageSize = 10,
  defaultPageIndex = 0,
}: ListReviewsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!reviews) return undefined;
    return {
      items: reviews.items,
      total: reviews.total,
    };
  }, [reviews]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ReviewType>(
            ["id", "rating", "comment", "provider", "reviewer", "shift"],
            {
              filename: "reviews_export.csv",
              label: "Export Selected Reviews",
              resolvers: {
                provider: (provider) => {
                  if (
                    provider &&
                    typeof provider === "object" &&
                    "firstName" in provider &&
                    "lastName" in provider
                  ) {
                    return `${provider.firstName} ${provider.lastName}`;
                  }
                  return "";
                },
                reviewer: (reviewer) => {
                  if (
                    reviewer &&
                    typeof reviewer === "object" &&
                    "firstName" in reviewer &&
                    "lastName" in reviewer
                  ) {
                    return `${reviewer.firstName} ${reviewer.lastName}`;
                  }
                  return "";
                },
                shift: (shift) => {
                  if (
                    shift &&
                    typeof shift === "object" &&
                    "summary" in shift
                  ) {
                    return shift.summary || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Review Actions",
            render: (context: ActionContext<ReviewType>) => {
              if (context.type === "row") {
                return <ReviewMenu review={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "rating",
              accessorKey: "rating",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.rating}
                />
              ),
              cell: ({ row }) => (
                <div className="mx-auto flex w-fit items-center justify-center gap-1">
                  <span className="font-medium">{row.original.rating}</span>
                  <Star className="size-4 fill-yellow-400 text-yellow-400" />
                </div>
              ),
            },
            {
              id: "comment",
              accessorKey: "comment",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.comment}
                />
              ),
              cell: ({ row }) => (
                // TODO: Add a tooltip to show the full comment or a popover to show the full comment
                // TODO: Add a button to copy the comment to the clipboard
                <div className="w-fit truncate">{row.original.comment}</div>
              ),
            },
            {
              id: "shift",
              accessorKey: "shift.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.shift}
                />
              ),
              cell: ({ row }) => {
                const shift = row.original.shift as unknown as {
                  id: string;
                  summary: string;
                } | null;
                return shift ? (
                  <Link
                    href={i18n.links.shifts.replace("[id]", shift.id)}
                    className="w-fit"
                  >
                    <span className="truncate text-sm text-muted-foreground">
                      {shift.summary}
                    </span>
                  </Link>
                ) : null;
              },
            },
            {
              id: "provider",
              accessorKey: "provider.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => {
                const provider = row.original.provider;
                return provider ? (
                  <Link
                    className="w-fit"
                    href={i18n.links.providers.replace("[id]", provider.id)}
                  >
                    <PreviewProvider provider={provider} />
                  </Link>
                ) : null;
              },
            },
            {
              id: "reviewer",
              accessorKey: "reviewer.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.reviewer}
                />
              ),
              cell: ({ row }) => {
                const reviewer = row.original.reviewer;
                return reviewer ? (
                  <div className="w-fit">
                    <PreviewContact
                      hideEmpty
                      className="h-fit items-center"
                      firstName={reviewer.firstName}
                      lastName={reviewer.lastName}
                      avatar={reviewer.avatar}
                      email={reviewer.email}
                      phone={reviewer.phone}
                      link={i18n.links.people.replace("[id]", row.original.id)}
                    />
                  </div>
                ) : null;
              },
            },
          ] as ColumnDef<ReviewType, ReviewType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
