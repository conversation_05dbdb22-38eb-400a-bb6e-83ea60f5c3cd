"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren, ReactNode } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import {
  IncidentSeverity,
  IncidentStatus,
  IncidentType as IncidentTypeEnum,
} from "@axa/database-medical";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import TimeAgo from "@axa/ui/shared/TimeAgo";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";
import type {
  DeleteIncidentProps,
  UpdateIncidentProps,
} from "@/components/actions/incident";

import { IncidentMenu } from "@/components/actions/incident";
import IncidentSeverityBadge from "@/components/common/IncidentSeverity";
import IncidentStatusBadge from "@/components/common/IncidentStatus";
import IncidentTypeBadge from "@/components/common/IncidentType";
import PreviewProvider from "@/components/shared/PreviewProvider";

const i18n = {
  en: {
    noData: "There are no incidents yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search incidents...",
    },
    headers: {
      summary: "Summary",
      severity: "Severity",
      status: "Status",
      type: "Type",
      provider: "Provider",
      organization: "Organization",
      shift: "Shift",
      createdAt: "Created At",
      updatedAt: "Updated At",
    },
    filters: {
      severity: "Severity",
      status: "Status",
      type: "Type",
      options: {
        severity: {
          ALL: "All",
          [IncidentSeverity.MINOR]: "Minor",
          [IncidentSeverity.MAJOR]: "Major",
          [IncidentSeverity.CRITICAL]: "Critical",
        },
        status: {
          ALL: "All",
          [IncidentStatus.OPEN]: "Open",
          [IncidentStatus.IN_PROGRESS]: "In Progress",
          [IncidentStatus.RESOLVED]: "Resolved",
        },
        type: {
          ALL: "All",
          [IncidentTypeEnum.SAFETY]: "Safety",
          [IncidentTypeEnum.HEALTH]: "Health",
          [IncidentTypeEnum.ENVIRONMENT]: "Environment",
          [IncidentTypeEnum.OTHER]: "Other",
        },
      },
    },
  },
  links: {
    incidents: "/app/incidents/[id]",
    organizations: "/app/organizations/[id]",
    providers: "/app/providers/[id]",
    shifts: "/app/shifts/[id]",
  },
};

export const groupName = "incidents";
const filterGroups = [
  {
    id: "severity",
    label: i18n.en.filters.severity,
    options: [
      { value: null, label: i18n.en.filters.options.severity.ALL },
      {
        value: IncidentSeverity.MINOR,
        label: i18n.en.filters.options.severity[IncidentSeverity.MINOR],
      },
      {
        value: IncidentSeverity.MAJOR,
        label: i18n.en.filters.options.severity[IncidentSeverity.MAJOR],
      },
      {
        value: IncidentSeverity.CRITICAL,
        label: i18n.en.filters.options.severity[IncidentSeverity.CRITICAL],
      },
    ],
  },
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.status.ALL },
      {
        value: IncidentStatus.OPEN,
        label: i18n.en.filters.options.status[IncidentStatus.OPEN],
      },
      {
        value: IncidentStatus.IN_PROGRESS,
        label: i18n.en.filters.options.status[IncidentStatus.IN_PROGRESS],
      },
      {
        value: IncidentStatus.RESOLVED,
        label: i18n.en.filters.options.status[IncidentStatus.RESOLVED],
      },
    ],
  },
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.type.ALL },
      {
        value: IncidentTypeEnum.SAFETY,
        label: i18n.en.filters.options.type[IncidentTypeEnum.SAFETY],
      },
      {
        value: IncidentTypeEnum.HEALTH,
        label: i18n.en.filters.options.type[IncidentTypeEnum.HEALTH],
      },
      {
        value: IncidentTypeEnum.ENVIRONMENT,
        label: i18n.en.filters.options.type[IncidentTypeEnum.ENVIRONMENT],
      },
      {
        value: IncidentTypeEnum.OTHER,
        label: i18n.en.filters.options.type[IncidentTypeEnum.OTHER],
      },
    ],
  },
];

export type IncidentsQueryResult = RouterOutputs["incidents"]["getMany"];
export type IncidentsType = IncidentsQueryResult["items"];
export type IncidentStructure = IncidentsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListIncidentsProps extends PropsWithChildren {
  loading?: boolean;
  incidents?: IncidentsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  onUpdate?: UpdateIncidentProps["onUpdate"];
  onDelete?: DeleteIncidentProps["onDelete"];
  filters?: ReactNode | ReactNode[];
}

export default function ListIncidents({
  loading = false,
  incidents,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  onUpdate,
  onDelete,
}: ListIncidentsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!incidents) return undefined;
    return {
      items: incidents.items,
      total: incidents.total,
    };
  }, [incidents]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<IncidentStructure>(
            [
              "id",
              "title",
              "description",
              "severity",
              "status",
              "type",
              "provider",
              "organization",
            ],
            {
              filename: "incidents_export.csv",
              label: "Export Selected Incidents",
              resolvers: {
                provider: (provider) => {
                  if (
                    provider &&
                    typeof provider === "object" &&
                    "firstName" in provider &&
                    "lastName" in provider
                  ) {
                    return `${provider.firstName as string} ${provider.lastName as string}`;
                  }
                  return "";
                },
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Incident Actions",
            render: (context: ActionContext<IncidentStructure>) => {
              if (context.type === "row") {
                return (
                  <IncidentMenu
                    incident={context.row}
                    onUpdate={onUpdate}
                    onDelete={onDelete}
                    variant="ghost"
                  />
                );
              }
              return null;
            },
          },
        ],
        [onUpdate, onDelete],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <IncidentStatusBadge type={row.original.status} />
                </div>
              ),
            },
            {
              id: "summary",
              accessorKey: "title",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.summary}
                />
              ),
              cell: ({ row }) => (
                <div className="flex max-w-[200px] flex-col">
                  <Link
                    href={i18n.links.incidents.replace("[id]", row.original.id)}
                    className="truncate font-medium transition-colors hover:text-primary"
                  >
                    {row.original.title}
                  </Link>
                  {row.original.description && (
                    <p className="mt-1 truncate text-xs text-muted-foreground">
                      {row.original.description}
                    </p>
                  )}
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "severity",
              accessorKey: "severity",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.severity}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <IncidentSeverityBadge type={row.original.severity} />
                </div>
              ),
            },
            {
              id: "type",
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <IncidentTypeBadge type={row.original.type} />
                </div>
              ),
            },
            {
              id: "provider",
              accessorKey: "provider",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) =>
                row.original.provider ? (
                  <div className="w-fit">
                    <Link
                      href={i18n.links.providers.replace(
                        "[id]",
                        row.original.provider.id,
                      )}
                      className="transition-colors hover:text-primary"
                    >
                      <PreviewProvider provider={row.original.provider} />
                    </Link>
                  </div>
                ) : null,
            },
            {
              id: "organization",
              accessorKey: "organization",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) =>
                row.original.organization ? (
                  <div className="w-fit">
                    <Link
                      href={i18n.links.organizations.replace(
                        "[id]",
                        row.original.organization.id,
                      )}
                      className="transition-colors hover:text-primary"
                    >
                      <PreviewOrganization
                        organization={row.original.organization}
                      />
                    </Link>
                  </div>
                ) : null,
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
            {
              id: "shift",
              accessorKey: "shift",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.shift}
                />
              ),
              cell: ({ row }) =>
                row.original.shift ? (
                  <div className="w-fit">
                    <Link
                      href={i18n.links.shifts.replace(
                        "[id]",
                        row.original.shift.id,
                      )}
                      className="transition-colors hover:text-primary"
                    >
                      {row.original.shift.summary}
                    </Link>
                  </div>
                ) : null,
            },
            {
              id: "createdAt",
              accessorKey: "createdAt",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.createdAt}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <TimeAgo date={row.original.createdAt} />
                </div>
              ),
            },
            {
              id: "updatedAt",
              accessorKey: "updatedAt",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.updatedAt}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <TimeAgo date={row.original.updatedAt} />
                </div>
              ),
            },
          ] as ColumnDef<IncidentStructure, IncidentStructure[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
