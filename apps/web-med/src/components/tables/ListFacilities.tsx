"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import LocationAddress from "@axa/ui/common/LocationAddress";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import TimeZone from "@axa/ui/shared/TimeZone";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { FacilityType } from "@/api";
import { FacilityMenu } from "@/components/actions/facility";
import FacilityTypeBadge from "@/components/common/FacilityType";

const i18n = {
  en: {
    noData: "There are no facilities yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search facilities...",
    },
    headers: {
      address: "Address",
      timeZone: "Time Zone",
      type: "Type",
      organization: "Organization",
    },
    filters: {
      type: "Type",
      options: {
        all: "All",
        [FacilityType.CAMPUS]: "Campus",
        [FacilityType.HOSPITAL]: "Hospital",
        [FacilityType.CLINIC]: "Clinic",
        [FacilityType.OFFICE]: "Office",
        [FacilityType.LAB]: "Laboratory",
        [FacilityType.PHARMACY]: "Pharmacy",
        [FacilityType.IMAGING]: "Imaging",
        [FacilityType.REHABILITATION]: "Rehabilitation",
        [FacilityType.OTHER]: "Other",
      },
    },
  },
  links: {
    facilities: "/app/facilities/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "facility";
const filterGroups = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "CAMPUS",
        label: i18n.en.filters.options[FacilityType.CAMPUS],
      },
      {
        value: "HOSPITAL",
        label: i18n.en.filters.options[FacilityType.HOSPITAL],
      },
      {
        value: "CLINIC",
        label: i18n.en.filters.options[FacilityType.CLINIC],
      },
      {
        value: "OFFICE",
        label: i18n.en.filters.options[FacilityType.OFFICE],
      },
      {
        value: "LAB",
        label: i18n.en.filters.options[FacilityType.LAB],
      },
      {
        value: "PHARMACY",
        label: i18n.en.filters.options[FacilityType.PHARMACY],
      },
      {
        value: "IMAGING",
        label: i18n.en.filters.options[FacilityType.IMAGING],
      },
      {
        value: "REHABILITATION",
        label: i18n.en.filters.options[FacilityType.REHABILITATION],
      },
      {
        value: "OTHER",
        label: i18n.en.filters.options[FacilityType.OTHER],
      },
    ],
  },
];

export type FacilitiesQueryResult = RouterOutputs["locations"]["getMany"];
export type FacilitiesType = FacilitiesQueryResult["items"];
export type FacilityStructure = FacilitiesType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListFacilitiesProps extends PropsWithChildren {
  loading?: boolean;
  facilities?: FacilitiesQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListFacilities({
  group = groupName,
  loading = false,
  facilities,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
  filters,
}: ListFacilitiesProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!facilities) return undefined;
    return {
      items: facilities.items,
      total: facilities.total,
    };
  }, [facilities]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<FacilityStructure>(
            ["id", "name", "type", "address", "organization"],
            {
              filename: "facilities_export.csv",
              label: "Export Selected Facilities",
              resolvers: {
                address: (address) => {
                  if (
                    address &&
                    typeof address === "object" &&
                    "formatted" in address
                  ) {
                    return address.formatted || "";
                  }
                  return "";
                },
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Facility Actions",
            render: (context: ActionContext<FacilityStructure>) => {
              if (context.type === "row") {
                return <FacilityMenu facility={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "address",
              accessorKey: "address.formatted",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.address}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <Link
                    href={i18n.links.facilities.replace(
                      "[id]",
                      row.original.id,
                    )}
                    className="font-semibold transition-colors hover:text-primary"
                  >
                    {row.original.name}
                  </Link>
                  <LocationAddress
                    address={row.getValue("address")}
                    className="text-sm text-muted-foreground"
                  />
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "type",
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => {
                const type = row.original.type;
                return <FacilityTypeBadge type={type} />;
              },
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              id: "timeZone",
              accessorKey: "timeZone",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.timeZone}
                />
              ),
              cell: ({ row }) => (
                <TimeZone
                  timeZone={
                    /* eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion, @typescript-eslint/no-unnecessary-condition */
                    ((row.original.address.timeZone ?? "") as string)
                      .replace("_", " ")
                      .replace("/", " | ")
                  }
                />
              ),
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.organizations.replace(
                    "[id]",
                    row.original.organization?.id ?? "",
                  )}
                  className="transition-colors hover:text-primary"
                >
                  <PreviewOrganization
                    size="sm"
                    organization={row.original.organization}
                  />
                </Link>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<FacilityStructure, FacilitiesType>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
