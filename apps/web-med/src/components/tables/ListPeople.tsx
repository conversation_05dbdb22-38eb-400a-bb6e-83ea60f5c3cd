"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Image from "next/image";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import ContactEmail from "@axa/ui/common/ContactEmail";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { PersonRole } from "@/api";
import { PersonMenu } from "@/components/actions/people";

const i18n = {
  en: {
    noData: "There are no people yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search people...",
    },
    headers: {
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      organization: "Organization",
    },
    filters: {
      role: "Role",
      options: {
        ALL: "All",
        [PersonRole.NONE]: "Contact",
        // USER: "User",
        [PersonRole.PROVIDER]: "Provider",
        [PersonRole.CLIENT]: "Client",
        // INTERNAL: "Internal",
        // BILLING: "Billing",
        // ADMIN: "Admin",
      },
    },
  },
  links: {
    people: "/app/people/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "people";
const filterGroups = [
  {
    id: "role",
    label: i18n.en.filters.role,
    options: Object.entries(i18n.en.filters.options).map(([value, label]) => ({
      value: value === "ALL" ? null : value,
      label,
    })),
  },
];

export type PeopleQueryResult = RouterOutputs["people"]["getMany"];
export type PeopleType = PeopleQueryResult["items"];
export type PersonStructure = PeopleType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListPeopleProps extends PropsWithChildren {
  loading?: boolean;
  people?: PeopleQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListPeople({
  group = groupName,
  loading = false,
  people,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: ListPeopleProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!people) return undefined;
    return {
      items: people.items,
      total: people.total,
    };
  }, [people]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<PersonStructure>(
            ["id", "firstName", "lastName", "email", "phone", "organization"],
            {
              filename: "people_export.csv",
              label: "Export Selected People",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Person Actions",
            render: (context: ActionContext<PersonStructure>) => {
              if (context.type === "row") {
                return (
                  <PersonMenu
                    person={context.row}
                    disabled={context.row.role === "NONE" ? false : true}
                    variant="ghost"
                  />
                );
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "avatar",
              accessorKey: "avatar",
              header: () => null,
              cell: ({ row }) => (
                <Avatar className="size-10">
                  <AvatarImage
                    asChild
                    src={row.original.avatar ?? undefined}
                    alt={`${row.original.firstName} ${row.original.lastName}`}
                  >
                    <Image
                      src={row.original.avatar ?? ""}
                      alt={`${row.original.firstName} ${row.original.lastName}`}
                      width={40}
                      height={40}
                      layout="fixed"
                    />
                  </AvatarImage>
                  <AvatarFallback>
                    {row.original.firstName.charAt(0)}
                    {row.original.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              ),
            },
            {
              id: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-full items-center gap-2">
                  <ContactName
                    className="font-semibold"
                    link={i18n.links.people.replace("[id]", row.original.id)}
                    name={`${row.original.firstName} ${row.original.lastName}`}
                  />
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "email",
              accessorKey: "email",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.email}
                />
              ),
              cell: ({ row }) => <ContactEmail email={row.getValue("email")} />,
            },
            {
              id: "phone",
              accessorKey: "phone",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.phone}
                />
              ),
              cell: ({ row }) => <ContactPhone phone={row.getValue("phone")} />,
            },
            {
              id: "organization",
              accessorKey: "organization.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.organizations.replace(
                    "[id]",
                    row.original.organization?.id ?? "",
                  )}
                >
                  <PreviewOrganization
                    size="sm"
                    organization={row.original.organization}
                  />
                </Link>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<PersonStructure, PersonStructure[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
