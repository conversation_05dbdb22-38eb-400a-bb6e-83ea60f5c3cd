"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { OfferStatus } from "@/api";
import { OrganizationOfferMenu } from "@/components/actions/jobs/offer";
import OfferStatusBadge from "@/components/common/OfferStatus";
import PreviewProvider from "@/components/shared/PreviewProvider";

const i18n = {
  en: {
    noData: "There are no offers yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search offers...",
    },
    headers: {
      provider: "Provider",
      job: "Job",
      status: "Status",
      notes: "Notes",
      expiresAt: "Expires",
      organization: "Organization",
      createdAt: "Submitted",
    },
    filters: {
      status: "Status",
      options: {
        ALL: "All",
        [OfferStatus.PENDING]: "Pending",
        [OfferStatus.ACCEPTED]: "Accepted",
        [OfferStatus.REJECTED]: "Rejected",
      },
    },
  },
  links: {
    offers: "/app/offers/[id]",
    providers: "/app/providers/[id]",
    jobs: "/app/jobs/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "offer";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: "PENDING", label: i18n.en.filters.options.PENDING },
      { value: "ACCEPTED", label: i18n.en.filters.options.ACCEPTED },
      { value: "REJECTED", label: i18n.en.filters.options.REJECTED },
    ],
  },
];

export type OffersQueryResult = RouterOutputs["offers"]["getMany"];
export type OffersType = OffersQueryResult["items"];
export type OfferType = OffersType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListOffersProps extends PropsWithChildren {
  loading?: boolean;
  offers?: OffersQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListOffers({
  group = groupName,
  loading = false,
  offers,
  children,
  filters,
  defaultPageSize = 10,
  defaultPageIndex = 0,
}: ListOffersProps) {
  const data = useMemo(() => {
    if (!offers) return undefined;
    return {
      items: offers.items,
      total: offers.total,
    };
  }, [offers]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<OfferType>(
            [
              "id",
              "status",
              "notes",
              "createdAt",
              "job",
              "provider",
              "organization",
            ],
            {
              filename: "offers_export.csv",
              label: "Export Selected Offers",
              resolvers: {
                job: (job) => {
                  if (job && typeof job === "object" && "summary" in job) {
                    return job.summary || "";
                  }
                  return "";
                },
                provider: (provider) => {
                  if (
                    provider &&
                    typeof provider === "object" &&
                    "firstName" in provider &&
                    "lastName" in provider
                  ) {
                    return `${provider.firstName as string} ${provider.lastName as string}`;
                  }
                  return "";
                },
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Offer Actions",
            render: (context: ActionContext<OfferType>) => {
              if (context.type === "row") {
                return (
                  <OrganizationOfferMenu offer={context.row} variant="ghost" />
                );
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <OfferStatusBadge status={row.original.status} />
                </div>
              ),
            },
            {
              id: "job",
              accessorKey: "job.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.job}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <Link
                    href={i18n.links.jobs.replace(
                      "[id]",
                      row.original.job?.id ?? "",
                    )}
                    className="font-semibold transition-colors hover:text-primary"
                  >
                    <span className="text-sm text-muted-foreground">
                      {row.original.job?.summary}
                    </span>
                  </Link>
                </div>
              ),
            },
            {
              id: "notes",
              accessorKey: "notes",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.notes}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <span className="text-sm text-muted-foreground">
                    {row.original.notes}
                  </span>
                </div>
              ),
            },
            {
              id: "createdAt",
              accessorKey: "createdAt",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.createdAt}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <span className="text-sm text-muted-foreground">
                    {new Date(row.original.createdAt).toLocaleDateString()}
                  </span>
                </div>
              ),
            },
            {
              id: "provider",
              accessorKey: "provider.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => {
                const provider = row.original.provider;
                return (
                  <div className="w-fit">
                    <Link
                      href={i18n.links.providers.replace(
                        "[id]",
                        provider?.id ?? "",
                      )}
                      className="font-semibold transition-colors hover:text-primary"
                    >
                      <PreviewProvider provider={provider} />
                    </Link>
                  </div>
                );
              },
              enableHiding: false,
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.organizations.replace(
                    "[id]",
                    row.original.organization?.id ?? "",
                  )}
                  className="transition-colors hover:text-primary"
                >
                  <PreviewOrganization
                    organization={row.original.organization}
                  />
                </Link>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<OfferType, OfferType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
