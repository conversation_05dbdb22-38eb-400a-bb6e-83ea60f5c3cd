"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import TimeAgo from "@axa/ui/shared/TimeAgo";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";
import type {
  DeleteValueProps,
  UpdateValueProps,
} from "@/components/actions/value";

import { ValueType } from "@/api";
import { ValueMenu } from "@/components/actions/value";
import ValueTypeBadge from "@/components/common/ValueType";

const i18n = {
  en: {
    noData: "There are no values yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search values...",
    },
    headers: {
      title: "Title",
      type: "Type",
      value: "Value",
      organization: "Organization",
      createdAt: "Created At",
      updatedAt: "Updated At",
    },
    filters: {
      type: "Type",
      options: {
        ALL: "All",
        [ValueType.TAG]: "Tag",
        [ValueType.ACTION]: "Action",
        [ValueType.MEDICAL_ROLE]: "Medical Role",
        [ValueType.CONTACT]: "Contact",
        [ValueType.SPECIALTY]: "Specialty",
      },
    },
  },
  links: {
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "values";
const filterGroups = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: ValueType.TAG, label: i18n.en.filters.options[ValueType.TAG] },
      {
        value: ValueType.ACTION,
        label: i18n.en.filters.options[ValueType.ACTION],
      },
      {
        value: ValueType.MEDICAL_ROLE,
        label: i18n.en.filters.options[ValueType.MEDICAL_ROLE],
      },
      {
        value: ValueType.CONTACT,
        label: i18n.en.filters.options[ValueType.CONTACT],
      },
      {
        value: ValueType.SPECIALTY,
        label: i18n.en.filters.options[ValueType.SPECIALTY],
      },
    ] satisfies { value: ValueType | null; label: string }[],
  },
];

export type ValuesQueryResult = RouterOutputs["values"]["getMany"];
export type ValuesType = ValuesQueryResult["items"];
export type ValueStructure = ValuesType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListValuesProps extends PropsWithChildren {
  loading?: boolean;
  values?: ValuesQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  onUpdate?: UpdateValueProps["onUpdate"];
  onDelete?: DeleteValueProps["onDelete"];
  showOrganization?: boolean;
}

export default function ListValues({
  loading = false,
  values,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
  filters,
  onUpdate,
  onDelete,
  showOrganization = false,
}: ListValuesProps) {
  const data = useMemo(() => {
    if (!values) return undefined;
    return {
      items: values.items,
      total: values.total,
    };
  }, [values]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ValueStructure>(
            ["id", "value", "type", "organization"],
            {
              filename: "values_export.csv",
              label: "Export Selected Values",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Value Actions",
            render: (context: ActionContext<ValueStructure>) => {
              if (context.type === "row") {
                return (
                  <ValueMenu
                    // @ts-expect-error TODO: fix this
                    value={{
                      id: context.row.id,
                      value: context.row.value,
                      type: context.row.type,
                      organization: {
                        id: context.row.organization?.id,
                      },
                    }}
                    variant="ghost"
                    onUpdate={onUpdate}
                    onDelete={onDelete}
                  />
                );
              }
              return null;
            },
          },
        ],
        [onUpdate, onDelete],
      )}
      columns={useMemo(() => {
        const columns: ColumnDef<ValueStructure, ValueStructure[]>[] = [
          {
            id: "value",
            accessorKey: "value",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.value}
              />
            ),
            cell: ({ row }) => (
              <div className="w-fit font-semibold">{row.original.value}</div>
            ),
            enableHiding: false,
          },
          {
            id: "type",
            accessorKey: "type",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.type}
              />
            ),
            cell: ({ row }) => (
              <div className="flex w-fit flex-col">
                <ValueTypeBadge type={row.original.type} />
              </div>
            ),
          },
          {
            id: "createdAt",
            accessorKey: "createdAt",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.createdAt}
              />
            ),
            cell: ({ row }) => (
              <div className="flex w-fit flex-col">
                <TimeAgo date={row.original.createdAt} />
              </div>
            ),
          },
          {
            id: "updatedAt",
            accessorKey: "updatedAt",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.updatedAt}
              />
            ),
            cell: ({ row }) => (
              <div className="flex w-fit flex-col">
                <TimeAgo date={row.original.updatedAt} />
              </div>
            ),
          },
        ];

        if (showOrganization) {
          columns.push({
            id: "organization",
            accessorKey: "organization.name",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.organization}
              />
            ),
            cell: ({ row }) => {
              const organization = row.original.organization;
              return organization ? (
                <div className="w-fit">
                  <Link
                    href={i18n.links.organizations.replace(
                      "[id]",
                      organization.id,
                    )}
                    className="transition-colors hover:text-primary"
                  >
                    <PreviewOrganization organization={organization} />
                  </Link>
                </div>
              ) : null;
            },
            filterFn: (row, id, value: string) => {
              return value.includes(row.original.organization?.id ?? "");
            },
          });
        }

        return columns;
      }, [showOrganization])}
    >
      {children}
    </Table>
  );
}
