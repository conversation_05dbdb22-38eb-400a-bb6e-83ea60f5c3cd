"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { TableConfig } from "@axa/ui/tables/table";
import DocumentViewer from "@axa/ui/common/DocumentViewer";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

// import { DocumentMenu } from "@/components/actions/document";

const i18n = {
  en: {
    noData: "There are no documents yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search documents...",
    },
    headers: {
      name: "Name",
      type: "Type",
      organization: "Organization",
    },
    filters: {
      type: "Type",
      options: {
        ALL: "All",
        PDF: "PDF",
        IMAGE: "Image",
      },
    },
  },
  links: {
    documents: "/app/documents/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "documents";
const filterGroups = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: Object.entries(i18n.en.filters.options).map(([value, label]) => ({
      value: value === "ALL" ? null : value,
      label,
    })),
  },
];

export type DocumentsQueryResult = RouterOutputs["documents"]["getMany"];
export type DocumentsType = DocumentsQueryResult["items"];
export type DocumentStructure = DocumentsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListDocumentsProps extends PropsWithChildren {
  loading?: boolean;
  documents?: DocumentsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListDocuments({
  loading = false,
  documents,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: ListDocumentsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!documents) return undefined;
    return {
      items: documents.items,
      total: documents.total,
    };
  }, [documents]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<DocumentStructure>(
            ["id", "name", "type", "organization"],
            {
              filename: "documents_export.csv",
              label: "Export Selected Documents",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows) - commented out as DocumentMenu is not active
          // {
          //   type: "row",
          //   label: "Document Actions",
          //   render: (context: ActionContext<DocumentType>) => {
          //     if (context.type === "row") {
          //       return (
          //         <DocumentMenu
          //           document={context.row}
          //           variant="ghost"
          //         />
          //       );
          //     }
          //     return null;
          //   },
          // },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "preview",
              accessorKey: "preview",
              header: () => null,
              cell: ({ row }) => (
                <DocumentViewer
                  document={{
                    id: row.original.id,
                    name: row.original.name,
                    type: row.original.type,
                    url: `/api/assets/${row.original.url}`,
                  }}
                />
              ),
            },
            {
              id: "name",
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={`/app/documents/${row.original.id}`}
                  className="h-fit"
                >
                  <p className="font-semibold">{row.getValue("name")}</p>
                </Link>
              ),
              enableHiding: false,
            },
            {
              id: "type",
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => <p>{row.getValue("type")}</p>,
            },
            {
              id: "organization",
              accessorKey: "organization.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.organizations.replace(
                    "[id]",
                    row.original.organization?.id ?? "",
                  )}
                  className="transition-colors hover:text-primary"
                >
                  <PreviewOrganization
                    size="sm"
                    organization={row.original.organization}
                  />
                </Link>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<DocumentStructure, DocumentStructure[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
