"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import ContactEmail from "@axa/ui/common/ContactEmail";
import ContactPhone from "@axa/ui/common/ContactPhone";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { ProviderStatus } from "@/api";
import { ProviderMenu } from "@/components/actions/provider";
import ProviderStatusBadge from "@/components/common/ProviderStatus";

import PreviewProvider from "../shared/PreviewProvider";

const i18n = {
  en: {
    noData: "There are no providers yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search providers...",
    },
    headers: {
      provider: "Provider",
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      status: "Status",
    },
    filters: {
      status: "Status",
      options: {
        all: "All",
        [ProviderStatus.PENDING]: "Pending",
        [ProviderStatus.ACTIVE]: "Active",
        [ProviderStatus.INACTIVE]: "Inactive",
        [ProviderStatus.REJECTED]: "Rejected",
        [ProviderStatus.SUSPENDED]: "Suspended",
      },
    },
  },
  links: {
    providers: "/app/providers/[id]",
  },
};

export const groupName = "provider";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.all },
      {
        value: ProviderStatus.PENDING,
        label: i18n.en.filters.options[ProviderStatus.PENDING],
      },
      {
        value: ProviderStatus.ACTIVE,
        label: i18n.en.filters.options[ProviderStatus.ACTIVE],
      },
      {
        value: ProviderStatus.REJECTED,
        label: i18n.en.filters.options[ProviderStatus.REJECTED],
      },
      {
        value: ProviderStatus.SUSPENDED,
        label: i18n.en.filters.options[ProviderStatus.SUSPENDED],
      },
      {
        value: ProviderStatus.INACTIVE,
        label: i18n.en.filters.options[ProviderStatus.INACTIVE],
      },
    ] satisfies { value: ProviderStatus | null; label: string }[],
  },
];

export type ProvidersQueryResult = RouterOutputs["providers"]["getMany"];
export type ProvidersType = ProvidersQueryResult["items"];
export type ProviderStructure = ProvidersType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListProvidersProps extends PropsWithChildren {
  loading?: boolean;
  providers?: ProvidersQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListProviders({
  group = groupName,
  loading = false,
  providers,
  children,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
}: ListProvidersProps) {
  const data = useMemo(() => {
    if (!providers) return undefined;
    return {
      items: providers.items,
      total: providers.total,
    };
  }, [providers]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ProviderStructure>(
            ["id", "status", "person"],
            {
              filename: "providers_export.csv",
              label: "Export Selected Providers",
              resolvers: {
                person: (person) => {
                  if (
                    person &&
                    typeof person === "object" &&
                    "email" in person
                  ) {
                    return `${person.firstName || ""} ${person.lastName || ""} (${person.email ?? ""})`;
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Provider Actions",
            render: (context: ActionContext<ProviderStructure>) => {
              if (context.type === "row") {
                return <ProviderMenu provider={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "provider",
              accessorKey: "id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={i18n.links.providers.replace("[id]", row.original.id)}
                >
                  <PreviewProvider loading={loading} provider={row.original} />
                </Link>
              ),
            },
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <ProviderStatusBadge status={row.getValue("status")} />
                </div>
              ),
            },
            {
              id: "email",
              accessorKey: "person.email",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.email}
                />
              ),
              cell: ({ row }) => (
                <ContactEmail email={row.original.person?.email ?? ""} />
              ),
            },
            {
              id: "phone",
              accessorKey: "person.phone",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.phone}
                />
              ),
              cell: ({ row }) => (
                <ContactPhone phone={row.original.person?.phone ?? ""} />
              ),
            },
          ] as ColumnDef<ProviderStructure, ProviderStructure[]>[],
        [loading],
      )}
    >
      {children}
    </Table>
  );
}
