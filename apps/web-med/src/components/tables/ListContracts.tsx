"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { ContractStatus, ContractType } from "@/api";
import { ContractMenu } from "@/components/actions/contracts/contract";
import ContractStatusBadge from "@/components/common/ContractStatus";
import ContractTypeBadge from "@/components/common/ContractType";
import PreviewProvider from "@/components/shared/PreviewProvider";

const i18n = {
  en: {
    noData: "There are no contracts yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search contracts...",
    },
    headers: {
      title: "Title",
      type: "Type",
      provider: "Provider",
      organization: "Organization",
      status: "Status",
    },
    filters: {
      status: "Status",
      type: "Type",
      options: {
        ALL: "All",
        [ContractStatus.PENDING]: "Pending",
        [ContractStatus.REJECTED]: "Rejected",
        [ContractStatus.SIGNED]: "Signed",
        [ContractType.EMPLOYMENT]: "Employment",
        [ContractType.NON_COMPETE]: "Non-Compete",
        [ContractType.NON_DISCLOSURE]: "Non-Disclosure",
        [ContractType.SERVICE_RATE]: "Service Rate",
        [ContractType.SERVICE_AGREEMENT]: "Service Agreement",
        [ContractType.OTHER]: "Other",
      },
    },
  },
  links: {
    contracts: "/app/contracts/[id]",
    providers: "/app/providers/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "contract";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.ALL,
      },
      {
        value: ContractStatus.PENDING,
        label: i18n.en.filters.options.PENDING,
      },
      {
        value: ContractStatus.REJECTED,
        label: i18n.en.filters.options.REJECTED,
      },
      {
        value: ContractStatus.SIGNED,
        label: i18n.en.filters.options.SIGNED,
      },
    ] satisfies { value: ContractStatus | null; label: string }[],
  },
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      {
        value: ContractType.EMPLOYMENT,
        label: i18n.en.filters.options.EMPLOYMENT,
      },
      {
        value: ContractType.NON_COMPETE,
        label: i18n.en.filters.options.NON_COMPETE,
      },
      {
        value: ContractType.NON_DISCLOSURE,
        label: i18n.en.filters.options.NON_DISCLOSURE,
      },
      {
        value: ContractType.SERVICE_RATE,
        label: i18n.en.filters.options.SERVICE_RATE,
      },
      {
        value: ContractType.SERVICE_AGREEMENT,
        label: i18n.en.filters.options.SERVICE_AGREEMENT,
      },
      {
        value: ContractType.OTHER,
        label: i18n.en.filters.options.OTHER,
      },
    ] satisfies { value: ContractType | null; label: string }[],
  },
];

export type ContractsQueryResult = RouterOutputs["contracts"]["getMany"];
export type ContractsType = ContractsQueryResult["items"];
export type ContractStructure = ContractsType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListContractsProps extends PropsWithChildren {
  loading?: boolean;
  contracts?: ContractsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}

export default function ListContracts({
  group = groupName,
  loading = false,
  contracts,
  children,
  filters,
  defaultPageSize = 10,
  defaultPageIndex = 0,
}: ListContractsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!contracts) return undefined;
    return {
      items: contracts.items,
      total: contracts.total,
    };
  }, [contracts]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ContractStructure>(
            ["id", "title", "type", "status", "provider", "organization"],
            {
              filename: "contracts_export.csv",
              label: "Export Selected Contracts",
              resolvers: {
                provider: (provider) => {
                  if (
                    provider &&
                    typeof provider === "object" &&
                    "firstName" in provider &&
                    "lastName" in provider
                  ) {
                    return `${provider.firstName} ${provider.lastName}` || "";
                  }
                  return "";
                },
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Contract Actions",
            render: (context: ActionContext<ContractStructure>) => {
              if (context.type === "row") {
                return <ContractMenu contract={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <ContractStatusBadge status={row.original.status} />
                </div>
              ),
            },
            {
              id: "title",
              accessorKey: "title",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.title}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <Link
                    href={i18n.links.contracts.replace("[id]", row.original.id)}
                    className="font-semibold transition-colors hover:text-primary"
                  >
                    {row.original.title}
                  </Link>
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "type",
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <ContractTypeBadge type={row.original.type} />
                </div>
              ),
            },
            {
              id: "provider",
              accessorKey: "provider.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => {
                const provider = row.original.provider;
                return provider ? (
                  <div className="w-fit">
                    <Link
                      href={i18n.links.providers.replace("[id]", provider.id)}
                      className="transition-colors hover:text-primary"
                    >
                      <PreviewProvider provider={provider} />
                    </Link>
                  </div>
                ) : null;
              },
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => {
                const organization = row.original.organization;
                return organization ? (
                  <div className="w-fit">
                    <Link
                      href={i18n.links.organizations.replace(
                        "[id]",
                        organization.id,
                      )}
                      className="transition-colors hover:text-primary"
                    >
                      <PreviewOrganization organization={organization} />
                    </Link>
                  </div>
                ) : null;
              },
            },
          ] as ColumnDef<ContractStructure, ContractStructure[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
