"use client";

import { useCallback } from "react";

import type {
  SearchLocationProps as SearchLocationBaseProps,
  SelectLocationProps as SelectLocationBaseProps,
  SelectLocationFieldProps as SelectLocationFieldBaseProps,
} from "@axa/ui/selectors/SelectLocation";
import {
  SearchLocation as SearchLocationBase,
  SelectLocation as SelectLocationBase,
  SelectLocationField as SelectLocationFieldBase,
} from "@axa/ui/selectors/SelectLocation";

import type { Facility } from "@/hooks/selectors/use-select-facility";

import { useSelectFacility } from "@/hooks/selectors/use-select-facility";

export type { Facility };
export type FacilityStructure = Facility;

const i18n = {
  en: {
    label: "Facility",
    description: "Select a facility",
    placeholder: "Select a facility",
  },
};

interface FacilityAsLocation {
  id: string;
  name: string;
  address: {
    formatted: string | null;
    street?: string | null;
    city?: string | null;
    state?: string | null;
    postal?: string | null;
    country?: string | null;
  };
  [key: string]: unknown;
}

export interface SelectFacilityProps
  extends Omit<
    SelectLocationBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  defaultQuery?: string;
  defaultSelection?: Facility;
  defaultDebounce?: number;
  pageSize?: number;
  pageNumber?: number;
  onSelect?: (facility: Facility) => void | Promise<void>;
}

export function SelectFacility({
  loading = false,
  enabled = true,
  organizationId,
  defaultQuery = "",
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  onSelect,
  ...props
}: SelectFacilityProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectFacility({
    enabled,
    organizationId,
    defaultQuery,
    defaultSelection,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectLocationBase<FacilityAsLocation>
      {...props}
      data={data}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection}
      onValueChange={setQuery}
      onSelect={useCallback(
        async (facilityAsLocation: FacilityAsLocation) => {
          // Find the original facility from the hook data
          const originalFacility = data.find(
            (f) => f.id === facilityAsLocation.id,
          );
          if (originalFacility) {
            await setSelection(originalFacility);
          }
        },
        [setSelection, data],
      )}
    />
  );
}

export interface SelectFacilityFieldProps
  extends Omit<
    SelectLocationFieldBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  pageSize?: number;
  name?: string;
  label?: string;
  description?: string;
  showLabel?: boolean;
  showDescription?: boolean;
  onSelect?: (facility: Facility) => void | Promise<void>;
}

export function SelectFacilityField({
  enabled = true,
  loading,
  organizationId,
  pageSize = 5,
  name = "facilityId",
  label = i18n.en.label,
  description = i18n.en.description,
  showLabel = true,
  showDescription = true,
  onSelect,
  ...props
}: SelectFacilityFieldProps) {
  const { data, loading: hookLoading } = useSelectFacility({
    enabled,
    organizationId,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectLocationFieldBase<FacilityAsLocation>
      {...props}
      name={name}
      label={label}
      description={description}
      showLabel={showLabel}
      showDescription={showDescription}
      data={data}
      loading={isLoading}
      onSelect={async (facilityAsLocation: FacilityAsLocation) => {
        // Find the original facility from the hook data
        const originalFacility = data.find(
          (f) => f.id === facilityAsLocation.id,
        );
        if (originalFacility && onSelect) {
          await onSelect(originalFacility);
        }
      }}
    />
  );
}

export interface SearchFacilityProps
  extends Omit<
    SearchLocationBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  enabled?: boolean;
  organizationId?: string;
  pageSize?: number;
  group: string;
  name?: string;
  defaultValue?: string;
  onSelect?: (facility: Facility) => void | Promise<void>;
}

export function SearchFacility({
  enabled = true,
  organizationId,
  pageSize = 5,
  group,
  name = "facility",
  defaultValue,
  onSelect,
  ...props
}: SearchFacilityProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    setSelection,
  } = useSelectFacility({
    enabled,
    organizationId,
    pageSize,
  });

  return (
    <SearchLocationBase<FacilityAsLocation>
      {...props}
      name={name}
      group={group}
      selection={selection}
      defaultValue={defaultValue}
      data={data}
      loading={hookLoading}
      onSelect={useCallback(
        async (facilityAsLocation: FacilityAsLocation) => {
          // Find the original facility from the hook data
          const originalFacility = data.find(
            (f) => f.id === facilityAsLocation.id,
          );
          if (originalFacility && onSelect) {
            await onSelect(originalFacility);
            await setSelection(originalFacility);
          }
        },
        [onSelect, setSelection, data],
      )}
      size="sm"
    />
  );
}
