"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { Offer } from "@/hooks/selectors/use-select-offer";

import { useSelectOffer } from "@/hooks/selectors/use-select-offer";

const i18n = {
  en: {
    label: "Offer",
    description: "Select an offer",
    placeholder: "Select an offer",
  },
};

export type OfferStructure = Offer;
type PartialOfferSelector = Omit<SelectorProps<Offer>, "data">;

export interface SelectOfferProps extends PartialOfferSelector {
  loading?: boolean;
  enabled?: boolean;
  providerId?: string;
  includeProvider?: boolean;
  defaultQuery?: string;
  defaultSelection?: Offer;
  defaultDebounce?: number;
  pageSize?: number;
  pageNumber?: number;
  size?: PartialOfferSelector["size"];
}

interface SelectOfferCoreProps extends PartialOfferSelector {
  data?: Offer[];
  loading?: boolean;
  selection?: Offer;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (offer: Offer) => void | Promise<void>;
  onClear?: () => void;
}

function SelectOfferCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectOfferCoreProps) {
  const enhancedData = useMemo(() => {
    const offers = [...data];
    if (selection && !offers.find((offer) => offer.id === selection.id)) {
      offers.unshift(selection);
    }
    return offers;
  }, [data, selection]);

  const renderItem = useCallback(
    (item: Offer) => {
      const name = item.provider?.person
        ? `${item.provider.person.firstName} ${item.provider.person.lastName}`
        : "Unknown Provider";

      const jobSummary = item.job?.summary ?? "No job summary";

      return (
        <div className="flex flex-col gap-1 truncate text-start">
          <div
            className={cn("truncate font-medium", {
              "text-sm": size === "sm",
              "text-base": size === "md",
              "text-lg": size === "lg",
            })}
          >
            {name}
          </div>
          <div
            className={cn("truncate text-sm text-muted-foreground", {
              "text-xs": size === "sm",
              "text-sm": size === "md",
              "text-base": size === "lg",
            })}
          >
            {jobSummary}
          </div>
        </div>
      );
    },
    [size],
  );

  const renderValue = useCallback((offer: Offer) => {
    if (offer.provider?.person) {
      return `${offer.provider.person.firstName} ${offer.provider.person.lastName}`;
    }
    return offer.job?.summary ?? i18n.en.placeholder;
  }, []);

  const renderLoading = useCallback(
    () => (
      <div className="flex flex-col gap-1 text-start">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-4 w-20" />
      </div>
    ),
    [],
  );

  return (
    <Selector<Offer>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-12 p-2": size === "md",
          "h-9 p-2": size === "sm",
          "min-h-8 py-2": size === "lg",
        },
        className,
      )}
      loading={loading}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={renderItem}
      renderLoading={renderLoading}
      renderValue={renderValue}
    >
      {children}
    </Selector>
  );
}

export function SelectOffer({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  providerId,
  includeProvider = true,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  pageNumber = 0,
  ...props
}: SelectOfferProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectOffer({
    enabled,
    providerId,
    includeProvider,
    pageSize,
    pageNumber,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectOfferCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectOfferFieldProps extends SelectOfferProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectOfferField({
  name = "offer",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectOfferFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectOffer
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchOfferProps extends SelectOfferProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchOffer({
  group,
  name = "offer",
  defaultValue,
  enabled = true,
  loading = false,
  providerId,
  includeProvider = true,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  pageNumber = 0,
  onSelect,
  useDialog = false,
  ...props
}: SearchOfferProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectOffer({
    enabled,
    providerId,
    includeProvider,
    pageSize,
    pageNumber,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Offer>({
    name,
    group,
    defaultValue,
    data,
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (offer: Offer) => {
      await setSelection(offer);
      await onSelect?.(offer);
      onSelectionChange(offer);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectOfferCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
      size="sm"
    />
  );
}
