"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import Image from "next/image";
import { useFormContext } from "react-hook-form";

import type { GenericNode, SelectorProps } from "@axa/ui/selectors/Selector";
import ContactName from "@axa/ui/common/ContactName";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { PersonRole } from "@/api";
import type { Person } from "@/hooks/selectors/use-select-person";

import { useSelectPerson } from "@/hooks/selectors/use-select-person";

const i18n = {
  en: {
    addPerson: "Add Person",
    label: "Person",
    description: "The person to contact",
    placeholder: "Select a person to contact",
    searchPerson: "Search person...",
    avatarFor: "avatar image for ",
  },
};

export type PersonStructure = Person;

export interface PersonPartial extends GenericNode {
  firstName: string;
  lastName: string;
  email?: string | null;
  phone?: string | null;
  avatar?: string | null;
  level?: number | null;
  userRole?: string | null;
  isUser?: boolean | null;
}

export interface SelectPersonCoreProps<PersonType extends PersonPartial>
  extends SelectorProps<PersonType> {
  placeholder?: string;
}

function SelectPersonCore<PersonType extends PersonPartial>({
  children,
  loading = false,
  placeholder = i18n.en.searchPerson,
  ...props
}: SelectPersonCoreProps<PersonType>) {
  return (
    <Selector<PersonType>
      loading={loading}
      label={i18n.en.addPerson}
      placeholder={placeholder}
      renderLoading={() => (
        <div className="flex w-full flex-col items-center justify-center gap-1">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      )}
      renderValue={useCallback(
        (person: PersonType) => `${person.firstName} ${person.lastName}`,
        [],
      )}
      renderItem={useCallback(
        (person: PersonType) => {
          const { name, initials } = {
            name: `${person.firstName ?? ""} ${person.lastName ?? ""}`.trim(),
            initials:
              [(person.firstName ?? " ")[0], (person.lastName ?? " ")[0]]
                .join("")
                .trim() || "AA",
          };
          return (
            <div className="flex items-center gap-2">
              {loading ? (
                <Skeleton className="size-9" />
              ) : (
                <Avatar className="size-9 rounded-lg">
                  <AvatarImage asChild src={person.avatar ?? ""}>
                    <Image
                      src={person.avatar ?? ""}
                      alt={i18n.en.avatarFor + name}
                      width={32}
                      height={32}
                    />
                  </AvatarImage>
                  <AvatarFallback className="rounded-lg">
                    {initials}
                  </AvatarFallback>
                </Avatar>
              )}

              <dl className="grid flex-1 gap-1">
                {loading ? (
                  <Skeleton className="size-9" />
                ) : (
                  <ContactName
                    showCopyButton={false}
                    name={name}
                    className="font-semibold text-foreground"
                  />
                )}
              </dl>
            </div>
          );
        },
        [loading],
      )}
      {...props}
    >
      {children}
    </Selector>
  );
}

export interface SelectPersonProps
  extends Omit<SelectPersonCoreProps<Person>, "data"> {
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  roles?: PersonRole[];
  defaultQuery?: string;
  defaultSelection?: Person;
  defaultOptions?: Person[];
  defaultDebounce?: number;
  pageSize?: number;
  group?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
  placeholder?: string;
}

export function SelectPerson({
  loading = false,
  enabled = true,
  useDialog = false,
  children,
  onSelect,
  organizationId,
  roles,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  group,
  size = "md",
  className,
  placeholder = i18n.en.searchPerson,
  ...props
}: SelectPersonProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectPerson({
    enabled,
    organizationId,
    roles,
    defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  const enhancedData = useMemo(() => {
    const people = [...data];
    if (selection && !people.find((person) => person.id === selection.id)) {
      people.unshift(selection);
    }
    return people;
  }, [data, selection]);

  return (
    <SelectPersonCore<Person>
      useDialog={useDialog}
      className={className}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={setSelection}
    >
      {children}
    </SelectPersonCore>
  );
}

export interface SelectPersonFieldCoreProps<DataType extends PersonPartial>
  extends SelectPersonCoreProps<DataType> {
  name?: "person.id";
  label?: string;
  description?: string;
  placeholder?: string;
  onQueryChange?: (value: string) => void;
}

function SelectPersonFieldCore<DataType extends PersonPartial>({
  loading = false,
  name = "person.id" as const,
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  data,
  onSelect,
  onQueryChange,
  ...props
}: SelectPersonFieldCoreProps<DataType>) {
  const form = useFormContext<{
    person?: DataType;
    "person.id"?: DataType["id"];
    personId?: DataType["id"];
  }>();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const selection = data?.find((person) => person.id === field.value);
        return (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <SelectPersonCore
                {...props}
                loading={loading}
                placeholder={placeholder}
                data={data ?? []}
                selection={selection}
                useDialog={false}
                onValueChange={(value) => {
                  onQueryChange?.(value);
                }}
                onSelect={async (person) => {
                  await onSelect?.(person);
                  field.onChange(person.id);
                  form.setValue("personId", person.id, {
                    shouldDirty: true,
                  });
                  form.setValue(
                    "person",
                    {
                      id: person.id,
                      firstName: person.firstName,
                      lastName: person.lastName,
                      email: person.email ?? "",
                      phone: person.phone ?? "",
                      avatar: person.avatar ?? "",
                      isUser: person.isUser ?? false,
                    } as DataType,
                    {
                      shouldDirty: true,
                    },
                  );
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}

export interface SelectPersonFieldProps<DataType extends Person = Person>
  extends Omit<SelectPersonFieldCoreProps<DataType>, "data" | "onValueChange"> {
  loading?: boolean;
  organizationId?: string;
  roles?: PersonRole[];
  enabled?: boolean;
  defaultOptions?: Person[];
  pageSize?: number;
  label?: string;
  description?: string;
  placeholder?: string;
  size?: "sm" | "md" | "lg";
}

export function SelectPersonField<DataType extends Person = Person>({
  organizationId,
  roles,
  enabled = true,
  loading,
  defaultOptions = [],
  pageSize = 5,
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  size = "md",
  className,
  onSelect,
  onQueryChange,
  ...props
}: SelectPersonFieldProps<DataType>) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectPerson({
    enabled,
    organizationId,
    roles,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectPersonFieldCore
      label={label}
      description={description}
      placeholder={placeholder}
      className={className}
      {...props}
      loading={isLoading}
      data={data as unknown as DataType[]}
      onValueChange={setQuery}
      onQueryChange={(value) => {
        void setQuery(value);
        onQueryChange?.(value);
      }}
      onSelect={onSelect}
    />
  );
}

export interface SearchPersonProps extends SelectPersonProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchPerson({
  group,
  name = "person",
  defaultValue,
  onSelect,
  useDialog = false,
  enabled = true,
  organizationId,
  roles,
  defaultOptions = [],
  pageSize = 5,
  ...props
}: SearchPersonProps) {
  const { data, loading, open, setOpen, query, setQuery } = useSelectPerson({
    enabled,
    organizationId,
    roles,
    defaultOptions,
    pageSize,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Person>({
    name,
    group,
    defaultValue,
    data,
  });

  const handleSelect = useCallback(
    async (person: Person) => {
      await onSelect?.(person);
      onSelectionChange(person);
    },
    [onSelect, onSelectionChange],
  );

  return (
    <SelectPersonCore
      {...props}
      data={data}
      loading={loading}
      useDialog={useDialog}
      selection={selection}
      open={open}
      value={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      size="sm"
    />
  );
}
