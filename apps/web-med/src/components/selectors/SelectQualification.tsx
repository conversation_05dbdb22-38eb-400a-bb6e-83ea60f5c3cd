"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { QualificationStatus, QualificationType } from "@/api";
import type { Qualification } from "@/hooks/selectors/use-select-qualification";

import { useSelectQualification } from "@/hooks/selectors/use-select-qualification";

import QualificationStatusBadge from "../common/QualificationStatus";
import QualificationTypeBadge from "../common/QualificationType";

const i18n = {
  en: {
    label: "Qualification",
    description: "Select a qualification",
    placeholder: "Select a qualification",
  },
};

export type QualificationStructure = Qualification;
type PartialQualificationSelector = Omit<SelectorProps<Qualification>, "data">;

export interface SelectQualificationProps extends PartialQualificationSelector {
  loading?: boolean;
  enabled?: boolean;
  providerId?: string;
  type?: QualificationType;
  status?: QualificationStatus;
  defaultQuery?: string;
  defaultSelection?: Qualification;
  defaultDebounce?: number;
  size?: PartialQualificationSelector["size"];
}

interface SelectQualificationCoreProps extends PartialQualificationSelector {
  data?: Qualification[];
  loading?: boolean;
  selection?: Qualification;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (qualification: Qualification) => void | Promise<void>;
  onClear?: () => void;
}

function SelectQualificationCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectQualificationCoreProps) {
  const enhancedData = useMemo(() => {
    const qualifications = [...data];
    if (
      selection &&
      !qualifications.find((qualification) => qualification.id === selection.id)
    ) {
      qualifications.unshift(selection);
    }
    return qualifications;
  }, [data, selection]);

  const renderItem = useCallback(
    (item: Qualification) => (
      <div className="flex flex-col gap-1 truncate text-start">
        <h3
          className={cn("truncate text-base font-semibold", {
            "text-sm": size === "sm",
            "text-base": size === "md",
            "text-lg": size === "lg",
          })}
        >
          {item.name}
        </h3>
        <div className="flex items-center gap-2">
          <QualificationStatusBadge status={item.status} />
          <QualificationTypeBadge type={item.type} />
        </div>
      </div>
    ),
    [size],
  );

  const renderLoading = useCallback(
    () => (
      <div className="flex flex-col gap-1 text-start">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-4 w-20" />
      </div>
    ),
    [],
  );

  return (
    <Selector<Qualification>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-12 p-2": size === "md",
          "h-9 p-2": size === "sm",
          "min-h-8 py-2": size === "lg",
        },
        className,
      )}
      loading={loading}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={renderItem}
      renderLoading={renderLoading}
    >
      {children}
    </Selector>
  );
}

export function SelectQualification({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  providerId,
  type,
  status,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  ...props
}: SelectQualificationProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectQualification({
    enabled,
    providerId,
    type,
    status,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectQualificationCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectQualificationFieldProps
  extends SelectQualificationProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectQualificationField({
  name = "qualification",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectQualificationFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectQualification
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchQualificationProps extends SelectQualificationProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchQualification({
  group,
  name = "qualification",
  defaultValue,
  enabled = true,
  loading = false,
  providerId,
  type,
  status,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  onSelect,
  useDialog = false,
  ...props
}: SearchQualificationProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectQualification({
    enabled,
    providerId,
    type,
    status,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } =
    useSearchValue<Qualification>({
      name,
      group,
      defaultValue,
      data: data ?? [],
    });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (qualification: Qualification) => {
      await setSelection(qualification);
      await onSelect?.(qualification);
      onSelectionChange(qualification);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectQualificationCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
      size="sm"
    />
  );
}
