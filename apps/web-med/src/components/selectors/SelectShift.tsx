"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { ShiftStatus } from "@/api";
import type { Shift } from "@/hooks/selectors/use-select-shift";

import { useSelectShift } from "@/hooks/selectors/use-select-shift";

const i18n = {
  en: {
    label: "Shift",
    description: "Select a shift",
    placeholder: "Select a shift",
  },
};

export type ShiftStructure = Shift;
type PartialShiftSelector = Omit<SelectorProps<Shift>, "data">;

export interface SelectShiftProps extends PartialShiftSelector {
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  facilityId?: string;
  status?: ShiftStatus[];
  includeLocation?: boolean;
  includeProvider?: boolean;
  includePerson?: boolean;
  defaultQuery?: string;
  defaultSelection?: Shift;
  defaultOptions?: Shift[];
  pageSize?: number;
  defaultDebounce?: number;
  size?: PartialShiftSelector["size"];
}

interface SelectShiftCoreProps extends PartialShiftSelector {
  data?: Shift[];
  loading?: boolean;
  selection?: Shift;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (shift: Shift) => void | Promise<void>;
  onClear?: () => void;
}

function SelectShiftCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectShiftCoreProps) {
  const enhancedData = useMemo(() => {
    const shifts = [...data];
    if (selection && !shifts.find((shift) => shift.id === selection.id)) {
      shifts.unshift(selection);
    }
    return shifts;
  }, [data, selection]);

  const handleRenderValue = useCallback(
    (shift: Shift) => {
      return shift.summary || placeholder;
    },
    [placeholder],
  );

  const handleRenderItem = useCallback((shift: Shift) => {
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{shift.summary}</div>
        {shift.location?.name && (
          <div className="text-sm text-muted-foreground">
            {shift.location.name}
          </div>
        )}
        {shift.provider?.person && (
          <div className="text-xs text-muted-foreground">
            Provider: {shift.provider.person.firstName}{" "}
            {shift.provider.person.lastName}
          </div>
        )}
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Shift>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={loading}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
    >
      {children}
    </Selector>
  );
}

/**
 * Shift selector component (data wrapper)
 */
export function SelectShift({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  organizationId,
  facilityId,
  status,
  includeLocation = true,
  includeProvider = true,
  includePerson = true,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  ...props
}: SelectShiftProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectShift({
    enabled,
    organizationId,
    facilityId,
    status,
    includeLocation,
    includeProvider,
    includePerson,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectShiftCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectShiftFieldProps extends SelectShiftProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectShiftField({
  name = "shift",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectShiftFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectShift
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchShiftProps extends SelectShiftProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchShift({
  group,
  name = "shift",
  defaultValue,
  enabled = true,
  loading = false,
  organizationId,
  facilityId,
  status,
  includeLocation = true,
  includeProvider = true,
  includePerson = true,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  onSelect,
  useDialog = false,
  ...props
}: SearchShiftProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectShift({
    enabled,
    organizationId,
    facilityId,
    status,
    includeLocation,
    includeProvider,
    includePerson,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Shift>({
    name,
    group,
    defaultValue,
    data,
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (shift: Shift) => {
      await setSelection(shift);
      await onSelect?.(shift);
      onSelectionChange(shift);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectShiftCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
      size="sm"
    />
  );
}
