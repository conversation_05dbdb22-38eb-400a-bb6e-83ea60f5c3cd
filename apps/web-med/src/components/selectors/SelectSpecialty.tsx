"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { PlusCircleIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { Specialty } from "@/hooks/selectors/use-select-specialty";

import { useSelectSpecialty } from "@/hooks/selectors/use-select-specialty";

import type { AddSpecialtyProps } from "../actions/specialty";

import { AddSpecialty } from "../actions/specialty";

const i18n = {
  en: {
    label: "Specialty",
    description: "Select a specialty",
    placeholder: "Select a specialty",
    missingSpecialty: "Can't find your specialty?",
    addNewSpecialty: "Add New Specialty",
  },
};

export type SpecialtyStructure = Specialty;
type PartialSpecialtySelector = Omit<SelectorProps<Specialty>, "data">;

export interface SelectSpecialtyProps extends PartialSpecialtySelector {
  open?: boolean;
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  defaultQuery?: string;
  defaultSelection?: Specialty;
  defaultDebounce?: number;
  canNotFindText?: string;
  shouldAddSpecialty?: boolean;
  onAdd?: AddSpecialtyProps["onAdd"];
  onSuccess?: AddSpecialtyProps["onSuccess"];
  onError?: AddSpecialtyProps["onError"];
  onSelect?: PartialSpecialtySelector["onSelect"];
  size?: PartialSpecialtySelector["size"];
}

interface SelectSpecialtyCoreProps extends PartialSpecialtySelector {
  data?: Specialty[];
  loading?: boolean;
  selection?: Specialty;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (specialty: Specialty) => void | Promise<void>;
  onClear?: () => void;
  canNotFindText?: string;
  shouldAddSpecialty?: boolean;
  onAdd?: AddSpecialtyProps["onAdd"];
  onSuccess?: AddSpecialtyProps["onSuccess"];
  onError?: AddSpecialtyProps["onError"];
}

function SelectSpecialtyCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  canNotFindText = i18n.en.missingSpecialty,
  shouldAddSpecialty = false,
  onAdd,
  onError,
  onSuccess,
  size = "md",
  className,
  ...props
}: SelectSpecialtyCoreProps) {
  const enhancedData = useMemo(() => {
    const specialties = [...data];
    if (
      selection &&
      !specialties.find((specialty) => specialty.id === selection.id)
    ) {
      specialties.unshift(selection);
    }
    return specialties;
  }, [data, selection]);

  const renderItem = useCallback(
    (item: Specialty) => (
      <div className="flex flex-col gap-1 truncate text-start">
        <h3
          className={cn("text-base font-bold", {
            "text-sm": size === "md",
            "text-xs": size === "sm",
          })}
        >
          {item.name}
        </h3>
        <p
          className={cn(
            "w-full overflow-hidden truncate text-sm text-muted-foreground",
            {
              "text-sm": size === "lg",
              "text-xs": size === "sm",
            },
          )}
        >
          {item.description ?? "No description"}
        </p>
      </div>
    ),
    [size],
  );

  return (
    <Selector<Specialty>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-18 p-2": size === "lg",
          "h-16 p-2": size === "md",
          "h-14 p-2": size === "sm",
        },
        className,
      )}
      label={i18n.en.placeholder}
      placeholder={i18n.en.placeholder}
      {...props}
      data={enhancedData}
      loading={loading}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={renderItem}
      footer={
        shouldAddSpecialty && (
          <div className="flex w-full flex-col items-center justify-center gap-2 pb-2 pt-4 text-center text-sm text-muted-foreground">
            {canNotFindText}{" "}
            <AddSpecialty
              onAdd={onAdd}
              onError={onError}
              onSuccess={async (result) => {
                onValueChange?.("");
                await onSelect?.(result as Specialty);
                await onSuccess?.(result);
              }}
            >
              <Button variant="outline" className="w-full gap-2">
                <PlusCircleIcon className="size-4" />
                {i18n.en.addNewSpecialty}
              </Button>
            </AddSpecialty>
          </div>
        )
      }
    >
      {children}
    </Selector>
  );
}

export function SelectSpecialty({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  shouldAddSpecialty = true,
  ...props
}: SelectSpecialtyProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectSpecialty({
    enabled,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectSpecialtyCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
      shouldAddSpecialty={shouldAddSpecialty}
    />
  );
}

export interface SelectSpecialtyFieldProps extends SelectSpecialtyProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectSpecialtyField({
  name = "specialty",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectSpecialtyFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectSpecialty
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchSpecialtyProps extends SelectSpecialtyProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchSpecialty({
  group,
  name = "specialty",
  defaultValue,
  enabled = true,
  loading = false,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  shouldAddSpecialty = false,
  onSelect,
  useDialog = false,
  ...props
}: SearchSpecialtyProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectSpecialty({
    enabled,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Specialty>({
    name,
    group,
    defaultValue,
    data: data ?? [],
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (specialty: Specialty) => {
      await setSelection(specialty);
      await onSelect?.(specialty);
      onSelectionChange(specialty);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectSpecialtyCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      shouldAddSpecialty={shouldAddSpecialty}
      useDialog={useDialog}
      size="sm"
    />
  );
}
