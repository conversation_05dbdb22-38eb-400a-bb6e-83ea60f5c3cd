"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { JobPositionStatus, JobPostType } from "@/api";
import type { Position } from "@/hooks/selectors/use-select-position";

import { useSelectPosition } from "@/hooks/selectors/use-select-position";

const i18n = {
  en: {
    label: "Position",
    description: "Select a position",
    placeholder: "Select a position",
  },
};

export type PositionStructure = Position;
type PartialPositionSelector = Omit<SelectorProps<Position>, "data">;

export interface SelectPositionProps extends PartialPositionSelector {
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  facilityId?: string;
  departmentId?: string;
  status?: JobPositionStatus[];
  type?: JobPostType[];
  includeProvider?: boolean;
  includeLocation?: boolean;
  includeFacility?: boolean;
  includeDepartment?: boolean;
  defaultQuery?: string;
  defaultSelection?: Position;
  defaultOptions?: Position[];
  pageSize?: number;
  defaultDebounce?: number;
  size?: PartialPositionSelector["size"];
}

interface SelectPositionCoreProps extends PartialPositionSelector {
  data?: Position[];
  loading?: boolean;
  selection?: Position;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (position: Position) => void | Promise<void>;
  onClear?: () => void;
}

function SelectPositionCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectPositionCoreProps) {
  const enhancedData = useMemo(() => {
    const positions = [...data];
    if (
      selection &&
      !positions.find((position) => position.id === selection.id)
    ) {
      positions.unshift(selection);
    }
    return positions;
  }, [data, selection]);

  const handleRenderValue = useCallback(
    (position: Position) => {
      return position.role || position.summary || placeholder;
    },
    [placeholder],
  );

  const handleRenderItem = useCallback((position: Position) => {
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{position.role}</div>
        {position.summary && (
          <div className="text-sm text-muted-foreground">
            {position.summary}
          </div>
        )}
        {position.location?.name && (
          <div className="text-xs text-muted-foreground">
            Location: {position.location.name}
          </div>
        )}
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Position>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={loading}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
    >
      {children}
    </Selector>
  );
}

export function SelectPosition({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  organizationId,
  facilityId,
  departmentId,
  status,
  type,
  includeProvider = true,
  includeLocation = true,
  includeFacility = true,
  includeDepartment = true,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  ...props
}: SelectPositionProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectPosition({
    enabled,
    organizationId,
    facilityId,
    departmentId,
    status,
    type,
    includeProvider,
    includeLocation,
    includeFacility,
    includeDepartment,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectPositionCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectPositionFieldProps extends SelectPositionProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectPositionField({
  name = "position",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectPositionFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectPosition
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchPositionProps extends SelectPositionProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchPosition({
  group,
  name = "position",
  defaultValue,
  enabled = true,
  loading = false,
  organizationId,
  facilityId,
  departmentId,
  status,
  type,
  includeProvider = true,
  includeLocation = true,
  includeFacility = true,
  includeDepartment = true,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  onSelect,
  useDialog = false,
  ...props
}: SearchPositionProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectPosition({
    enabled,
    organizationId,
    facilityId,
    departmentId,
    status,
    type,
    includeProvider,
    includeLocation,
    includeFacility,
    includeDepartment,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Position>({
    name,
    group,
    defaultValue,
    data,
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (position: Position) => {
      await setSelection(position);
      await onSelect?.(position);
      onSelectionChange(position);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectPositionCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
      size="sm"
    />
  );
}
