"use client";

import { useCallback } from "react";

import type {
  SearchDocumentProps as SearchDocumentBaseProps,
  SelectDocumentProps as SelectDocumentBaseProps,
  SelectDocumentFieldProps as SelectDocumentFieldBaseProps,
} from "@axa/ui/selectors/SelectDocument";
import { useSearchValue } from "@axa/ui/search/value";
import {
  SearchDocument as SearchDocumentBase,
  SelectDocument as SelectDocumentBase,
  SelectDocumentField as SelectDocumentFieldBase,
} from "@axa/ui/selectors/SelectDocument";

import type { Document } from "@/hooks/selectors/use-select-document";

import { useSelectDocument } from "@/hooks/selectors/use-select-document";

const i18n = {
  en: {
    label: "Document",
    description: "Select a document",
    placeholder: "Select a document",
  },
};

export interface SelectDocumentProps
  extends Omit<SelectDocumentBaseProps<Document>, "data"> {
  loading?: boolean;
  enabled?: boolean;
  type?: string;
  defaultQuery?: string;
  defaultSelection?: Document;
  defaultOptions?: Document[];
  defaultDebounce?: number;
  pageSize?: number;
}

export function SelectDocument({
  loading = false,
  enabled = true,
  type,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  onSelect,
  ...props
}: SelectDocumentProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectDocument({
    enabled,
    type,
    defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectDocumentBase<Document>
      {...props}
      data={data}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectDocumentFieldProps
  extends Omit<SelectDocumentFieldBaseProps<Document>, "data"> {
  loading?: boolean;
  enabled?: boolean;
  type?: string;
  defaultOptions?: Document[];
  pageSize?: number;
  name?: string;
  label?: string;
  description?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectDocumentField({
  type,
  enabled = true,
  loading,
  defaultOptions = [],
  pageSize = 5,
  name = "documentId",
  label = i18n.en.label,
  description = i18n.en.description,
  showLabel = true,
  showDescription = true,
  onSelect,
  ...props
}: SelectDocumentFieldProps) {
  const { data, loading: hookLoading } = useSelectDocument({
    enabled,
    type,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectDocumentFieldBase<Document>
      {...props}
      name={name}
      label={label}
      description={description}
      showLabel={showLabel}
      showDescription={showDescription}
      data={data}
      loading={isLoading}
      onSelect={onSelect}
    />
  );
}

export interface SearchDocumentProps
  extends Omit<SearchDocumentBaseProps<Document>, "data"> {
  enabled?: boolean;
  type?: string;
  defaultOptions?: Document[];
  pageSize?: number;
  group?: string;
  name?: string;
  defaultValue?: string;
}

export function SearchDocument({
  enabled = true,
  type,
  defaultOptions = [],
  pageSize = 5,
  group,
  name = "document",
  defaultValue,
  onSelect,
  ...props
}: SearchDocumentProps) {
  const { data, loading } = useSelectDocument({
    enabled,
    type,
    defaultOptions,
    pageSize,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Document>({
    name,
    group,
    defaultValue,
    data,
  });

  const handleSelect = useCallback(
    async (document: Document) => {
      await onSelect?.(document);
      onSelectionChange(document);
    },
    [onSelect, onSelectionChange],
  );

  return (
    <SearchDocumentBase<Document>
      {...props}
      name={name}
      group={group}
      defaultValue={defaultValue}
      loading={loading}
      data={data}
      selection={selection}
      onSelect={handleSelect}
      onClear={onClear}
      size="sm"
    />
  );
}
