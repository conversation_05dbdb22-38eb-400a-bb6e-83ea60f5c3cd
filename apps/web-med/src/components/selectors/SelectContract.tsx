"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { ContractStatus } from "@/api";
import type { Contract } from "@/hooks/selectors/use-select-contract";

import { useSelectContract } from "@/hooks/selectors/use-select-contract";

const i18n = {
  en: {
    label: "Contract",
    description: "Select a contract",
    placeholder: "Select a contract",
  },
};

export type ContractStructure = Contract;
type PartialContractSelector = Omit<SelectorProps<Contract>, "data">;

export interface SelectContractProps extends PartialContractSelector {
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  providerId?: string;
  status?: ContractStatus;
  type?:
    | "OTHER"
    | "EMPLOYMENT"
    | "NON_COMPETE"
    | "NON_DISCLOSURE"
    | "SERVICE_RATE"
    | "SERVICE_AGREEMENT";
  includeProvider?: boolean;
  includeOrganization?: boolean;
  defaultQuery?: string;
  defaultSelection?: Contract;
  defaultOptions?: Contract[];
  pageSize?: number;
  defaultDebounce?: number;
  size?: PartialContractSelector["size"];
}

interface SelectContractCoreProps extends PartialContractSelector {
  data?: Contract[];
  loading?: boolean;
  selection?: Contract;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (contract: Contract) => void | Promise<void>;
  onClear?: () => void;
}

function SelectContractCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectContractCoreProps) {
  const enhancedData = useMemo(() => {
    const contracts = [...data];
    if (
      selection &&
      !contracts.find((contract) => contract.id === selection.id)
    ) {
      contracts.unshift(selection);
    }
    return contracts;
  }, [data, selection]);

  const handleRenderValue = useCallback(
    (contract: Contract) => {
      return contract.title || placeholder;
    },
    [placeholder],
  );

  const handleRenderItem = useCallback((contract: Contract) => {
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{contract.title}</div>
        <div className="text-sm text-muted-foreground">
          Status: {contract.status}
        </div>
        {contract.organization?.name && (
          <div className="text-xs text-muted-foreground">
            Organization: {contract.organization.name}
          </div>
        )}
        {contract.provider?.person.firstName && (
          <div className="text-xs text-muted-foreground">
            Provider: {contract.provider.person.firstName}{" "}
            {contract.provider.person.lastName}
          </div>
        )}
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Contract>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={loading}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
    >
      {children}
    </Selector>
  );
}

export function SelectContract({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  organizationId,
  providerId,
  status,
  type,
  includeProvider = true,
  includeOrganization = true,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  ...props
}: SelectContractProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectContract({
    enabled,
    organizationId,
    providerId,
    status,
    type,
    includeProvider,
    includeOrganization,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectContractCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectContractFieldProps extends SelectContractProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectContractField({
  name = "contract",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectContractFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectContract
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchContractProps extends SelectContractProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchContract({
  group,
  name = "contract",
  defaultValue,
  enabled = true,
  loading = false,
  organizationId,
  providerId,
  status,
  type,
  includeProvider = true,
  includeOrganization = true,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  onSelect,
  useDialog = false,
  ...props
}: SearchContractProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectContract({
    enabled,
    organizationId,
    providerId,
    status,
    type,
    includeProvider,
    includeOrganization,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Contract>({
    name,
    group,
    defaultValue,
    data,
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (contract: Contract) => {
      await setSelection(contract);
      await onSelect?.(contract);
      onSelectionChange(contract);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectContractCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
      size="sm"
    />
  );
}
