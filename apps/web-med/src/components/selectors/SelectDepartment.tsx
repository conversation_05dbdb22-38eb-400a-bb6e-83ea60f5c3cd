"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { Department } from "@/hooks/selectors/use-select-department";

import { useSelectDepartment } from "@/hooks/selectors/use-select-department";

import DepartmentTypeBadge from "../common/DepartmentType";

const i18n = {
  en: {
    label: "Department",
    description: "Select a department",
    placeholder: "Select a department",
  },
};

export type DepartmentStructure = Department;
type PartialDepartmentSelector = Omit<SelectorProps<Department>, "data">;

export interface SelectDepartmentProps extends PartialDepartmentSelector {
  loading?: boolean;
  enabled?: boolean;
  facilityId?: string;
  defaultQuery?: string;
  defaultSelection?: Department;
  defaultOptions?: Department[];
  defaultDebounce?: number;
  pageSize?: number;
  pageNumber?: number;
  size?: PartialDepartmentSelector["size"];
}

interface SelectDepartmentCoreProps extends PartialDepartmentSelector {
  data?: Department[];
  loading?: boolean;
  selection?: Department;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (department: Department) => void | Promise<void>;
  onClear?: () => void;
}

function SelectDepartmentCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectDepartmentCoreProps) {
  const enhancedData = useMemo(() => {
    const departments = [...data];
    if (
      selection &&
      !departments.find((department) => department.id === selection.id)
    ) {
      departments.unshift(selection);
    }
    return departments;
  }, [data, selection]);

  const renderItem = useCallback(
    (item: Department) => (
      <div className="flex flex-col gap-1 truncate text-start">
        <h3
          className={cn("truncate text-base font-semibold", {
            "text-sm": size === "sm",
            "text-base": size === "md",
            "text-lg": size === "lg",
          })}
        >
          {item.name}
        </h3>
        {item.description && (
          <p
            className={cn("truncate text-sm text-muted-foreground", {
              "text-xs": size === "sm",
              "text-sm": size === "md",
              "text-base": size === "lg",
            })}
          >
            {item.description}
          </p>
        )}
        <div className="flex items-center gap-2">
          <DepartmentTypeBadge type={item.type} size="sm" />
          {item.organization && (
            <span className="text-xs text-muted-foreground">
              {item.organization.name}
            </span>
          )}
        </div>
      </div>
    ),
    [size],
  );

  const renderLoading = useCallback(
    () => (
      <div className="flex flex-col gap-1 text-start">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-4 w-20" />
      </div>
    ),
    [],
  );

  return (
    <Selector<Department>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-12 p-2": size === "md",
          "h-9 p-2": size === "sm",
          "min-h-8 py-2": size === "lg",
        },
        className,
      )}
      loading={loading}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={renderItem}
      renderLoading={renderLoading}
    >
      {children}
    </Selector>
  );
}

export function SelectDepartment({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  facilityId,
  defaultQuery,
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  pageNumber = 0,
  ...props
}: SelectDepartmentProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectDepartment({
    enabled,
    facilityId,
    pageSize,
    pageNumber,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectDepartmentCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectDepartmentFieldProps extends SelectDepartmentProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectDepartmentField({
  name = "department",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectDepartmentFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectDepartment
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchDepartmentProps extends SelectDepartmentProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchDepartment({
  group,
  name = "department",
  defaultValue,
  enabled = true,
  loading = false,
  facilityId,
  defaultQuery,
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  pageNumber = 0,
  onSelect,
  useDialog = false,
  ...props
}: SearchDepartmentProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectDepartment({
    enabled,
    facilityId,
    pageSize,
    pageNumber,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Department>({
    name,
    group,
    defaultValue,
    data,
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (department: Department) => {
      await setSelection(department);
      await onSelect?.(department);
      onSelectionChange(department);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectDepartmentCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
      size="sm"
    />
  );
}
