"use client";

import { useCallback } from "react";

import type {
  SearchLocationProps as SearchLocationBaseProps,
  SelectLocationProps as SelectLocationBaseProps,
  SelectLocationFieldProps as SelectLocationFieldBaseProps,
} from "@axa/ui/selectors/SelectLocation";
import { useSearchValue } from "@axa/ui/search/value";
import {
  SearchLocation as SearchLocationBase,
  SelectLocation as SelectLocationBase,
  SelectLocationField as SelectLocationFieldBase,
} from "@axa/ui/selectors/SelectLocation";

import type {
  Address,
  AddressAsLocation,
} from "@/hooks/selectors/use-select-address";

import { useSelectAddress } from "@/hooks/selectors/use-select-address";

export type { Address, AddressAsLocation };

const i18n = {
  en: {
    label: "Address",
    description: "Select an address",
    placeholder: "Select an address",
  },
};

export interface SelectAddressProps
  extends Omit<SelectLocationBaseProps<AddressAsLocation>, "data"> {
  loading?: boolean;
  enabled?: boolean;
  defaultQuery?: string;
  defaultDebounce?: number;
  pageSize?: number;
  onSelect?: (address: Address | AddressAsLocation) => void | Promise<void>;
}

export function SelectAddress({
  loading = false,
  enabled = true,
  defaultQuery = "",
  defaultDebounce = 500,
  pageSize = 5,
  onSelect,
  ...props
}: SelectAddressProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectAddress({
    enabled,
    defaultQuery,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectLocationBase<AddressAsLocation>
      {...props}
      data={data}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectAddressFieldProps
  extends Omit<SelectLocationFieldBaseProps<AddressAsLocation>, "data"> {
  loading?: boolean;
  enabled?: boolean;
  pageSize?: number;
  name?: string;
  label?: string;
  description?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectAddressField({
  enabled = true,
  loading,
  pageSize = 5,
  name = "addressId",
  label = i18n.en.label,
  description = i18n.en.description,
  showLabel = true,
  showDescription = true,
  onSelect,
  ...props
}: SelectAddressFieldProps) {
  const { data, loading: hookLoading } = useSelectAddress({
    enabled,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectLocationFieldBase<AddressAsLocation>
      {...props}
      name={name}
      label={label}
      description={description}
      showLabel={showLabel}
      showDescription={showDescription}
      data={data}
      loading={isLoading}
      onSelect={onSelect}
    />
  );
}

export interface SearchAddressProps
  extends Omit<SearchLocationBaseProps<AddressAsLocation>, "data"> {
  enabled?: boolean;
  pageSize?: number;
  group: string;
  name?: string;
  defaultValue?: string;
  onSelect?: (address: Address | AddressAsLocation) => void | Promise<void>;
}

export function SearchAddress({
  enabled = true,
  pageSize = 5,
  group,
  name = "address",
  defaultValue,
  onSelect,
  ...props
}: SearchAddressProps) {
  const { data, loading } = useSelectAddress({
    enabled,
    pageSize,
  });

  const { selection, onClear, onSelectionChange } =
    useSearchValue<AddressAsLocation>({
      name,
      group,
      defaultValue,
      data,
    });

  const handleSelect = useCallback(
    async (address: AddressAsLocation) => {
      await onSelect?.(address);
      onSelectionChange(address);
    },
    [onSelect, onSelectionChange],
  );

  return (
    <SearchLocationBase<AddressAsLocation>
      {...props}
      name={name}
      group={group}
      defaultValue={defaultValue}
      loading={loading}
      data={data}
      selection={selection}
      onSelect={handleSelect}
      onClear={onClear}
      size="sm"
    />
  );
}
