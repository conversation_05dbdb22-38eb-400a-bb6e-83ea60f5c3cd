"use client";

import { useMemo } from "react";

import type {
  SearchOrganizationProps as BaseSearchOrganizationProps,
  SelectOrganizationFieldProps as SelectFieldProps,
  SelectOrganizationProps as SelectorProps,
} from "@axa/ui/selectors/SelectOrganization";
import {
  SearchOrganization as SearchOrganizationBase,
  SelectOrganizationField as SelectField,
  SelectOrganization as SelectOrganizationCore,
} from "@axa/ui/selectors/SelectOrganization";

import type { OrganizationStatus, OrganizationType } from "@/api";
import type { Organization } from "@/hooks/selectors/use-select-organization";

import { useSelectOrganization } from "@/hooks/selectors/use-select-organization";

const i18n = {
  en: {
    label: "Organization",
    description: "Select an organization",
    placeholder: "Select an organization",
    actions: {
      SelectOrganization: "Select Organization",
    },
  },
};

export interface SelectOrganizationProps
  extends Omit<SelectorProps<Organization>, "data"> {
  types?: OrganizationType[];
  status?: OrganizationStatus[];
  enabled?: boolean;
  defaultQuery?: string;
  defaultSelection?: Organization;
  defaultOptions?: Organization[];
  defaultDebounce?: number;
  pageSize?: number;
  organizationId?: string;
  size?: "sm" | "md" | "lg";
}

export function SelectOrganization({
  loading = false,
  enabled = true,
  useDialog = false,
  children,
  onSelect,
  types,
  status,
  defaultQuery = "",
  defaultSelection,
  defaultOptions = [],
  defaultDebounce = 500,
  pageSize = 5,
  organizationId,
  size = "lg",
  className,
  ...props
}: SelectOrganizationProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectOrganization({
    enabled,
    types,
    status,
    defaultQuery,
    defaultSelection,
    defaultOptions,
    defaultDebounce,
    pageSize,
    organizationId,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  const enhancedData = useMemo(() => {
    const orgs = [...data];
    if (selection && !orgs.find((org) => org.id === selection.id)) {
      orgs.unshift(selection);
    }
    return orgs;
  }, [data, selection]);

  return (
    <SelectOrganizationCore<Organization>
      useDialog={useDialog}
      size={size}
      className={className}
      {...props}
      data={enhancedData}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={setSelection}
    >
      {children}
    </SelectOrganizationCore>
  );
}

export interface SelectOrganizationFieldProps<
  T extends Organization = Organization,
> extends Omit<SelectFieldProps<T>, "data" | "onValueChange"> {
  loading?: boolean;
  types?: OrganizationType[];
  status?: OrganizationStatus[];
  organizationId?: string;
  enabled?: boolean;
  defaultOptions?: Organization[];
  pageSize?: number;
  label?: string;
  description?: string;
  placeholder?: string;
  size?: "sm" | "md" | "lg";
}

export function SelectOrganizationField<T extends Organization = Organization>({
  types,
  status,
  organizationId,
  enabled = true,
  loading,
  defaultOptions = [],
  pageSize = 5,
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  size = "lg",
  className,
  ...props
}: SelectOrganizationFieldProps<T>) {
  const {
    data,
    selection,
    open,
    loading: hookLoading,
    setQuery,
    setSelection,
    setOpen,
  } = useSelectOrganization({
    enabled,
    types,
    status,
    organizationId,
    defaultOptions,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectField<T>
      label={label}
      description={description}
      placeholder={placeholder}
      size={size}
      className={className}
      {...props}
      loading={isLoading}
      open={open}
      selection={selection as T | null | undefined}
      data={data as T[]}
      onValueChange={setQuery}
      onSelect={setSelection}
      onOpenChange={setOpen}
    />
  );
}

export interface SearchOrganizationProps
  extends BaseSearchOrganizationProps<Organization> {
  group: string;
  name?: string;
  defaultValue?: string;
  enabled?: boolean;
  types?: OrganizationType[];
  status?: OrganizationStatus[];
  organizationId?: string;
  defaultOptions?: Organization[];
  pageSize?: number;
}

export function SearchOrganization({
  group,
  name = "organization",
  defaultValue,
  onSelect,
  useDialog = false,
  enabled = true,
  types,
  status,
  organizationId,
  defaultOptions,
  pageSize,
  ...props
}: SearchOrganizationProps) {
  const {
    data,
    loading: hookLoading,
    setQuery,
  } = useSelectOrganization({
    enabled,
    types,
    status,
    organizationId,
    defaultOptions,
    pageSize,
  });

  return (
    <SearchOrganizationBase
      {...props}
      data={data}
      group={group}
      name={name}
      defaultValue={defaultValue}
      useDialog={useDialog}
      onSelect={onSelect}
      loading={hookLoading}
      onValueChange={setQuery}
      size="sm"
    />
  );
}
