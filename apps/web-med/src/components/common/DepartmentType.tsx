import type { VariantProps } from "class-variance-authority";
import type { LucideIcon } from "lucide-react";

import { cva } from "class-variance-authority";
import {
  BedDouble,
  Building2,
  DoorOpen,
  HelpCircle,
  Library,
  SquareActivity,
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { DepartmentType } from "@/api";

const i18n = {
  en: {
    types: {
      [DepartmentType.CENTER]: "Center",
      [DepartmentType.INSTITUTE]: "Institute",
      [DepartmentType.DEPARTMENT]: "Department",
      [DepartmentType.WARD]: "Ward",
      [DepartmentType.UNIT]: "Unit",
      [DepartmentType.ROOM]: "Room",
      [DepartmentType.OTHER]: "Other",
    } satisfies Record<DepartmentType, string>,
  },
};

const typeIconMap: Record<string, LucideIcon> = {
  [DepartmentType.CENTER]: Building2,
  [DepartmentType.INSTITUTE]: Library,
  [DepartmentType.DEPARTMENT]: Building2,
  [DepartmentType.WARD]: BedDouble,
  [DepartmentType.UNIT]: SquareActivity,
  [DepartmentType.ROOM]: DoorOpen,
  [DepartmentType.OTHER]: HelpCircle,
};

const departmentTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [DepartmentType.CENTER]:
          "border-purple-200 bg-purple-50 text-purple-800 hover:bg-purple-100 dark:border-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/30",
        [DepartmentType.INSTITUTE]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [DepartmentType.DEPARTMENT]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [DepartmentType.WARD]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [DepartmentType.UNIT]:
          "border-amber-200 bg-amber-50 text-amber-800 hover:bg-amber-100 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-300 dark:hover:bg-amber-900/30",
        [DepartmentType.ROOM]:
          "border-cyan-200 bg-cyan-50 text-cyan-800 hover:bg-cyan-100 dark:border-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300 dark:hover:bg-cyan-900/30",
        [DepartmentType.OTHER]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "gap-1 px-2 py-0.5 text-xs",
        md: "gap-1.5 px-2.5 py-0.5 text-xs",
        lg: "gap-2 px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface DepartmentTypeBadgeProps
  extends VariantProps<typeof departmentTypeVariants> {
  type?: DepartmentType;
  loading?: boolean;
  showIcon?: boolean;
  className?: string;
}

export default function DepartmentTypeBadge({
  type,
  loading,
  showIcon = true,
  size,
  className,
}: DepartmentTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const Icon = type ? (typeIconMap[type] ?? HelpCircle) : HelpCircle;
  const label = type ? i18n.en.types[type] : "Unknown";
  const iconSize =
    size === "sm" ? "h-3 w-3" : size === "lg" ? "h-4 w-4" : "h-3.5 w-3.5";

  return (
    <span
      className={cn(departmentTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Department type: ${label}`}
    >
      {showIcon && <Icon className={iconSize} aria-hidden="true" />}
      <span>{label}</span>
    </span>
  );
}
