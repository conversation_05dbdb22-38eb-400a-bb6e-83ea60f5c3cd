import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { JobPostMode } from "@/api";

const i18n = {
  en: {
    mode: {
      [JobPostMode.INDEPENDENT]: "Independent",
      [JobPostMode.ASSISTED]: "Assisted",
    },
  },
};

const jobPostModeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      mode: {
        [JobPostMode.INDEPENDENT]:
          "border-gray-200 bg-white text-gray-900 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700",
        [JobPostMode.ASSISTED]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface JobPostModeBadgeProps
  extends VariantProps<typeof jobPostModeVariants> {
  type: JobPostMode;
  mode?: JobPostMode; // For future standardization
  loading?: boolean;
  className?: string;
}

export default function JobPostModeBadge({
  type,
  mode = type, // Backward compatibility fallback
  loading,
  size,
  className,
}: JobPostModeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const label = i18n.en.mode[mode];

  return (
    <span
      className={cn(jobPostModeVariants({ mode, size }), className)}
      role="img"
      aria-label={`Job post mode: ${label}`}
    >
      {label}
    </span>
  );
}
