import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";
import {
  Building,
  Building2,
  HandHeartIcon,
  HeartPulseIcon,
  HomeIcon,
  Hospital,
  MicroscopeIcon,
  MoreHorizontal,
  Pill,
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { FacilityType } from "@/api";

const i18n = {
  en: {
    [FacilityType.CAMPUS]: "Medical Campus",
    [FacilityType.HOSPITAL]: "Hospital",
    [FacilityType.CLINIC]: "Clinic",
    [FacilityType.OFFICE]: "Medical Office",
    [FacilityType.PHARMACY]: "Pharmacy",
    [FacilityType.LAB]: "Laboratory",
    [FacilityType.IMAGING]: "Imaging Center",
    [FacilityType.REHABILITATION]: "Rehabilitation",
    [FacilityType.OTHER]: "Other",
  },
};

export const facilityIcons = {
  [FacilityType.CAMPUS]: Building2,
  [FacilityType.HOSPITAL]: Hospital,
  [FacilityType.CLINIC]: Building,
  [FacilityType.OFFICE]: HomeIcon,
  [FacilityType.PHARMACY]: Pill,
  [FacilityType.LAB]: MicroscopeIcon,
  [FacilityType.IMAGING]: HeartPulseIcon,
  [FacilityType.REHABILITATION]: HandHeartIcon,
  [FacilityType.OTHER]: MoreHorizontal,
} as const;

const typeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        [FacilityType.CAMPUS]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [FacilityType.HOSPITAL]:
          "border-purple-200 bg-purple-50 text-purple-800 hover:bg-purple-100 dark:border-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/30",
        [FacilityType.CLINIC]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [FacilityType.OFFICE]:
          "border-orange-200 bg-orange-50 text-orange-800 hover:bg-orange-100 dark:border-orange-800 dark:bg-orange-900/20 dark:text-orange-300 dark:hover:bg-orange-900/30",
        [FacilityType.PHARMACY]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [FacilityType.LAB]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [FacilityType.IMAGING]:
          "border-indigo-200 bg-indigo-50 text-indigo-800 hover:bg-indigo-100 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300 dark:hover:bg-indigo-900/30",
        [FacilityType.REHABILITATION]:
          "border-pink-200 bg-pink-50 text-pink-800 hover:bg-pink-100 dark:border-pink-800 dark:bg-pink-900/20 dark:text-pink-300 dark:hover:bg-pink-900/30",
        [FacilityType.OTHER]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: FacilityType.HOSPITAL,
      size: "md",
    },
  },
);

export interface FacilityTypeProps extends VariantProps<typeof typeVariants> {
  type: FacilityType;
  className?: string;
  showIcon?: boolean;
  loading?: boolean;
}

export default function FacilityTypeBadge({
  type,
  className,
  showIcon = true,
  size = "md",
  loading = false,
}: FacilityTypeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const Icon = facilityIcons[type];
  return (
    <span
      className={cn(typeVariants({ variant: type, size }), className)}
      role="img"
      aria-label={`Facility type: ${i18n.en[type]}`}
    >
      {showIcon && (
        <Icon
          className={cn("mr-1", {
            "size-3": size === "sm",
            "size-3.5": size === "md",
            "size-4": size === "lg",
          })}
        />
      )}
      {i18n.en[type]}
    </span>
  );
}
