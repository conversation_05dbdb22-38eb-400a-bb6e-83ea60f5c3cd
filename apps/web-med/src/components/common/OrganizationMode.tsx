import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { OrganizationMode } from "@/api";

const i18n = {
  en: {
    types: {
      [OrganizationMode.INDEPENDENT]: "Independent",
      [OrganizationMode.ASSISTED]: "Assisted",
    } satisfies Record<OrganizationMode, string>,
  },
};

const modeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      mode: {
        [OrganizationMode.INDEPENDENT]:
          "border-gray-200 bg-transparent text-gray-800 hover:bg-gray-50 dark:border-gray-800 dark:text-gray-300 dark:hover:bg-gray-900/20",
        [OrganizationMode.ASSISTED]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      mode: OrganizationMode.INDEPENDENT,
      size: "md",
    },
  },
);

export interface OrganizationModeProps
  extends VariantProps<typeof modeVariants> {
  type: OrganizationMode;
  loading?: boolean;
  className?: string;
}

export default function OrganizationModeBadge({
  type,
  loading,
  size,
  className,
}: OrganizationModeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(modeVariants({ mode: type, size }), className)}
      role="img"
      aria-label={`Organization mode: ${i18n.en.types[type]}`}
    >
      {i18n.en.types[type]}
    </span>
  );
}
