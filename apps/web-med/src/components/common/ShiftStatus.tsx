import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { ShiftStatus } from "@/api";

const i18n = {
  en: {
    status: {
      [ShiftStatus.PENDING]: "Pending",
      [ShiftStatus.CONFIRMED]: "Confirmed",
      [ShiftStatus.ACTIVE]: "In Progress",
      [ShiftStatus.COMPLETED]: "Completed",
      [ShiftStatus.CANCELLED]: "Cancelled",
      [ShiftStatus.APPROVED]: "Approved",
      [ShiftStatus.REJECTED]: "Rejected",
    },
  },
};

const shiftStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        [ShiftStatus.PENDING]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [ShiftStatus.CONFIRMED]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [ShiftStatus.ACTIVE]:
          "border-indigo-200 bg-indigo-50 text-indigo-800 hover:bg-indigo-100 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300 dark:hover:bg-indigo-900/30",
        [ShiftStatus.COMPLETED]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [ShiftStatus.CANCELLED]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [ShiftStatus.APPROVED]:
          "border-emerald-200 bg-emerald-50 text-emerald-800 hover:bg-emerald-100 dark:border-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-300 dark:hover:bg-emerald-900/30",
        [ShiftStatus.REJECTED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface ShiftStatusProps
  extends VariantProps<typeof shiftStatusVariants> {
  status: ShiftStatus;
  loading?: boolean;
  className?: string;
}

export default function ShiftStatusBadge({
  status,
  loading,
  size,
  className,
}: ShiftStatusProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const label = i18n.en.status[status];

  return (
    <span
      className={cn(shiftStatusVariants({ status, size }), className)}
      role="status"
      aria-label={`Shift status: ${label}`}
    >
      {label}
    </span>
  );
}
