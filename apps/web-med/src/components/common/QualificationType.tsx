import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";
import {
  AwardIcon,
  CircleHelpIcon,
  GraduationCapIcon,
  KeyIcon,
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { QualificationType } from "@/api";

const i18n = {
  en: {
    types: {
      [QualificationType.DEGREE]: "Degree",
      [QualificationType.LICENSE]: "License",
      [QualificationType.CERTIFICATE]: "Certificate",
      [QualificationType.OTHER]: "Other",
    } satisfies Record<QualificationType, string>,
  },
};

const qualificationTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [QualificationType.CERTIFICATE]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [QualificationType.DEGREE]:
          "border-indigo-200 bg-indigo-50 text-indigo-800 hover:bg-indigo-100 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300 dark:hover:bg-indigo-900/30",
        [QualificationType.LICENSE]:
          "border-violet-200 bg-violet-50 text-violet-800 hover:bg-violet-100 dark:border-violet-800 dark:bg-violet-900/20 dark:text-violet-300 dark:hover:bg-violet-900/30",
        [QualificationType.OTHER]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export const qualificationTypeConfig = {
  [QualificationType.CERTIFICATE]: {
    label: i18n.en.types[QualificationType.CERTIFICATE],
    icon: AwardIcon,
  },
  [QualificationType.DEGREE]: {
    label: i18n.en.types[QualificationType.DEGREE],
    icon: GraduationCapIcon,
  },
  [QualificationType.LICENSE]: {
    label: i18n.en.types[QualificationType.LICENSE],
    icon: KeyIcon,
  },
  [QualificationType.OTHER]: {
    label: i18n.en.types[QualificationType.OTHER],
    icon: CircleHelpIcon,
  },
};

export interface QualificationTypeProps
  extends VariantProps<typeof qualificationTypeVariants> {
  type: QualificationType;
  loading?: boolean;
  className?: string;
  showIcon?: boolean;
}

export default function QualificationTypeBadge({
  type,
  loading,
  size,
  className,
  showIcon = true,
}: QualificationTypeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const { icon: Icon, label } = qualificationTypeConfig[type];

  return (
    <span
      className={cn(qualificationTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Qualification type: ${label}`}
    >
      {showIcon && (
        <Icon
          className={cn("mr-1", {
            "h-3 w-3": size === "sm",
            "h-3.5 w-3.5": size === "md" || !size,
            "h-4 w-4": size === "lg",
          })}
        />
      )}
      {label}
    </span>
  );
}
