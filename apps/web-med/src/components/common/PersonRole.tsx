import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { PersonRole } from "@/api";

const i18n = {
  en: {
    types: {
      [PersonRole.ADMIN]: "Admin",
      [PersonRole.BILLING]: "Billing",
      [PersonRole.INTERNAL]: "Internal",
      [PersonRole.CLIENT]: "Client",
      [PersonRole.PROVIDER]: "Provider",
      [PersonRole.USER]: "User",
      [PersonRole.NONE]: "None",
    } satisfies Record<PersonRole, string>,
  },
};

const personRoleVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [PersonRole.ADMIN]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [PersonRole.BILLING]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [PersonRole.INTERNAL]:
          "border-purple-200 bg-purple-50 text-purple-800 hover:bg-purple-100 dark:border-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/30",
        [PersonRole.CLIENT]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [PersonRole.PROVIDER]:
          "border-amber-200 bg-amber-50 text-amber-800 hover:bg-amber-100 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-300 dark:hover:bg-amber-900/30",
        [PersonRole.USER]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [PersonRole.NONE]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface PersonRoleBadgeProps
  extends VariantProps<typeof personRoleVariants> {
  type: PersonRole;
  loading?: boolean;
  className?: string;
}

export default function PersonRoleBadge({
  type,
  loading,
  size,
  className,
}: PersonRoleBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(personRoleVariants({ type, size }), className)}
      role="img"
      aria-label={`Person role: ${i18n.en.types[type]}`}
    >
      {i18n.en.types[type]}
    </span>
  );
}
