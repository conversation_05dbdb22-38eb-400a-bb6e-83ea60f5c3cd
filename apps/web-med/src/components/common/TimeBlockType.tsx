import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { TimeBlockType } from "@/api";

const i18n = {
  en: {
    types: {
      [TimeBlockType.AVAILABILITY]: "Availability",
      [TimeBlockType.TIME_OFF]: "Time Off",
      [TimeBlockType.SHIFT]: "Shift",
      [TimeBlockType.BLOCK]: "Block",
    } satisfies Record<TimeBlockType, string>,
  },
};

const timeBlockTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [TimeBlockType.AVAILABILITY]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [TimeBlockType.TIME_OFF]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [TimeBlockType.SHIFT]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [TimeBlockType.BLOCK]:
          "border-purple-200 bg-purple-50 text-purple-800 hover:bg-purple-100 dark:border-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface TimeBlockTypeBadgeProps
  extends VariantProps<typeof timeBlockTypeVariants> {
  type: TimeBlockType;
  loading?: boolean;
  className?: string;
}

export default function TimeBlockTypeBadge({
  type,
  loading,
  size,
  className,
}: TimeBlockTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(timeBlockTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Time block type: ${i18n.en.types[type]}`}
    >
      {i18n.en.types[type]}
    </span>
  );
}
