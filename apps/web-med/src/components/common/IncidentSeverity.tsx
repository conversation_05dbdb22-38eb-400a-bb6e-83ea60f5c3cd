import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";
import { AlertTriangleIcon, CircleIcon, ZapIcon } from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { IncidentSeverity } from "@/api";

const i18n = {
  en: {
    severity: {
      [IncidentSeverity.MINOR]: "Minor",
      [IncidentSeverity.MAJOR]: "Major",
      [IncidentSeverity.CRITICAL]: "Critical",
    } satisfies Record<IncidentSeverity, string>,
  },
};

const iconMap: Record<IncidentSeverity, React.ElementType> = {
  [IncidentSeverity.MINOR]: CircleIcon,
  [IncidentSeverity.MAJOR]: AlertTriangleIcon,
  [IncidentSeverity.CRITICAL]: ZapIcon,
};

const incidentSeverityVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      severity: {
        [IncidentSeverity.MINOR]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [IncidentSeverity.MAJOR]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [IncidentSeverity.CRITICAL]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface IncidentSeverityBadgeProps
  extends VariantProps<typeof incidentSeverityVariants> {
  type: IncidentSeverity;
  loading?: boolean;
  showIcon?: boolean;
  className?: string;
}

export default function IncidentSeverityBadge({
  type,
  loading,
  showIcon = true,
  size,
  className,
}: IncidentSeverityBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const Icon = iconMap[type];

  return (
    <span
      className={cn(
        incidentSeverityVariants({ severity: type, size }),
        className,
      )}
      role="img"
      aria-label={`Incident severity: ${i18n.en.severity[type]}`}
    >
      {showIcon && (
        <Icon
          className={cn(
            size === "sm"
              ? "h-3 w-3"
              : size === "lg"
                ? "h-4 w-4"
                : "h-3.5 w-3.5",
            "mr-1.5",
          )}
        />
      )}
      {i18n.en.severity[type]}
    </span>
  );
}
