import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";
import {
  AlertCircleIcon,
  BuildingIcon,
  HeartPulseIcon,
  TagIcon,
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { IncidentType } from "@/api";

const i18n = {
  en: {
    type: {
      [IncidentType.SAFETY]: "Safety",
      [IncidentType.HEALTH]: "Health",
      [IncidentType.ENVIRONMENT]: "Environment",
      [IncidentType.OTHER]: "Other",
    } satisfies Record<IncidentType, string>,
  },
};

const iconMap: Record<IncidentType, React.ElementType> = {
  [IncidentType.SAFETY]: AlertCircleIcon,
  [IncidentType.HEALTH]: HeartPulseIcon,
  [IncidentType.ENVIRONMENT]: BuildingIcon,
  [IncidentType.OTHER]: TagIcon,
};

const incidentTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [IncidentType.SAFETY]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [IncidentType.HEALTH]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [IncidentType.ENVIRONMENT]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [IncidentType.OTHER]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface IncidentTypeBadgeProps
  extends VariantProps<typeof incidentTypeVariants> {
  type: IncidentType;
  loading?: boolean;
  showIcon?: boolean;
  className?: string;
}

export default function IncidentTypeBadge({
  type,
  loading,
  showIcon = true,
  size,
  className,
}: IncidentTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const Icon = iconMap[type];

  return (
    <span
      className={cn(incidentTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Incident type: ${i18n.en.type[type]}`}
    >
      {showIcon && (
        <Icon
          className={cn(
            size === "sm"
              ? "h-3 w-3"
              : size === "lg"
                ? "h-4 w-4"
                : "h-3.5 w-3.5",
            "mr-1.5",
          )}
        />
      )}
      {i18n.en.type[type]}
    </span>
  );
}
