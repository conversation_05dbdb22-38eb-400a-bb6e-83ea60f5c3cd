# Symbol/Status Components Refactoring Strategy

## Overview

This document outlines the comprehensive strategy for refactoring 25 symbol/status components in `/apps/web-med/src/components/common/` to follow the established [`AccountStatus.tsx`](./AccountStatus.tsx) pattern while maintaining complete backward compatibility.

## Target Pattern Analysis

The [`AccountStatus.tsx`](./AccountStatus.tsx) component serves as our reference implementation with:

- ✅ CVA (Class Variance Authority) integration with status and size variants
- ✅ Skeleton loading states with size inheritance
- ✅ i18n object for internationalization
- ✅ Accessibility attributes (`role="status"` and `aria-label`)
- ✅ Light/dark mode color schemes
- ✅ Proper TypeScript interfaces extending `VariantProps`

## Component Inventory & Grouping Strategy

### Group 1: Status Components (6 components)

**Primary Focus:** Components ending in "Status" - highest priority for standardization

- `AgreementStatus.tsx` - Uses Badge variant + direct Tailwind classes
- `ApplicationStatus.tsx` - Uses switch statement with different Badge variants
- `ContractStatus.tsx` - Uses Badge variant + direct Tailwind classes
- `IncidentStatus.tsx` - Uses direct Tailwind + icons
- `InvoiceStatus.tsx` - Has size variants but uses direct Tailwind
- `OfferStatus.tsx` - TBD pattern analysis

**Current Issues:** Inconsistent color application, missing CVA, limited size variants

### Group 2: Type Components (6 components)

**Primary Focus:** Components ending in "Type" - moderate complexity

- `ContractType.tsx` - TBD pattern analysis
- `DepartmentType.tsx` - TBD pattern analysis
- `IncidentType.tsx` - TBD pattern analysis
- `PaymentType.tsx` - TBD pattern analysis
- `PayoutType.tsx` - TBD pattern analysis
- `TimeBlockType.tsx` - Uses Badge variants, clean implementation
- `ValueType.tsx` - Simple Badge variant implementation

**Current Issues:** Varying complexity, some may lack color differentiation

### Group 3: Priority/Mode Components (5 components)

**Primary Focus:** Functional classification components

- `IncidentSeverity.tsx` - Likely color-coded by severity level
- `JobPriority.tsx` - Has icons and color coding, good complexity
- `JobPostMode.tsx` - TBD pattern analysis
- `OrganizationBillingType.tsx` - TBD pattern analysis
- `ScheduleType.tsx` - TBD pattern analysis

**Current Issues:** May have custom implementations that need CVA migration

### Group 4: Role/Classification Components (4 components)

**Primary Focus:** User/entity classification

- `PersonRole.tsx` - Uses Badge variants with role-based mapping
- `TimeBlockRecurrence.tsx` - TBD pattern analysis
- `VerificationStatus.tsx` - TBD pattern analysis
- `QualificationStatus.tsx` - TBD pattern analysis

**Current Issues:** Role-based components may need special handling for permissions

### Group 5: Specialized Components (4 components)

**Primary Focus:** Unique or complex implementations

- `PayoutStatus.tsx` - Financial component, may need special colors
- `ShiftStatus.tsx` - Likely has temporal/scheduling considerations
- `SignatureStatus.tsx` - Security-focused, may need special indicators
- `PayoutType.tsx` - Move from Group 2 if complex

**Current Issues:** May have specialized requirements that need careful handling

## Standardized Patterns

### 1. CVA Variant Definitions

**Template Structure:**

```typescript
const componentVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      [existingPropName]: {
        // Map existing enum values to consistent color schemes
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);
```

### 2. Size Variant Specifications

| Size | Padding         | Text Size | Icon Size     | Skeleton Dimensions |
| ---- | --------------- | --------- | ------------- | ------------------- |
| `sm` | `px-2 py-0.5`   | `text-xs` | `h-3 w-3`     | `h-5 w-14`          |
| `md` | `px-2.5 py-0.5` | `text-xs` | `h-3.5 w-3.5` | `h-5 w-16`          |
| `lg` | `px-3 py-1`     | `text-sm` | `h-4 w-4`     | `h-7 w-20`          |

### 3. Color Scheme Standards

**Light/Dark Mode Pattern:**

```typescript
[EnumValue.EXAMPLE]:
  "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30"
```

**Color Mapping Guidelines:**

- **Success/Completed/Active:** green-\*
- **Warning/Pending/In Progress:** yellow-_ / amber-_
- **Error/Rejected/Failed:** red-\*
- **Info/Open/Default:** blue-\*
- **Neutral/Draft/Cancelled:** gray-\*
- **High Priority:** red-\*
- **Medium Priority:** yellow-_ / amber-_
- **Low Priority:** blue-_ / green-_

### 4. Loading State Implementation

**Consistent Skeleton Pattern:**

```typescript
if (loading) {
  return (
    <Skeleton
      className={cn(
        "rounded-full",
        size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
      )}
    />
  );
}
```

### 5. i18n Object Structure

**Standardized Format:**

```typescript
const i18n = {
  en: {
    [propName]: {
      [EnumValue.VALUE1]: "Display Name 1",
      [EnumValue.VALUE2]: "Display Name 2",
    },
  },
};
```

**Naming Convention:**

- Use `status` for status-type props
- Use `types` for type/classification props
- Use existing prop name when maintaining backward compatibility

### 6. Accessibility Attributes

**Required Implementation:**

```typescript
<span
  className={cn(componentVariants({ [propName]: value, size }), className)}
  role="status"
  aria-label={`${ComponentName}: ${i18n.en[propName][value]}`}
>
  {i18n.en[propName][value]}
</span>
```

### 7. TypeScript Interface Patterns

**Backward Compatible Extension:**

```typescript
export interface ComponentBadgeProps
  extends VariantProps<typeof componentVariants> {
  [existingPropName]: EnumType; // Maintain existing prop
  loading?: boolean; // Maintain existing prop
  className?: string; // Maintain existing prop
  size?: "sm" | "md" | "lg"; // NEW: Add standardized size
  showIcon?: boolean; // NEW: Add if component supports icons
}
```

## Refactoring Approach

### Phase 1: Component Analysis & CVA Integration

```mermaid
flowchart TD
    A[Analyze Current Component] --> B{Has CVA?}
    B -->|No| C[Create CVA Definition]
    B -->|Yes| D[Enhance Existing CVA]
    C --> E[Map Existing Colors to CVA]
    D --> E
    E --> F[Add Size Variants to CVA]
    F --> G[Update Component Logic]
```

**Steps:**

1. **Audit current implementation** - Document existing props, color schemes, and patterns
2. **Create CVA definition** - Map existing color schemes to CVA variants
3. **Preserve existing prop names** - Maintain original prop interface completely
4. **Add size variants** - Extend CVA with sm/md/lg sizing
5. **Update component logic** - Replace Badge/Tailwind usage with CVA

### Phase 2: Interface Extension & Feature Addition

```mermaid
flowchart TD
    A[Extend TypeScript Interface] --> B[Add VariantProps Extension]
    B --> C[Add Size Prop with Default]
    C --> D[Add Optional New Props]
    D --> E[Maintain Backward Compatibility]
    E --> F[Update Component Props Destructuring]
```

**Steps:**

1. **Extend interfaces** - Add `VariantProps<typeof componentVariants>`
2. **Add size prop** - Optional size with default "md"
3. **Add accessibility props** - Optional aria-label override
4. **Add icon props** - showIcon boolean for applicable components
5. **Update prop destructuring** - Include new props with defaults

### Phase 3: Accessibility & Loading State Standardization

```mermaid
flowchart TD
    A[Update Loading States] --> B[Size-Aware Skeleton Dimensions]
    B --> C[Add Accessibility Attributes]
    C --> D[Standardize ARIA Labels]
    D --> E[Update i18n Object]
    E --> F[Test Backward Compatibility]
```

**Steps:**

1. **Standardize skeleton loading** - Size-aware dimensions matching variants
2. **Add role="status"** - Semantic HTML for status indicators
3. **Implement aria-label** - Descriptive labels for screen readers
4. **Update i18n structure** - Ensure consistent object formatting
5. **Preserve existing behavior** - All original functionality intact

## Implementation Guidelines

### Backward Compatibility Requirements

**CRITICAL: Zero Breaking Changes**

- ✅ All existing prop names must remain functional
- ✅ All existing prop types must remain unchanged
- ✅ All existing default behaviors must be preserved
- ✅ All existing className behavior must work identically
- ✅ All existing loading prop behavior must work identically

**Hybrid Prop Strategy:**

```typescript
// Example: Component uses 'type' prop, we want to standardize on 'status'
interface ComponentProps {
  type: EnumType; // KEEP: Original prop name
  status?: EnumType; // NEW: Standardized prop name
  loading?: boolean; // KEEP: Original
  className?: string; // KEEP: Original
  size?: "sm" | "md" | "lg"; // NEW: Standardized sizing
}

// Implementation uses fallback pattern:
function Component({ type, status = type, size = "md", loading, className }) {
  // Use 'status' internally but default to 'type' for backward compatibility
}
```

### Performance Optimization Considerations

**CVA Performance:**

- CVA computations are memoized - no performance degradation
- Reduced bundle size by eliminating duplicate Tailwind classes
- Better tree-shaking with consistent variant structure

**Loading State Optimization:**

- Size-aware skeleton prevents layout shift
- Consistent dimensions across all components
- Reduced CSS-in-JS overhead with CVA

**Memory Considerations:**

- i18n objects are static - no re-creation on renders
- Variant mappings computed once at module load
- Icon component references are static

### Testing Requirements

**Automated Testing Checklist:**

```typescript
describe("ComponentBadge Refactoring", () => {
  it("maintains backward compatibility with existing props", () => {
    // Test all existing prop combinations work identically
  });

  it("supports new size variants", () => {
    // Test sm, md, lg sizing works correctly
  });

  it("provides accessible markup", () => {
    // Test role="status" and aria-label are present
  });

  it("renders size-appropriate loading states", () => {
    // Test skeleton dimensions match size variants
  });

  it("supports className prop extension", () => {
    // Test custom classes merge correctly with CVA
  });
});
```

**Manual Testing Requirements:**

- Visual regression testing across all size variants
- Dark mode color verification
- Screen reader compatibility testing
- Loading state visual consistency
- Icon alignment and sizing (where applicable)

## Quality Assurance Checklist

### Pre-Refactoring Verification

- [ ] **Current Usage Analysis** - Document all current prop usage patterns
- [ ] **Color Scheme Audit** - Catalog existing color mappings and ensure consistency
- [ ] **Dependencies Check** - Verify all imports and enum references
- [ ] **Test Coverage Review** - Identify existing tests that need updates

### Post-Refactoring Verification

- [ ] **CVA Integration** - Component uses CVA for all styling variants
- [ ] **Size Variants** - All three sizes (sm, md, lg) render correctly
- [ ] **Accessibility Compliance** - role="status" and aria-label implemented
- [ ] **Loading States** - Size-aware skeleton dimensions match variants
- [ ] **TypeScript Compliance** - Interfaces extend VariantProps correctly
- [ ] **Backward Compatibility** - All existing prop combinations work identically
- [ ] **i18n Structure** - Consistent object format and complete translations
- [ ] **Color Consistency** - Light/dark mode colors follow established patterns
- [ ] **Icon Support** - Icons render correctly with consistent sizing (if applicable)
- [ ] **Performance Validation** - No performance degradation vs original
- [ ] **Bundle Size** - No significant increase in component bundle size

### Component-Specific Validations

- [ ] **Status Components** - Semantic color mapping (green=success, red=error, etc.)
- [ ] **Type Components** - Appropriate neutral/categorical color schemes
- [ ] **Priority Components** - Clear visual hierarchy (red=high, yellow=medium, green=low)
- [ ] **Role Components** - Appropriate authority/permission visual indicators
- [ ] **Specialized Components** - Domain-specific requirements maintained

## Risk Mitigation

### Potential Risks & Mitigation Strategies

**Risk 1: Breaking Changes in Consuming Code**

- **Likelihood:** Medium
- **Impact:** High
- **Mitigation:**
  - Comprehensive backward compatibility testing
  - Gradual rollout with feature flags
  - Detailed prop mapping documentation
  - Automated prop usage scanning before deployment

**Risk 2: Visual Regression from CVA Migration**

- **Likelihood:** Medium
- **Impact:** Medium
- **Mitigation:**
  - Visual regression testing suite
  - Side-by-side comparison screenshots
  - Storybook integration for visual validation
  - Staged deployment with rollback capability

**Risk 3: Performance Degradation**

- **Likelihood:** Low
- **Impact:** Medium
- **Mitigation:**
  - Performance benchmarking before/after
  - Bundle size monitoring
  - Runtime performance profiling
  - Lighthouse score validation

**Risk 4: Accessibility Regressions**

- **Likelihood:** Low
- **Impact:** High
- **Mitigation:**
  - Automated accessibility testing (axe-core)
  - Screen reader testing protocol
  - WCAG 2.1 compliance verification
  - Accessibility expert review

**Risk 5: i18n/Localization Issues**

- **Likelihood:** Low
- **Impact:** Medium
- **Mitigation:**
  - i18n structure validation
  - Translation completeness checking
  - Locale-specific testing
  - Future i18n expansion planning

### Rollback Strategy

**Immediate Rollback Triggers:**

- Any breaking change in consuming components
- Performance degradation >10%
- Accessibility compliance failures
- Visual regressions in critical user flows

**Rollback Process:**

1. **Component-Level Rollback** - Restore individual component if issues isolated
2. **Group-Level Rollback** - Restore entire group if systematic issues found
3. **Full Rollback** - Restore all components if fundamental approach flawed
4. **Hot-Fix Path** - Patch critical issues while maintaining refactored benefits

### Deployment Strategy

**Recommended Approach:**

```mermaid
gantt
    title Refactoring Deployment Timeline
    dateFormat  X
    axisFormat %d

    section Group 1
    Status Components    :active, group1, 0, 5

    section Group 2
    Type Components      :group2, after group1, 5

    section Group 3
    Priority/Mode        :group3, after group2, 4

    section Group 4
    Role/Classification  :group4, after group3, 4

    section Group 5
    Specialized          :group5, after group4, 4
```

**Per-Group Process:**

1. **Week 1:** Refactor group components
2. **Week 2:** Internal testing and validation
3. **Week 3:** Staged deployment with monitoring
4. **Week 4:** Full deployment and group completion

This strategy ensures systematic, safe migration while maintaining full backward compatibility and adding the standardized features from the AccountStatus pattern.
