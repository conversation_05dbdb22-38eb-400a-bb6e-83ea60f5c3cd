import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { OrganizationClass } from "@/api";

const i18n = {
  en: {
    [OrganizationClass.NONPROFIT]: "Non Profit Organization",
    [OrganizationClass.GOVERNMENT]: "Government Agency",
    [OrganizationClass.PRIVATE]: "Private Company",
  },
};

const classVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        [OrganizationClass.NONPROFIT]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [OrganizationClass.GOVERNMENT]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [OrganizationClass.PRIVATE]:
          "border-indigo-200 bg-indigo-50 text-indigo-800 hover:bg-indigo-100 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300 dark:hover:bg-indigo-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: OrganizationClass.PRIVATE,
      size: "md",
    },
  },
);

export interface OrganizationClassProps
  extends VariantProps<typeof classVariants> {
  class?: OrganizationClass | null;
  className?: string;
  loading?: boolean;
}

export default function OrganizationClassType({
  class: organizationClass,
  className,
  loading,
  size,
}: OrganizationClassProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(
        classVariants({ variant: organizationClass, size }),
        className,
      )}
      role="img"
      aria-label={`Organization class: ${i18n.en[organizationClass ?? OrganizationClass.PRIVATE]}`}
    >
      {i18n.en[organizationClass ?? OrganizationClass.PRIVATE]}
    </span>
  );
}
