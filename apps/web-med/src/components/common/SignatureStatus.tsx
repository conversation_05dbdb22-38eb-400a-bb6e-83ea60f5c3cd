import type { VariantProps } from "class-variance-authority";
import type { LucideIcon } from "lucide-react";

import { cva } from "class-variance-authority";
import { CheckCircle, Clock, XCircle } from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { SignatureStatus } from "@/api";

const i18n = {
  en: {
    status: {
      [SignatureStatus.PENDING]: "Pending",
      [SignatureStatus.SIGNED]: "Signed",
      [SignatureStatus.REJECTED]: "Rejected",
    },
  },
};

const statusIcons: Record<SignatureStatus, LucideIcon> = {
  [SignatureStatus.PENDING]: Clock,
  [SignatureStatus.SIGNED]: CheckCircle,
  [SignatureStatus.REJECTED]: XCircle,
};

const signatureStatusVariants = cva(
  "inline-flex items-center gap-1 rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        [SignatureStatus.PENDING]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [SignatureStatus.SIGNED]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [SignatureStatus.REJECTED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
      },
      size: {
        sm: "gap-1 px-2 py-0.5 text-xs",
        md: "gap-1 px-2.5 py-0.5 text-xs",
        lg: "gap-1.5 px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface SignatureStatusBadgeProps
  extends VariantProps<typeof signatureStatusVariants> {
  status?: SignatureStatus;
  loading?: boolean;
  className?: string;
  showIcon?: boolean;
}

export default function SignatureStatusBadge({
  status,
  loading,
  size,
  className,
  showIcon = true,
}: SignatureStatusBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const currentStatus = status ?? SignatureStatus.PENDING;
  const Icon = statusIcons[currentStatus];
  const label = i18n.en.status[currentStatus];

  return (
    <span
      className={cn(
        signatureStatusVariants({ status: currentStatus, size }),
        className,
      )}
      role="status"
      aria-label={`Signature status: ${label}`}
    >
      {showIcon && (
        <Icon
          className={cn(
            size === "sm"
              ? "h-3 w-3"
              : size === "lg"
                ? "h-4 w-4"
                : "h-3.5 w-3.5",
          )}
        />
      )}
      <span>{label}</span>
    </span>
  );
}
