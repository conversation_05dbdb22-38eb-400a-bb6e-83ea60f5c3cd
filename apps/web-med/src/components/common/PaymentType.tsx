import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { PaymentType } from "@/api";

const i18n = {
  en: {
    types: {
      [PaymentType.HOURLY]: "Hourly",
      [PaymentType.FIXED]: "Fixed",
    } satisfies Record<PaymentType, string>,
  },
};

const paymentTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [PaymentType.HOURLY]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [PaymentType.FIXED]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface PaymentTypeBadgeProps
  extends VariantProps<typeof paymentTypeVariants> {
  type: PaymentType;
  loading?: boolean;
  className?: string;
}

export default function PaymentTypeBadge({
  type,
  loading,
  size,
  className,
}: PaymentTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(paymentTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Payment type: ${i18n.en.types[type]}`}
    >
      {i18n.en.types[type]}
    </span>
  );
}
