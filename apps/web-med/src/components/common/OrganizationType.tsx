import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { OrganizationType } from "@/api";

const i18n = {
  en: {
    types: {
      [OrganizationType.ACCOUNT]: "Account",
      [OrganizationType.CLIENT]: "Client",
      [OrganizationType.INTERNAL]: "Internal",
    },
  },
};

const typeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        [OrganizationType.ACCOUNT]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [OrganizationType.CLIENT]:
          "border-purple-200 bg-purple-50 text-purple-800 hover:bg-purple-100 dark:border-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/30",
        [OrganizationType.INTERNAL]:
          "border-orange-200 bg-orange-50 text-orange-800 hover:bg-orange-100 dark:border-orange-800 dark:bg-orange-900/20 dark:text-orange-300 dark:hover:bg-orange-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: OrganizationType.CLIENT,
      size: "md",
    },
  },
);

export interface OrganizationTypeProps
  extends VariantProps<typeof typeVariants> {
  type: OrganizationType;
  className?: string;
  loading?: boolean;
}

export default function OrganizationTypeBadge({
  type,
  className,
  loading,
  size,
}: OrganizationTypeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(typeVariants({ variant: type, size }), className)}
      role="img"
      aria-label={`Organization type: ${i18n.en.types[type]}`}
    >
      {i18n.en.types[type]}
    </span>
  );
}
