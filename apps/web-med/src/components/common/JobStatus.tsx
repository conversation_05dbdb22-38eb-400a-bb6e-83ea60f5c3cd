import type { VariantProps } from "class-variance-authority";
import type { LucideIcon } from "lucide-react";

import { cva } from "class-variance-authority";
import {
  CheckCircleIcon, // Completed, Filled
  ClockIcon, // Expired
  EyeIcon, // Published (Open/Visible)
  PencilIcon, // Draft
  XCircleIcon, // Cancelled
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { JobPostStatus } from "@/api";

const i18n = {
  en: {
    status: {
      [JobPostStatus.DRAFT]: "Draft",
      [JobPostStatus.PUBLISHED]: "Published",
      [JobPostStatus.FILLED]: "Filled",
      [JobPostStatus.COMPLETED]: "Completed",
      [JobPostStatus.CANCELLED]: "Cancelled",
      [JobPostStatus.EXPIRED]: "Expired",
    },
  },
};

// Define icons for each status
const statusIcons: Record<JobPostStatus, LucideIcon> = {
  [JobPostStatus.DRAFT]: PencilIcon,
  [JobPostStatus.PUBLISHED]: EyeIcon,
  [JobPostStatus.FILLED]: CheckCircleIcon,
  [JobPostStatus.COMPLETED]: CheckCircleIcon,
  [JobPostStatus.CANCELLED]: XCircleIcon,
  [JobPostStatus.EXPIRED]: ClockIcon,
};

const jobStatusVariants = cva(
  "inline-flex items-center gap-1.5 whitespace-nowrap rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        [JobPostStatus.DRAFT]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [JobPostStatus.PUBLISHED]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [JobPostStatus.FILLED]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [JobPostStatus.COMPLETED]:
          "border-teal-200 bg-teal-50 text-teal-800 hover:bg-teal-100 dark:border-teal-800 dark:bg-teal-900/20 dark:text-teal-300 dark:hover:bg-teal-900/30",
        [JobPostStatus.CANCELLED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [JobPostStatus.EXPIRED]:
          "border-orange-200 bg-orange-50 text-orange-800 hover:bg-orange-100 dark:border-orange-800 dark:bg-orange-900/20 dark:text-orange-300 dark:hover:bg-orange-900/30",
      },
      size: {
        sm: "gap-1 px-2 py-0.5 text-xs",
        md: "gap-1.5 px-2.5 py-0.5 text-xs",
        lg: "gap-1.5 px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface JobStatusProps extends VariantProps<typeof jobStatusVariants> {
  status?: JobPostStatus;
  loading?: boolean;
  className?: string;
  showIcon?: boolean;
}

export default function JobStatus({
  status,
  loading,
  size,
  className,
  showIcon = true,
}: JobStatusProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  if (!status) {
    return null;
  }

  const Icon = statusIcons[status];
  const label = i18n.en.status[status];

  return (
    <span
      className={cn(jobStatusVariants({ status, size }), className)}
      role="status"
      aria-label={`Job status: ${label}`}
    >
      {showIcon && (
        <Icon
          className={cn(
            size === "sm"
              ? "h-3 w-3"
              : size === "lg"
                ? "h-4 w-4"
                : "h-3.5 w-3.5",
          )}
        />
      )}
      <span>{label}</span>
    </span>
  );
}
