import type { VariantProps } from "class-variance-authority";
import type { LucideIcon } from "lucide-react";

import { cva } from "class-variance-authority";
import {
  ChevronDownIcon,
  ChevronUpIcon,
  TriangleAlertIcon,
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { JobPostPriority } from "@/api";

const i18n = {
  en: {
    priority: {
      [JobPostPriority.LOW]: "Low",
      [JobPostPriority.MEDIUM]: "Medium",
      [JobPostPriority.HIGH]: "High",
    },
  },
};

const priorityIcons: Record<JobPostPriority, LucideIcon> = {
  [JobPostPriority.LOW]: ChevronDownIcon,
  [JobPostPriority.MEDIUM]: ChevronUpIcon,
  [JobPostPriority.HIGH]: TriangleAlertIcon,
};

const jobPriorityVariants = cva(
  "inline-flex items-center gap-1.5 whitespace-nowrap rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      priority: {
        [JobPostPriority.LOW]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [JobPostPriority.MEDIUM]:
          "border-amber-200 bg-amber-50 text-amber-800 hover:bg-amber-100 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-300 dark:hover:bg-amber-900/30",
        [JobPostPriority.HIGH]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
      },
      size: {
        sm: "gap-1 px-2 py-0.5 text-xs",
        md: "gap-1.5 px-2.5 py-0.5 text-xs",
        lg: "gap-1.5 px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface JobPriorityProps
  extends VariantProps<typeof jobPriorityVariants> {
  priority?: JobPostPriority;
  loading?: boolean;
  className?: string;
  showIcon?: boolean;
}

export default function JobPriority({
  priority,
  loading,
  size,
  className,
  showIcon = true,
}: JobPriorityProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  if (!priority) {
    return null;
  }

  const Icon = priorityIcons[priority];
  const label = i18n.en.priority[priority];

  return (
    <span
      className={cn(jobPriorityVariants({ priority, size }), className)}
      role="img"
      aria-label={`Job priority: ${label}`}
    >
      {showIcon && (
        <Icon
          className={cn(
            size === "sm"
              ? "h-3 w-3"
              : size === "lg"
                ? "h-4 w-4"
                : "h-3.5 w-3.5",
          )}
        />
      )}
      <span>{label}</span>
    </span>
  );
}
