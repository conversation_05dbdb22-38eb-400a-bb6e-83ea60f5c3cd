import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { ContractStatus } from "@/api";

const i18n = {
  en: {
    status: {
      [ContractStatus.PENDING]: "Pending",
      [ContractStatus.SIGNED]: "Signed",
      [ContractStatus.REJECTED]: "Rejected",
      [ContractStatus.DRAFT]: "Draft",
      [ContractStatus.EXPIRED]: "Expired",
    },
  },
};

const contractStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        [ContractStatus.PENDING]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [ContractStatus.SIGNED]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [ContractStatus.REJECTED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [ContractStatus.DRAFT]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [ContractStatus.EXPIRED]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface ContractStatusBadgeProps
  extends VariantProps<typeof contractStatusVariants> {
  status: ContractStatus;
  loading?: boolean;
  className?: string;
}

export default function ContractStatusBadge({
  status,
  loading,
  size,
  className,
}: ContractStatusBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(contractStatusVariants({ status, size }), className)}
      role="status"
      aria-label={`Contract status: ${i18n.en.status[status]}`}
    >
      {i18n.en.status[status]}
    </span>
  );
}
