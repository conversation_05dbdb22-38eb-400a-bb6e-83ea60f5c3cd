import type { VariantProps } from "class-variance-authority";

import {
  CheckCircledIcon,
  ExclamationTriangleIcon,
  InfoCircledIcon,
  MinusCircledIcon,
  StopIcon,
} from "@radix-ui/react-icons";
import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { ProviderStatus } from "@/api";

const i18n = {
  en: {
    [ProviderStatus.PENDING]: "Incomplete",
    [ProviderStatus.ACTIVE]: "Active",
    [ProviderStatus.INACTIVE]: "Inactive",
    [ProviderStatus.SUSPENDED]: "Suspended",
    [ProviderStatus.REJECTED]: "Rejected",
  },
};

const statusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        [ProviderStatus.PENDING]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [ProviderStatus.ACTIVE]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [ProviderStatus.INACTIVE]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [ProviderStatus.SUSPENDED]:
          "border-orange-200 bg-orange-50 text-orange-800 hover:bg-orange-100 dark:border-orange-800 dark:bg-orange-900/20 dark:text-orange-300 dark:hover:bg-orange-900/30",
        [ProviderStatus.REJECTED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: ProviderStatus.ACTIVE,
      size: "md",
    },
  },
);

function getStatusIcon(status: ProviderStatus) {
  switch (status) {
    case ProviderStatus.ACTIVE:
      return CheckCircledIcon;
    case ProviderStatus.PENDING:
      return InfoCircledIcon;
    case ProviderStatus.INACTIVE:
      return MinusCircledIcon;
    case ProviderStatus.SUSPENDED:
      return StopIcon;
    case ProviderStatus.REJECTED:
      return ExclamationTriangleIcon;
    default:
      return InfoCircledIcon;
  }
}

export interface ProviderStatusBadgeProps
  extends VariantProps<typeof statusVariants> {
  status: ProviderStatus;
  className?: string;
  loading?: boolean;
  showIcon?: boolean;
}

export default function ProviderStatusBadge({
  status,
  className,
  loading,
  showIcon,
  size,
}: ProviderStatusBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const Icon = showIcon ? getStatusIcon(status) : null;

  return (
    <span
      className={cn(statusVariants({ variant: status, size }), className)}
      role="status"
      aria-label={`Provider status: ${i18n.en[status]}`}
    >
      {Icon && (
        <Icon
          className={cn("mr-1", {
            "size-3": size === "sm",
            "size-3.5": size === "md" || !size,
            "size-4": size === "lg",
          })}
        />
      )}
      {i18n.en[status]}
    </span>
  );
}
