import type { VariantProps } from "class-variance-authority";

import * as React from "react";
import { cva } from "class-variance-authority";
import { BriefcaseBusiness, CalendarCheck, Timer } from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { JobPostType } from "@/api";

const i18n = {
  en: {
    [JobPostType.PERMANENT]: "Permanent",
    [JobPostType.TEMPORARY]: "Temporary",
    [JobPostType.PER_DIEM]: "Per Diem",
  },
};

const jobTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [JobPostType.PERMANENT]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [JobPostType.TEMPORARY]:
          "border-amber-200 bg-amber-50 text-amber-800 hover:bg-amber-100 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-300 dark:hover:bg-amber-900/30",
        [JobPostType.PER_DIEM]:
          "border-purple-200 bg-purple-50 text-purple-800 hover:bg-purple-100 dark:border-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/30",
        default:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      type: "default",
      size: "md",
    },
  },
);

const typeIcons: Record<JobPostType, React.ElementType> = {
  [JobPostType.PERMANENT]: BriefcaseBusiness,
  [JobPostType.TEMPORARY]: Timer,
  [JobPostType.PER_DIEM]: CalendarCheck,
};

export interface JobTypeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof jobTypeVariants> {
  type?: JobPostType;
  loading?: boolean;
  showIcon?: boolean;
}

export default function JobType({
  className,
  type,
  size,
  loading,
  showIcon = true,
  ...props
}: JobTypeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const isValidType = type && type in i18n.en;
  const currentType = isValidType ? type : undefined;
  const text = isValidType ? i18n.en[type] : (type ?? "Unknown");
  const IconComponent = isValidType ? typeIcons[type] : null;

  return (
    <div
      className={cn(jobTypeVariants({ type: currentType, size }), className)}
      role="img"
      aria-label={`Job type: ${text}`}
      {...props}
    >
      {showIcon && IconComponent && (
        <IconComponent
          className={cn("mr-1", {
            "h-3 w-3": size === "sm",
            "h-3.5 w-3.5": size === "md" || !size,
            "h-4 w-4": size === "lg",
          })}
          strokeWidth={1.75}
        />
      )}
      {text}
    </div>
  );
}
