import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";
import { AlertTriangleIcon, CheckCircleIcon, ClockIcon } from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { QualificationStatus } from "@/api";

const i18n = {
  en: {
    status: {
      [QualificationStatus.PENDING]: "Pending",
      [QualificationStatus.APPROVED]: "Approved",
      [QualificationStatus.REJECTED]: "Rejected",
      [QualificationStatus.EXPIRED]: "Expired",
    } satisfies Record<QualificationStatus, string>,
  },
};

const iconMap: Record<QualificationStatus, React.ElementType> = {
  [QualificationStatus.PENDING]: ClockIcon,
  [QualificationStatus.APPROVED]: CheckCircleIcon,
  [QualificationStatus.REJECTED]: AlertTriangleIcon,
  [QualificationStatus.EXPIRED]: AlertTriangleIcon,
};

const qualificationStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        [QualificationStatus.PENDING]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [QualificationStatus.APPROVED]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [QualificationStatus.REJECTED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [QualificationStatus.EXPIRED]:
          "border-orange-200 bg-orange-50 text-orange-800 hover:bg-orange-100 dark:border-orange-800 dark:bg-orange-900/20 dark:text-orange-300 dark:hover:bg-orange-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface QualificationStatusBadgeProps
  extends VariantProps<typeof qualificationStatusVariants> {
  status: QualificationStatus;
  loading?: boolean;
  showIcon?: boolean;
  className?: string;
}

export default function QualificationStatusBadge({
  status,
  loading,
  showIcon = true,
  size,
  className,
}: QualificationStatusBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const Icon = iconMap[status];

  return (
    <span
      className={cn(qualificationStatusVariants({ status, size }), className)}
      role="status"
      aria-label={`Qualification status: ${i18n.en.status[status]}`}
    >
      {showIcon && (
        <Icon
          className={cn(
            size === "sm"
              ? "h-3 w-3"
              : size === "lg"
                ? "h-4 w-4"
                : "h-3.5 w-3.5",
            "mr-1.5",
          )}
        />
      )}
      {i18n.en.status[status]}
    </span>
  );
}
