import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";
import {
  CheckCircleIcon,
  CircleDotDashedIcon,
  CircleDotIcon,
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { IncidentStatus } from "@/api";

const i18n = {
  en: {
    status: {
      [IncidentStatus.OPEN]: "Open",
      [IncidentStatus.IN_PROGRESS]: "In Progress",
      [IncidentStatus.RESOLVED]: "Resolved",
    } satisfies Record<IncidentStatus, string>,
  },
};

const iconMap: Partial<Record<IncidentStatus, React.ElementType>> = {
  [IncidentStatus.OPEN]: CircleDotIcon,
  [IncidentStatus.IN_PROGRESS]: CircleDotDashedIcon,
  [IncidentStatus.RESOLVED]: CheckCircleIcon,
};

const incidentStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        [IncidentStatus.OPEN]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [IncidentStatus.IN_PROGRESS]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [IncidentStatus.RESOLVED]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface IncidentStatusBadgeProps
  extends VariantProps<typeof incidentStatusVariants> {
  type: IncidentStatus;
  loading?: boolean;
  showIcon?: boolean;
  className?: string;
}

export default function IncidentStatusBadge({
  type,
  loading,
  showIcon = true,
  size,
  className,
}: IncidentStatusBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const Icon = iconMap[type];

  return (
    <span
      className={cn(incidentStatusVariants({ status: type, size }), className)}
      role="status"
      aria-label={`Incident status: ${i18n.en.status[type]}`}
    >
      {showIcon && Icon && (
        <Icon
          className={cn(
            size === "sm"
              ? "h-3 w-3"
              : size === "lg"
                ? "h-4 w-4"
                : "h-3.5 w-3.5",
            "mr-1.5",
          )}
        />
      )}
      {i18n.en.status[type]}
    </span>
  );
}
