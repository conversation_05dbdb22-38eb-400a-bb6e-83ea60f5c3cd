import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { OrganizationBillingType } from "@/api";

const i18n = {
  en: {
    types: {
      [OrganizationBillingType.INVOICE]: "Invoice",
      [OrganizationBillingType.CHARGE]: "Charge",
      [OrganizationBillingType.NONE]: "None",
    } satisfies Record<OrganizationBillingType, string>,
  },
};

const organizationBillingTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [OrganizationBillingType.INVOICE]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [OrganizationBillingType.CHARGE]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [OrganizationBillingType.NONE]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface OrganizationBillingTypeBadgeProps
  extends VariantProps<typeof organizationBillingTypeVariants> {
  type: OrganizationBillingType;
  loading?: boolean;
  className?: string;
}

export default function OrganizationBillingTypeBadge({
  type,
  loading,
  size,
  className,
}: OrganizationBillingTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(organizationBillingTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Organization billing type: ${i18n.en.types[type]}`}
    >
      {i18n.en.types[type]}
    </span>
  );
}
