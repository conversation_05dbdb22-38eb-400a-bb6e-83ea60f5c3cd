import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";
import {
  CheckC<PERSON>cleIcon,
  ClockIcon,
  LoaderIcon,
  XCircleIcon,
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { VerificationStatus } from "@/api";

const i18n = {
  en: {
    status: {
      [VerificationStatus.PENDING]: "Pending",
      [VerificationStatus.APPROVED]: "Approved",
      [VerificationStatus.REJECTED]: "Rejected",
      [VerificationStatus.CANCELLED]: "Cancelled",
      [VerificationStatus.VERIFYING]: "Verifying",
    } satisfies Record<VerificationStatus, string>,
  },
};

const iconMap: Record<VerificationStatus, React.ElementType> = {
  [VerificationStatus.PENDING]: ClockIcon,
  [VerificationStatus.APPROVED]: CheckCircleIcon,
  [VerificationStatus.REJECTED]: XCircleIcon,
  [VerificationStatus.CANCELLED]: XCircleIcon,
  [VerificationStatus.VERIFYING]: LoaderIcon,
};

const verificationStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        [VerificationStatus.PENDING]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [VerificationStatus.APPROVED]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [VerificationStatus.REJECTED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [VerificationStatus.CANCELLED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [VerificationStatus.VERIFYING]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface VerificationStatusBadgeProps
  extends VariantProps<typeof verificationStatusVariants> {
  type: VerificationStatus;
  loading?: boolean;
  showIcon?: boolean;
  className?: string;
}

export default function VerificationStatusBadge({
  type,
  loading,
  showIcon = true,
  size,
  className,
}: VerificationStatusBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  const Icon = iconMap[type];

  return (
    <span
      className={cn(
        verificationStatusVariants({ status: type, size }),
        className,
      )}
      role="status"
      aria-label={`Verification status: ${i18n.en.status[type]}`}
    >
      {showIcon && (
        <Icon
          className={cn(
            size === "sm"
              ? "h-3 w-3"
              : size === "lg"
                ? "h-4 w-4"
                : "h-3.5 w-3.5",
            "mr-1.5",
          )}
        />
      )}
      {i18n.en.status[type]}
    </span>
  );
}
