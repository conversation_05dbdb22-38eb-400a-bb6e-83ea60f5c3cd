import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { InvoiceStatus } from "@/api";

const i18n = {
  en: {
    types: {
      [InvoiceStatus.DRAFT]: "Draft",
      [InvoiceStatus.OPEN]: "Open",
      [InvoiceStatus.PAID]: "Paid",
      [InvoiceStatus.DUE]: "Due",
      [InvoiceStatus.VOID]: "Void",
    } satisfies Record<InvoiceStatus, string>,
  },
};

const invoiceStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [InvoiceStatus.DRAFT]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        [InvoiceStatus.OPEN]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [InvoiceStatus.PAID]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [InvoiceStatus.DUE]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [InvoiceStatus.VOID]:
          "border-slate-200 bg-slate-50 text-slate-800 hover:bg-slate-100 dark:border-slate-800 dark:bg-slate-900/20 dark:text-slate-300 dark:hover:bg-slate-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface InvoiceStatusBadgeProps
  extends VariantProps<typeof invoiceStatusVariants> {
  type: InvoiceStatus;
  loading?: boolean;
  className?: string;
}

export default function InvoiceStatusBadge({
  type,
  loading,
  size,
  className,
}: InvoiceStatusBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(invoiceStatusVariants({ type, size }), className)}
      role="status"
      aria-label={`Invoice status: ${i18n.en.types[type]}`}
    >
      {i18n.en.types[type]}
    </span>
  );
}
