"use client";

import { useState } from "react";
import {
  Briefcase,
  Calendar,
  Clock,
  DollarSign,
  MapPin,
  Plus,
  X,
} from "lucide-react";

import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { Checkbox } from "@axa/ui/primitives/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@axa/ui/primitives/dialog";
import { Input } from "@axa/ui/primitives/input";
import { Label } from "@axa/ui/primitives/label";
import { ScrollArea } from "@axa/ui/primitives/scroll-area";
import { Slider } from "@axa/ui/primitives/slider";

export type JobCategory =
  | "All"
  | "Nursing"
  | "Physician"
  | "Specialist"
  | "Administrative";
export type UserType = "JobSeeker" | "Employer";
export type DayOfWeek =
  | "Monday"
  | "Tuesday"
  | "Wednesday"
  | "Thursday"
  | "Friday"
  | "Saturday"
  | "Sunday";
export type PositionType = "Full-time" | "Part-time" | "Per Diem";

export interface Job {
  id: number;
  title: string;
  company: string;
  location: string;
  category: JobCategory;
  description: string;
  postedDate: string;
  schedule: {
    days: DayOfWeek[];
    totalHours: number;
    startTime: string;
  };
  hourlyRate: number;
  positionType: PositionType;
}

interface JobBoardProps {
  jobCategories: JobCategory[];
  daysOfWeek: DayOfWeek[];
  positionTypes: PositionType[];
  allLocations: string[];
  jobs: Job[];
}

function LocationFilter({
  selectedLocations,
  onLocationChange,
  allLocations,
}: {
  selectedLocations: string[];
  onLocationChange: (locations: string[]) => void;
  allLocations: string[];
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempLocations, setTempLocations] = useState(selectedLocations);

  const handleLocationToggle = (location: string) => {
    setTempLocations((prev) =>
      prev.includes(location)
        ? prev.filter((l) => l !== location)
        : [...prev, location],
    );
  };

  const handleApply = () => {
    onLocationChange(tempLocations);
    setIsOpen(false);
  };

  const handleDelete = (location: string) => {
    onLocationChange(selectedLocations.filter((l) => l !== location));
  };

  return (
    <div>
      <h3 className="mb-2 text-lg font-semibold">Locations</h3>
      <div className="flex flex-wrap items-center gap-2">
        {selectedLocations.map((location) => (
          <Badge key={location} variant="secondary" className="text-sm">
            {location}
            <Button
              variant="ghost"
              size="sm"
              className="ml-2 size-4 p-0"
              onClick={() => handleDelete(location)}
            >
              <X className="size-3" />
              <span className="sr-only">Remove {location}</span>
            </Button>
          </Badge>
        ))}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Plus className="mr-2 size-4" />
              Add Location
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Select Locations</DialogTitle>
            </DialogHeader>
            <ScrollArea className="mt-4 h-[300px]">
              {allLocations.map((location) => (
                <div
                  key={location}
                  className="mb-2 flex items-center space-x-2"
                >
                  <Checkbox
                    id={location}
                    checked={tempLocations.includes(location)}
                    onCheckedChange={() => handleLocationToggle(location)}
                  />
                  <Label htmlFor={location}>{location}</Label>
                </div>
              ))}
            </ScrollArea>
            <Button onClick={handleApply} className="mt-4">
              Apply
            </Button>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}

export default function JobBoard({
  jobCategories,
  daysOfWeek,
  positionTypes,
  allLocations,
  jobs,
}: JobBoardProps) {
  const [selectedCategory, setSelectedCategory] = useState<JobCategory>("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentStage, setCurrentStage] = useState(1);
  const [userType, setUserType] = useState<UserType>("JobSeeker");
  const [selectedDays, setSelectedDays] = useState<DayOfWeek[]>([]);
  const [hourlyRateRange, setHourlyRateRange] = useState([0, 200]);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [selectedPositionTypes, setSelectedPositionTypes] = useState<
    PositionType[]
  >([]);

  const filteredJobs = jobs.filter(
    (job) =>
      (selectedCategory === "All" || job.category === selectedCategory) &&
      (job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.description.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (selectedDays.length === 0 ||
        selectedDays.every((day) => job.schedule.days.includes(day))) &&
      job.hourlyRate >= (hourlyRateRange[0] ?? 0) &&
      job.hourlyRate <= (hourlyRateRange[1] ?? 200) &&
      (selectedLocations.length === 0 ||
        selectedLocations.includes(job.location)) &&
      (selectedPositionTypes.length === 0 ||
        selectedPositionTypes.includes(job.positionType)),
  );

  const handleSwitchToEmployer = () => {
    setUserType("Employer");
    alert(
      "Switching to Employer View. In a full implementation, this would redirect to the employer dashboard.",
    );
  };

  const handleDayToggle = (day: DayOfWeek) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day],
    );
  };

  const handlePositionTypeToggle = (positionType: PositionType) => {
    setSelectedPositionTypes((prev) =>
      prev.includes(positionType)
        ? prev.filter((t) => t !== positionType)
        : [...prev, positionType],
    );
  };

  if (userType === "Employer") {
    return (
      <div className="container mx-auto p-4">
        <h1 className="mb-6 text-3xl font-bold">Employer Dashboard</h1>
        <p>
          Welcome to the employer view. Here you can post jobs and manage
          applications.
        </p>
        <Button onClick={() => setUserType("JobSeeker")} className="mt-4">
          Switch back to Job Seeker View
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6 space-y-4">
        <Input
          type="text"
          placeholder="Search jobs..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />

        <div>
          <h3 className="mb-2 text-lg font-semibold">Job Categories</h3>
          <div className="flex flex-wrap gap-2">
            {jobCategories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "primary" : "outline"}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        <LocationFilter
          selectedLocations={selectedLocations}
          onLocationChange={setSelectedLocations}
          allLocations={allLocations}
        />

        <div>
          <h3 className="mb-2 text-lg font-semibold">Position Type</h3>
          <div className="flex flex-wrap gap-4">
            {positionTypes.map((positionType) => (
              <div key={positionType} className="flex items-center">
                <Checkbox
                  id={positionType}
                  checked={selectedPositionTypes.includes(positionType)}
                  onCheckedChange={() => handlePositionTypeToggle(positionType)}
                />
                <Label htmlFor={positionType} className="ml-2">
                  {positionType}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="mb-2 text-lg font-semibold">Work Days</h3>
          <div className="flex flex-wrap gap-4">
            {daysOfWeek.map((day) => (
              <div key={day} className="flex items-center">
                <Checkbox
                  id={day}
                  checked={selectedDays.includes(day)}
                  onCheckedChange={() => handleDayToggle(day)}
                />
                <Label htmlFor={day} className="ml-2">
                  {day}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="mb-2 text-lg font-semibold">Hourly Rate Range</h3>
          <Slider
            min={0}
            max={200}
            step={5}
            value={hourlyRateRange}
            onValueChange={setHourlyRateRange}
            className="mb-2"
          />
          <div className="flex justify-between">
            <span>${hourlyRateRange[0]}/hr</span>
            <span>${hourlyRateRange[1]}/hr</span>
          </div>
        </div>
      </div>

      {filteredJobs.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
          {filteredJobs.map((job) => (
            <Card key={job.id}>
              <CardHeader>
                <CardTitle>{job.title}</CardTitle>
                <CardDescription>{job.company}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4 text-sm text-gray-600">{job.description}</p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <MapPin size={16} />
                    <span>{job.location}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Calendar size={16} />
                    <span>Posted on {job.postedDate}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Clock size={16} />
                    <span>
                      {job.schedule.totalHours}h/week, starts at{" "}
                      {job.schedule.startTime}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <DollarSign size={16} />
                    <span>${job.hourlyRate}/hr</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Briefcase size={16} />
                    <span>{job.positionType}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex items-center justify-between">
                <Badge variant="secondary">{job.category}</Badge>
                <Button variant="outline" onClick={() => setCurrentStage(2)}>
                  <Briefcase className="mr-2 size-4" />
                  Apply Now
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-6 text-center">
          <CardTitle className="mb-2">No Results Found</CardTitle>
          <CardDescription>
            Try adjusting your search criteria or filters to find more job
            listings.
          </CardDescription>
        </Card>
      )}
    </div>
  );
}
