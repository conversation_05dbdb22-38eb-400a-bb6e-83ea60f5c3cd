import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import { PayoutStatus, PersonRole, ProviderStatus } from "@/api";
import ListPayouts from "@/components/tables/ListPayouts";

const meta = {
  title: "Components/Tables/ListPayouts",
  component: ListPayouts,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
} satisfies Meta<typeof ListPayouts>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockProvider = {
  id: "provider-001",
  status: ProviderStatus.ACTIVE,
  person: {
    id: "person-001",
    firstName: "John",
    lastName: "Doe",
    avatar: "https://via.placeholder.com/150",
  },
} as const;

const mockPayout: RouterOutputs["billing"]["payouts"]["getMany"]["items"][number] =
  {
    id: "payout-001",
    status: PayoutStatus.PENDING,
    amount: 1500.0,
    overtimeAmount: 250.0,
    holidayAmount: 300.0,
    nightAmount: 187.5,
    paidAt: new Date(),
    providerId: mockProvider.id,
    shifts: [],
    provider: {
      ...mockProvider,
      person: {
        ...mockProvider.person,
        role: PersonRole.PROVIDER,
      },
      title: "Provider",
    },
  };

export const Loading: Story = {
  args: {
    loading: true,
    payouts: {
      items: [],
      total: 0,
    },
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    payouts: {
      items: [],
      total: 0,
    },
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    payouts: {
      items: [mockPayout],
      total: 1,
    },
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    payouts: {
      items: [
        mockPayout,
        {
          ...mockPayout,
          id: "payout-002",
          status: "PROCESSING",
          amount: 2000.0,
          overtimeAmount: 400.0,
          provider: {
            ...mockProvider,
            id: "provider-002",
            person: {
              ...mockProvider.person,
              id: "person-002",
              firstName: "Jane",
              lastName: "Smith",
              role: PersonRole.PROVIDER,
            },
            title: "Provider",
          },
        },
        {
          ...mockPayout,
          id: "payout-003",
          status: "COMPLETED",
          amount: 1750.0,
          nightAmount: 218.75,
          provider: {
            ...mockProvider,
            id: "provider-003",
            person: {
              ...mockProvider.person,
              id: "person-003",
              firstName: "Robert",
              lastName: "Johnson",
              role: PersonRole.PROVIDER,
            },
            title: "Provider",
          },
        },
        {
          ...mockPayout,
          id: "payout-004",
          status: "FAILED",
          amount: 1200.0,
          provider: {
            ...mockProvider,
            id: "provider-004",
            person: {
              ...mockProvider.person,
              id: "person-004",
              firstName: "Sarah",
              lastName: "Williams",
              role: PersonRole.PROVIDER,
            },
            title: "Provider",
          },
        },
        {
          ...mockPayout,
          id: "payout-005",
          status: "CANCELLED",
          amount: 900.0,
          holidayAmount: 180.0,
          provider: {
            ...mockProvider,
            id: "provider-005",
            person: {
              ...mockProvider.person,
              id: "person-005",
              firstName: "Michael",
              lastName: "Brown",
              role: PersonRole.PROVIDER,
            },
            title: "Provider",
          },
        },
      ],
      total: 5,
    },
  },
};
