import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import { But<PERSON> } from "@axa/ui/primitives/button";

import {
  SearchPosition,
  SelectPosition,
  SelectPositionField,
} from "@/components/selectors/SelectPosition";

const meta: Meta<typeof SelectPosition> = {
  title: "Components/Selectors/SelectPosition",
  component: SelectPosition,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    loading: {
      control: "boolean",
      description: "Override loading state",
    },
    enabled: {
      control: "boolean",
      description: "Enable/disable the selector",
    },
    organizationId: {
      control: "text",
      description: "Filter positions by organization ID",
    },
    facilityId: {
      control: "text",
      description: "Filter positions by facility ID",
    },
    departmentId: {
      control: "text",
      description: "Filter positions by department ID",
    },
    status: {
      control: "object",
      description: "Filter positions by status",
    },
    type: {
      control: "object",
      description: "Filter positions by type",
    },
    includeProvider: {
      control: "boolean",
      description: "Include provider data",
    },
    includeLocation: {
      control: "boolean",
      description: "Include location data",
    },
    includeFacility: {
      control: "boolean",
      description: "Include facility data",
    },
    includeDepartment: {
      control: "boolean",
      description: "Include department data",
    },
    defaultQuery: {
      control: "text",
      description: "Default query string",
    },
    pageSize: {
      control: "number",
      description: "Page size for pagination (default: 10)",
    },
    defaultDebounce: {
      control: "number",
      description: "Debounce delay in milliseconds",
    },
    size: {
      control: "select",
      options: ["sm", "md", "lg"],
      description: "Size of the selector",
    },
    useDialog: {
      control: "boolean",
      description: "Use dialog mode for selection",
    },
    placeholder: {
      control: "text",
      description: "Placeholder text",
    },
  },
} satisfies Meta<typeof SelectPosition>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Default: Story = {
  args: {
    placeholder: "Select a position",
    enabled: true,
    loading: false,
    size: "lg",
    includeProvider: true,
    includeLocation: true,
    includeFacility: true,
    includeDepartment: true,
    pageSize: 10,
    defaultDebounce: 500,
  },
};

// With organization filter
export const WithOrganizationFilter: Story = {
  args: {
    ...Default.args,
    organizationId: "org-123",
    placeholder: "Select a position from organization",
  },
};

// With facility filter
export const WithFacilityFilter: Story = {
  args: {
    ...Default.args,
    facilityId: "facility-456",
    placeholder: "Select a position from facility",
  },
};

// With status filter
export const WithStatusFilter: Story = {
  args: {
    ...Default.args,
    status: ["ACTIVE"],
    placeholder: "Select an active position",
  },
};

// With type filter
export const WithTypeFilter: Story = {
  args: {
    ...Default.args,
    type: ["PERMANENT"],
    placeholder: "Select a permanent position",
  },
};

// Loading state
export const Loading: Story = {
  args: {
    ...Default.args,
    loading: true,
  },
};

// Disabled state
export const Disabled: Story = {
  args: {
    ...Default.args,
    enabled: false,
  },
};

// Small size
export const SmallSize: Story = {
  args: {
    ...Default.args,
    size: "sm",
  },
};

// Medium size
export const MediumSize: Story = {
  args: {
    ...Default.args,
    size: "md",
  },
};

// Dialog mode
export const DialogMode: Story = {
  args: {
    ...Default.args,
    useDialog: true,
  },
};

// Note: Default options story removed due to complex Position type structure
// In real usage, defaultOptions would be provided from API responses

// Form field component
const FormWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      position: "",
    },
  });

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit((data) => {
          alert(`Selected position: ${JSON.stringify(data, null, 2)}`);
        })}
        className="space-y-4"
      >
        {children}
        <Button type="submit">Submit</Button>
      </form>
    </FormProvider>
  );
};

export const FormField: Story = {
  render: (args) => (
    <FormWrapper>
      <SelectPositionField
        name="position"
        label="Position"
        description="Select a position for the assignment"
        placeholder="Choose a position"
        organizationId={args.organizationId}
        facilityId={args.facilityId}
        status={args.status}
        type={args.type}
        size={args.size}
      />
    </FormWrapper>
  ),
  args: {
    ...Default.args,
  },
};

// Search component
export const SearchComponent: Story = {
  render: (args) => (
    <SearchPosition
      group="position-search"
      name="position"
      defaultValue=""
      placeholder="Search positions"
      organizationId={args.organizationId}
      facilityId={args.facilityId}
      status={args.status}
      type={args.type}
      size={args.size}
      onSelect={(position) => {
        alert(`Selected position: ${position.role}`);
      }}
    />
  ),
  args: {
    ...Default.args,
  },
};

// Multiple filters example
export const MultipleFilters: Story = {
  args: {
    ...Default.args,
    organizationId: "org-123",
    facilityId: "facility-456",
    departmentId: "dept-789",
    status: ["ACTIVE", "PENDING"],
    type: ["PERMANENT", "TEMPORARY"],
    placeholder: "Select a filtered position",
  },
};

// Custom page size
export const CustomPageSize: Story = {
  args: {
    ...Default.args,
    pageSize: 5,
    placeholder: "Select a position (5 per page)",
  },
};

// Fast debounce for testing
export const FastDebounce: Story = {
  args: {
    ...Default.args,
    defaultDebounce: 100,
    placeholder: "Select a position (fast search)",
  },
};
