import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { ValueStoreType } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    type: {
      [ValueStoreType.CONTACT]: "Contact",
      [ValueStoreType.EXPENSE]: "Expense",
      [ValueStoreType.ORDER_TYPE]: "Order Type",
      [ValueStoreType.ORDER_CATEGORY]: "Order Category",
      [ValueStoreType.LOCATION_TYPE]: "Location Type",
    } satisfies Record<ValueStoreType, string>,
  },
};

const valueStoreTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [ValueStoreType.CONTACT]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [ValueStoreType.EXPENSE]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [ValueStoreType.ORDER_TYPE]:
          "border-teal-200 bg-teal-50 text-teal-800 hover:bg-teal-100 dark:border-teal-800 dark:bg-teal-900/20 dark:text-teal-300 dark:hover:bg-teal-900/30",
        [ValueStoreType.ORDER_CATEGORY]:
          "border-cyan-200 bg-cyan-50 text-cyan-800 hover:bg-cyan-100 dark:border-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300 dark:hover:bg-cyan-900/30",
        [ValueStoreType.LOCATION_TYPE]:
          "border-indigo-200 bg-indigo-50 text-indigo-800 hover:bg-indigo-100 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300 dark:hover:bg-indigo-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface ValueStoreTypeBadgeProps
  extends VariantProps<typeof valueStoreTypeVariants> {
  type: ValueStoreType;
  loading?: boolean;
  className?: string;
}

export function ValueStoreTypeBadge({
  type,
  loading,
  size,
  className,
}: ValueStoreTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(valueStoreTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Value store type: ${i18n.en.type[type]}`}
    >
      {i18n.en.type[type]}
    </span>
  );
}
