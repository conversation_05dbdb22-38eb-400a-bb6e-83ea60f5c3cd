import type { VariantProps } from "class-variance-authority";
import type { LucideProps } from "lucide-react";
import type { FC } from "react";

import { cva } from "class-variance-authority";
import {
  ArrowBigDownDashIcon,
  ArrowBigRightIcon,
  ArrowBigUpDashIcon,
} from "lucide-react";

import type { WorkOrderPriority } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    priority: {
      HIGH: "High",
      MEDIUM: "Normal",
      LOW: "Low",
    } satisfies Record<WorkOrderPriority, string>,
  },
};

const priorityIcons = {
  HIGH: ArrowBigUpDashIcon,
  MEDIUM: ArrowBigRightIcon,
  LOW: ArrowBigDownDashIcon,
};

const orderPriorityVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      priority: {
        HIGH: "border-destructive/20 bg-destructive/10 text-destructive hover:bg-destructive/20 dark:border-destructive dark:bg-destructive/20 dark:text-destructive dark:hover:bg-destructive/30",
        MEDIUM:
          "border-muted-foreground/20 bg-muted/10 text-muted-foreground hover:bg-muted/20 dark:border-muted-foreground dark:bg-muted/20 dark:text-muted-foreground dark:hover:bg-muted/30",
        LOW: "border-muted-foreground/20 bg-muted/10 text-muted-foreground hover:bg-muted/20 dark:border-muted-foreground dark:bg-muted/20 dark:text-muted-foreground dark:hover:bg-muted/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface OrderPriorityBadgeProps
  extends VariantProps<typeof orderPriorityVariants> {
  priority: WorkOrderPriority;
  loading?: boolean;
  className?: string;
  showText?: boolean;
}

export function OrderPriorityIcon({
  priority = "HIGH",
  size = "md",
  className = "",
}: LucideProps & {
  priority: WorkOrderPriority;
  size?: "sm" | "md" | "lg";
}) {
  const Icon = priorityIcons[priority] as FC<LucideProps>;

  return (
    <Icon
      aria-label={i18n.en.priority[priority]}
      size={size === "sm" ? 16 : size === "lg" ? 24 : 20}
      className={cn(
        {
          "fill-destructive stroke-destructive": priority === "HIGH",
          "fill-muted-foreground stroke-muted-foreground":
            priority === "MEDIUM" || priority === "LOW",
        },
        className,
      )}
    />
  );
}

export function OrderPriorityLabel({
  priority = "HIGH",
  className = "",
  size = "md",
}: {
  priority: WorkOrderPriority;
  className?: string;
  size?: "sm" | "md" | "lg";
}) {
  return (
    <div className="flex w-fit items-center justify-center gap-1 font-bold">
      <OrderPriorityIcon size={size} priority={priority} />
      <span
        className={cn(
          "text-sm font-bold",
          {
            "text-destructive": priority === "HIGH",
            "text-muted-foreground":
              priority === "MEDIUM" || priority === "LOW",
          },
          className,
        )}
      >
        {i18n.en.priority[priority]}
      </span>
    </div>
  );
}

export default function OrderPriorityBadge({
  priority = "HIGH",
  loading,
  size,
  className = "",
  showText = true,
}: OrderPriorityBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(orderPriorityVariants({ priority, size }), className)}
      role="img"
      aria-label={`Order priority: ${i18n.en.priority[priority]}`}
    >
      <OrderPriorityIcon
        priority={priority}
        size={size || "md"}
        className="mr-1"
      />
      <span
        className={cn({
          "sr-only": !showText,
        })}
      >
        {i18n.en.priority[priority]}
      </span>
    </span>
  );
}
