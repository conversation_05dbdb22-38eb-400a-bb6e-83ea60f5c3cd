import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { ScheduleType } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    type: {
      [ScheduleType.ONE_TIME]: "One Time",
      [ScheduleType.SHORT_TERM]: "Short Term",
      [ScheduleType.LONG_TERM]: "Long Term",
    } satisfies Record<ScheduleType, string>,
  },
};

const scheduleTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [ScheduleType.ONE_TIME]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [ScheduleType.SHORT_TERM]:
          "border-teal-200 bg-teal-50 text-teal-800 hover:bg-teal-100 dark:border-teal-800 dark:bg-teal-900/20 dark:text-teal-300 dark:hover:bg-teal-900/30",
        [ScheduleType.LONG_TERM]:
          "border-indigo-200 bg-indigo-50 text-indigo-800 hover:bg-indigo-100 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300 dark:hover:bg-indigo-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface ScheduleTypeBadgeProps
  extends VariantProps<typeof scheduleTypeVariants> {
  type: ScheduleType;
  loading?: boolean;
  className?: string;
}

export function ScheduleTypeBadge({
  type,
  loading,
  size,
  className,
}: ScheduleTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(scheduleTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Schedule type: ${i18n.en.type[type]}`}
    >
      {i18n.en.type[type]}
    </span>
  );
}
