import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import type { StatementType } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    type: {
      PAYMENT: "Payment",
      FUNDING: "Funding",
      FEE: "Fee",
    } satisfies Record<StatementType, string>,
  },
};

const statementTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        PAYMENT:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        FUNDING:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        FEE: "border-amber-200 bg-amber-50 text-amber-800 hover:bg-amber-100 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-300 dark:hover:bg-amber-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface StatementTypeBadgeProps
  extends VariantProps<typeof statementTypeVariants> {
  type: StatementType;
  loading?: boolean;
  className?: string;
}

export default function StatementTypeBadge({
  type,
  loading,
  size,
  className,
}: StatementTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(statementTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Statement type: ${i18n.en.type[type]}`}
    >
      {i18n.en.type[type]}
    </span>
  );
}
