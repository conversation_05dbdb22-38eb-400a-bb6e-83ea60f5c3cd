import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import type { TimeSheetStatus } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    status: {
      APPROVED: "Approved",
      REJECTED: "Rejected",
      PENDING: "Pending",
      ASSIGNED: "Assigned",
    } satisfies Record<TimeSheetStatus, string>,
  },
};

const timeSheetStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        APPROVED:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        REJECTED:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        PENDING:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        ASSIGNED:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface TimeSheetStatusProps
  extends VariantProps<typeof timeSheetStatusVariants> {
  status: TimeSheetStatus;
  loading?: boolean;
  className?: string;
}

export default function TimeSheetStatusBadge({
  status = "PENDING",
  loading = false,
  size,
  className,
}: TimeSheetStatusProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(timeSheetStatusVariants({ status, size }), className)}
      role="status"
      aria-label={`TimeSheet status: ${i18n.en.status[status]}`}
    >
      {i18n.en.status[status]}
    </span>
  );
}
