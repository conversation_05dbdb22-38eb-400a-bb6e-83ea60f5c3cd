import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { PersonRole } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    role: {
      [PersonRole.ADMIN]: "Admin",
      [PersonRole.BILLING]: "Billing",
      [PersonRole.INTERNAL]: "Internal",
      [PersonRole.CLIENT]: "Client",
      [PersonRole.PROVIDER]: "Provider",
      [PersonRole.NONE]: "None",
    } satisfies Record<PersonRole, string>,
  },
};

const personRoleVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      role: {
        [PersonRole.ADMIN]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [PersonRole.BILLING]:
          "border-purple-200 bg-purple-50 text-purple-800 hover:bg-purple-100 dark:border-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/30",
        [PersonRole.INTERNAL]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [PersonRole.CLIENT]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [PersonRole.PROVIDER]:
          "border-teal-200 bg-teal-50 text-teal-800 hover:bg-teal-100 dark:border-teal-800 dark:bg-teal-900/20 dark:text-teal-300 dark:hover:bg-teal-900/30",
        [PersonRole.NONE]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
      role: PersonRole.NONE,
    },
  },
);

export interface PersonRoleBadgeProps
  extends VariantProps<typeof personRoleVariants> {
  role?: PersonRole;
  loading?: boolean;
  className?: string;
}

/**
 * PersonRole Badge Component
 *
 * Displays person role information following the AccountStatus pattern with:
 *
 * - CVA (class-variance-authority) implementation with rounded-full styling
 * - Size variants (sm/md/lg) with proper padding and text sizing
 * - Standardized loading states with size-aware skeleton dimensions
 * - Accessibility compliance with semantic role attributes
 * - TypeScript safety with VariantProps integration
 * - i18n support structure
 * - Consistent color schemes with hover states and dark mode support
 * - Role-specific color mapping for different privilege levels
 */
export default function PersonRoleBadge({
  role = PersonRole.NONE,
  loading,
  size,
  className,
}: PersonRoleBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(personRoleVariants({ role, size }), className)}
      role="img"
      aria-label={`Person role: ${i18n.en.role[role]}`}
    >
      {i18n.en.role[role]}
    </span>
  );
}
