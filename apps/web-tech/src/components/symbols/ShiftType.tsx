import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { ShiftType } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    type: {
      [ShiftType.SERVICE_WINDOW]: "Service Window",
      [ShiftType.SERVICE_DATE]: "Service Date",
      [ShiftType.SERVICE_TERM]: "Service Term",
    } satisfies Record<ShiftType, string>,
  },
};

const shiftTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [ShiftType.SERVICE_WINDOW]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [ShiftType.SERVICE_DATE]:
          "border-teal-200 bg-teal-50 text-teal-800 hover:bg-teal-100 dark:border-teal-800 dark:bg-teal-900/20 dark:text-teal-300 dark:hover:bg-teal-900/30",
        [ShiftType.SERVICE_TERM]:
          "border-indigo-200 bg-indigo-50 text-indigo-800 hover:bg-indigo-100 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300 dark:hover:bg-indigo-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface ShiftTypeBadgeProps
  extends VariantProps<typeof shiftTypeVariants> {
  type: ShiftType;
  loading?: boolean;
  className?: string;
}

export function ShiftTypeBadge({
  type,
  loading,
  size,
  className,
}: ShiftTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(shiftTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Shift type: ${i18n.en.type[type]}`}
    >
      {i18n.en.type[type]}
    </span>
  );
}
