import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import type { ShiftStatus } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    status: {
      OPEN: "Open",
      CLOSED: "Closed",
      ASSIGNED: "Assigned",
      COMPLETED: "Completed",
    } satisfies Record<ShiftStatus, string>,
  },
};

const shiftStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        OPEN: "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        CLOSED:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
        ASSIGNED:
          "border-cyan-200 bg-cyan-50 text-cyan-800 hover:bg-cyan-100 dark:border-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300 dark:hover:bg-cyan-900/30",
        COMPLETED:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface ShiftStatusProps
  extends VariantProps<typeof shiftStatusVariants> {
  status: ShiftStatus;
  loading?: boolean;
  className?: string;
}

export default function ShiftStatusBadge({
  status = "OPEN",
  loading = false,
  size,
  className,
}: ShiftStatusProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(shiftStatusVariants({ status, size }), className)}
      role="status"
      aria-label={`Shift status: ${i18n.en.status[status]}`}
    >
      {i18n.en.status[status]}
    </span>
  );
}
