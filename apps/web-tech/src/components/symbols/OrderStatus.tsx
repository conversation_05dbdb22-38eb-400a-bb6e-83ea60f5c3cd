import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { WorkOrderStatus } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    status: {
      [WorkOrderStatus.PENDING]: "Pending",
      [WorkOrderStatus.PUBLISHED]: "Reviewed",
      [WorkOrderStatus.SCHEDULED]: "Scheduled",
      [WorkOrderStatus.ASSIGNED]: "Assigned",
      [WorkOrderStatus.ACTIVE]: "In Progress",
      [WorkOrderStatus.COMPLETED]: "Completed",
      [WorkOrderStatus.APPROVED]: "Invoiced",
      [WorkOrderStatus.CANCELLED]: "Canceled",
      [WorkOrderStatus.DRAFT]: "Draft",
    } satisfies Record<WorkOrderStatus, string>,
  },
};

const orderStatusVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      status: {
        [WorkOrderStatus.PENDING]:
          "border-yellow-200 bg-yellow-50 text-yellow-800 hover:bg-yellow-100 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30",
        [WorkOrderStatus.PUBLISHED]:
          "border-fuchsia-200 bg-fuchsia-50 text-fuchsia-800 hover:bg-fuchsia-100 dark:border-fuchsia-800 dark:bg-fuchsia-900/20 dark:text-fuchsia-300 dark:hover:bg-fuchsia-900/30",
        [WorkOrderStatus.SCHEDULED]:
          "border-cyan-200 bg-cyan-50 text-cyan-800 hover:bg-cyan-100 dark:border-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300 dark:hover:bg-cyan-900/30",
        [WorkOrderStatus.ASSIGNED]:
          "border-blue-200 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/30",
        [WorkOrderStatus.ACTIVE]:
          "border-teal-200 bg-teal-50 text-teal-800 hover:bg-teal-100 dark:border-teal-800 dark:bg-teal-900/20 dark:text-teal-300 dark:hover:bg-teal-900/30",
        [WorkOrderStatus.COMPLETED]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [WorkOrderStatus.APPROVED]:
          "border-indigo-200 bg-indigo-50 text-indigo-800 hover:bg-indigo-100 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300 dark:hover:bg-indigo-900/30",
        [WorkOrderStatus.CANCELLED]:
          "border-red-200 bg-red-50 text-red-800 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30",
        [WorkOrderStatus.DRAFT]:
          "border-gray-200 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-300 dark:hover:bg-gray-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface OrderStatusProps
  extends VariantProps<typeof orderStatusVariants> {
  status: WorkOrderStatus;
  loading?: boolean;
  className?: string;
}

export default function OrderStatusBadge({
  status = WorkOrderStatus.ACTIVE,
  loading = false,
  size,
  className,
}: OrderStatusProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(orderStatusVariants({ status, size }), className)}
      role="status"
      aria-label={`Order status: ${i18n.en.status[status]}`}
    >
      {i18n.en.status[status]}
    </span>
  );
}
