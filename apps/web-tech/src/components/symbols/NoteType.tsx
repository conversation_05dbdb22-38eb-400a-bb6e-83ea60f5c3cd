import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { NoteType } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Skeleton } from "@axa/ui/primitives/skeleton";

const i18n = {
  en: {
    type: {
      [NoteType.PUBLIC]: "Public",
      [NoteType.INTERNAL]: "Internal",
    } satisfies Record<NoteType, string>,
  },
};

const noteTypeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      type: {
        [NoteType.PUBLIC]:
          "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30",
        [NoteType.INTERNAL]:
          "border-orange-200 bg-orange-50 text-orange-800 hover:bg-orange-100 dark:border-orange-800 dark:bg-orange-900/20 dark:text-orange-300 dark:hover:bg-orange-900/30",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface NoteTypeBadgeProps
  extends VariantProps<typeof noteTypeVariants> {
  type: NoteType;
  loading?: boolean;
  className?: string;
}

export function NoteTypeBadge({
  type,
  loading,
  size,
  className,
}: NoteTypeBadgeProps) {
  if (loading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" ? "h-5 w-14" : size === "lg" ? "h-7 w-20" : "h-5 w-16",
        )}
      />
    );
  }

  return (
    <span
      className={cn(noteTypeVariants({ type, size }), className)}
      role="img"
      aria-label={`Note type: ${i18n.en.type[type]}`}
    >
      {i18n.en.type[type]}
    </span>
  );
}
