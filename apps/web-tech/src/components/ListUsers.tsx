"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Image from "next/image";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import { PersonRole } from "@axa/database-tech";
import ContactEmail from "@axa/ui/common/ContactEmail";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { UserMenu } from "@/widgets/actions/user";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no users yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search users...",
    },
    headers: {
      role: "Role",
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      organization: "Organization",
    },
    filters: {
      role: "Role",
      options: {
        ALL: "All",
        [PersonRole.ADMIN]: "Admin",
        [PersonRole.BILLING]: "Billing",
        [PersonRole.INTERNAL]: "Internal",
        [PersonRole.PROVIDER]: "Provider",
        [PersonRole.CLIENT]: "Client",
        [PersonRole.NONE]: "None",
      },
    },
  },
  links: {
    users: "/app/admin/users/[id]",
  },
};

export const groupName = "user";
const filters = [
  {
    id: "role",
    label: i18n.en.filters.role,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.ALL,
      },
      {
        value: PersonRole.ADMIN,
        label: i18n.en.filters.options.ADMIN,
      },
      {
        value: PersonRole.BILLING,
        label: i18n.en.filters.options.BILLING,
      },
      {
        value: PersonRole.INTERNAL,
        label: i18n.en.filters.options.INTERNAL,
      },
      {
        value: PersonRole.PROVIDER,
        label: i18n.en.filters.options.PROVIDER,
      },
      {
        value: PersonRole.CLIENT,
        label: i18n.en.filters.options.CLIENT,
      },
      {
        value: PersonRole.NONE,
        label: i18n.en.filters.options.NONE,
      },
    ] satisfies { value: PersonRole | null; label: string }[],
  },
];

export type UserQueryResult = RouterOutputs["user"]["getMany"];
export type UserType = UserQueryResult["users"];
export type PersonType = UserType[number];

interface ListUserProps extends PropsWithChildren {
  loading?: boolean;
  users?: UserQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListUser({
  group = groupName,
  loading = false,
  users,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListUserProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!users) return undefined;
    return {
      items: users.users,
      total: users.total,
    };
  }, [users]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filters}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<PersonType>(
            [
              "id",
              "firstName",
              "lastName",
              "email",
              "phone",
              "role",
              "organization",
            ],
            {
              filename: "users_export.csv",
              label: "Export Selected Users",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
                // Add more resolvers as needed for complex fields
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "User Actions",
            render: (context: ActionContext<PersonType>) => {
              if (context.type === "row") {
                return <UserMenu variant="ghost" user={context.row} />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "avatar",
              accessorKey: "avatar",
              header: () => null,
              cell: ({ row }) => (
                <Avatar className="size-10">
                  <AvatarImage
                    asChild
                    src={row.original.avatar ?? undefined}
                    alt={`${row.original.firstName} ${row.original.lastName}`}
                  >
                    <Image
                      src={row.original.avatar ?? ""}
                      alt={`${row.original.firstName} ${row.original.lastName}`}
                      width={40}
                      height={40}
                      layout="fixed"
                    />
                  </AvatarImage>
                  <AvatarFallback>
                    {row.original.firstName.charAt(0)}
                    {row.original.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              ),
            },
            {
              id: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-full items-center gap-2">
                  <ContactName
                    className="font-semibold"
                    link={i18n.links.users.replace("[id]", row.original.id)}
                    name={`${row.original.firstName} ${row.original.lastName}`}
                  />
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "email",
              accessorKey: "email",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.email}
                />
              ),
              cell: ({ row }) => <ContactEmail email={row.getValue("email")} />,
            },
            {
              id: "phone",
              accessorKey: "phone",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.phone}
                />
              ),
              cell: ({ row }) => <ContactPhone phone={row.getValue("phone")} />,
            },
            {
              id: "role",
              accessorKey: "role",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.role}
                />
              ),
              cell: ({ row }) => (
                <Badge variant="outline" className="w-fit text-nowrap text-xs">
                  {row.original.role}
                </Badge>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.role);
              },
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) =>
                row.original.organization ? (
                  <PreviewOrganization
                    organization={row.original.organization}
                  />
                ) : null,
              filterFn: (row: any, id: string, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<PersonType, PersonType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
